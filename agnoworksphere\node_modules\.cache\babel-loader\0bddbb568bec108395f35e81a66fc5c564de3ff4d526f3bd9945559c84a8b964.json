{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\components\\\\QuickActions.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Button from '../../../components/ui/Button';\nimport Icon from '../../../components/AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuickActions = ({\n  userRole,\n  onCreateProject,\n  onInviteMembers,\n  onManageUsers\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const getActionsForRole = () => {\n    const role = userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase();\n    switch (role) {\n      case 'owner':\n        return [{\n          label: 'Create Project',\n          icon: 'FolderPlus',\n          variant: 'default',\n          action: 'create_project'\n        }, {\n          label: 'Manage Users',\n          icon: 'Users',\n          variant: 'outline',\n          action: 'manage_users'\n        }, {\n          label: 'View Analytics',\n          icon: 'BarChart3',\n          variant: 'outline',\n          action: 'view_analytics'\n        }, {\n          label: 'Organization Settings',\n          icon: 'Settings',\n          variant: 'outline',\n          action: 'org_settings'\n        }, {\n          label: 'Invite Members',\n          icon: 'UserPlus',\n          variant: 'outline',\n          action: 'invite_members'\n        }, {\n          label: 'Export Data',\n          icon: 'Download',\n          variant: 'ghost',\n          action: 'export_data'\n        }];\n      case 'admin':\n        return [{\n          label: 'Create Project',\n          icon: 'FolderPlus',\n          variant: 'default',\n          action: 'create_project'\n        }, {\n          label: 'Manage Team',\n          icon: 'Users',\n          variant: 'outline',\n          action: 'manage_team'\n        }, {\n          label: 'View Reports',\n          icon: 'FileText',\n          variant: 'outline',\n          action: 'view_reports'\n        }, {\n          label: 'Project Settings',\n          icon: 'Settings',\n          variant: 'outline',\n          action: 'project_settings'\n        }, {\n          label: 'Invite Members',\n          icon: 'UserPlus',\n          variant: 'outline',\n          action: 'invite_members'\n        }, {\n          label: 'Bulk Actions',\n          icon: 'List',\n          variant: 'ghost',\n          action: 'bulk_actions'\n        }];\n      case 'member':\n        return [{\n          label: 'Create Task',\n          icon: 'Plus',\n          variant: 'default',\n          action: 'create_task'\n        }, {\n          label: 'My Tasks',\n          icon: 'CheckSquare',\n          variant: 'outline',\n          action: 'my_tasks'\n        }, {\n          label: 'Join Project',\n          icon: 'UserPlus',\n          variant: 'outline',\n          action: 'join_project'\n        }, {\n          label: 'Time Tracking',\n          icon: 'Clock',\n          variant: 'outline',\n          action: 'time_tracking'\n        }, {\n          label: 'Upload Files',\n          icon: 'Upload',\n          variant: 'outline',\n          action: 'upload_files'\n        }, {\n          label: 'Calendar View',\n          icon: 'Calendar',\n          variant: 'ghost',\n          action: 'calendar_view'\n        }];\n      case 'viewer':\n        return [{\n          label: 'View Projects',\n          icon: 'Eye',\n          variant: 'default',\n          action: 'view_projects'\n        }, {\n          label: 'Download Reports',\n          icon: 'Download',\n          variant: 'outline',\n          action: 'download_reports'\n        }, {\n          label: 'Export Data',\n          icon: 'FileText',\n          variant: 'outline',\n          action: 'export_data'\n        }, {\n          label: 'Print Dashboard',\n          icon: 'Printer',\n          variant: 'outline',\n          action: 'print_dashboard'\n        }, {\n          label: 'Share Link',\n          icon: 'Share',\n          variant: 'ghost',\n          action: 'share_link'\n        }, {\n          label: 'Subscribe Updates',\n          icon: 'Bell',\n          variant: 'ghost',\n          action: 'subscribe_updates'\n        }];\n      default:\n        return [];\n    }\n  };\n  const handleAction = actionType => {\n    console.log(`Executing action: ${actionType} for role: ${userRole}`);\n    switch (actionType) {\n      case 'create_project':\n        if (onCreateProject) {\n          onCreateProject();\n        }\n        break;\n      case 'manage_users':\n      case 'manage_team':\n        if (onManageUsers) {\n          onManageUsers();\n        } else {\n          navigate('/team-members');\n        }\n        break;\n      case 'invite_members':\n        if (onInviteMembers) {\n          onInviteMembers();\n        }\n        break;\n      case 'org_settings':\n        navigate('/organization-settings');\n        break;\n      case 'view_analytics':\n      case 'view_reports':\n        navigate('/project-overview-analytics');\n        break;\n      case 'my_tasks':\n        navigate('/kanban-board');\n        break;\n      case 'view_projects':\n        navigate('/project-management');\n        break;\n      case 'project_settings':\n        navigate('/project-management');\n        break;\n      default:\n        console.log(`Action ${actionType} not implemented yet`);\n    }\n  };\n  const actions = getActionsForRole();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg border border-border p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-text-primary\",\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-primary rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 108,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-text-secondary font-medium\",\n          children: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 107,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\",\n      children: actions.map((action, index) => /*#__PURE__*/_jsxDEV(Button, {\n        variant: action.variant,\n        onClick: () => handleAction(action.action),\n        iconName: action.icon,\n        iconPosition: \"left\",\n        className: \"justify-start h-auto py-3 px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-start\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: action.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 117,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 p-4 bg-muted rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Lightbulb\",\n          size: 16,\n          className: \"text-accent mt-0.5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-text-primary mb-1\",\n            children: [userRole, \" Tips\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-text-secondary\",\n            children: [(userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase()) === 'owner' && \"You have full access to all features. Consider setting up automated reports and user permissions.\", (userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase()) === 'admin' && \"You can manage projects and team members. Use bulk actions to save time on repetitive tasks.\", (userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase()) === 'member' && \"Focus on your assigned tasks and collaborate with team members. Use time tracking for better productivity.\", (userRole === null || userRole === void 0 ? void 0 : userRole.toLowerCase()) === 'viewer' && \"You have read-only access. Export data and reports to share insights with stakeholders.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickActions, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = QuickActions;\nexport default QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");", "map": {"version": 3, "names": ["React", "useNavigate", "<PERSON><PERSON>", "Icon", "jsxDEV", "_jsxDEV", "QuickActions", "userRole", "onCreateProject", "onInviteMembers", "onManageUsers", "_s", "navigate", "getActionsForRole", "role", "toLowerCase", "label", "icon", "variant", "action", "handleAction", "actionType", "console", "log", "actions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "onClick", "iconName", "iconPosition", "name", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/role-based-dashboard/components/QuickActions.jsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Button from '../../../components/ui/Button';\nimport Icon from '../../../components/AppIcon';\n\nconst QuickActions = ({ userRole, onCreateProject, onInviteMembers, onManageUsers }) => {\n  const navigate = useNavigate();\n  const getActionsForRole = () => {\n    const role = userRole?.toLowerCase();\n    switch (role) {\n      case 'owner':\n        return [\n          { label: 'Create Project', icon: 'FolderPlus', variant: 'default', action: 'create_project' },\n          { label: 'Manage Users', icon: 'Users', variant: 'outline', action: 'manage_users' },\n          { label: 'View Analytics', icon: 'BarChart3', variant: 'outline', action: 'view_analytics' },\n          { label: 'Organization Settings', icon: 'Settings', variant: 'outline', action: 'org_settings' },\n          { label: 'Invite Members', icon: 'UserPlus', variant: 'outline', action: 'invite_members' },\n          { label: 'Export Data', icon: 'Download', variant: 'ghost', action: 'export_data' }\n        ];\n\n      case 'admin':\n        return [\n          { label: 'Create Project', icon: 'FolderPlus', variant: 'default', action: 'create_project' },\n          { label: 'Manage Team', icon: 'Users', variant: 'outline', action: 'manage_team' },\n          { label: 'View Reports', icon: 'FileText', variant: 'outline', action: 'view_reports' },\n          { label: 'Project Settings', icon: 'Settings', variant: 'outline', action: 'project_settings' },\n          { label: 'Invite Members', icon: 'UserPlus', variant: 'outline', action: 'invite_members' },\n          { label: 'Bulk Actions', icon: 'List', variant: 'ghost', action: 'bulk_actions' }\n        ];\n\n      case 'member':\n        return [\n          { label: 'Create Task', icon: 'Plus', variant: 'default', action: 'create_task' },\n          { label: 'My Tasks', icon: 'CheckSquare', variant: 'outline', action: 'my_tasks' },\n          { label: 'Join Project', icon: 'UserPlus', variant: 'outline', action: 'join_project' },\n          { label: 'Time Tracking', icon: 'Clock', variant: 'outline', action: 'time_tracking' },\n          { label: 'Upload Files', icon: 'Upload', variant: 'outline', action: 'upload_files' },\n          { label: 'Calendar View', icon: 'Calendar', variant: 'ghost', action: 'calendar_view' }\n        ];\n\n      case 'viewer':\n        return [\n          { label: 'View Projects', icon: 'Eye', variant: 'default', action: 'view_projects' },\n          { label: 'Download Reports', icon: 'Download', variant: 'outline', action: 'download_reports' },\n          { label: 'Export Data', icon: 'FileText', variant: 'outline', action: 'export_data' },\n          { label: 'Print Dashboard', icon: 'Printer', variant: 'outline', action: 'print_dashboard' },\n          { label: 'Share Link', icon: 'Share', variant: 'ghost', action: 'share_link' },\n          { label: 'Subscribe Updates', icon: 'Bell', variant: 'ghost', action: 'subscribe_updates' }\n        ];\n\n      default:\n        return [];\n    }\n  };\n\n  const handleAction = (actionType) => {\n    console.log(`Executing action: ${actionType} for role: ${userRole}`);\n\n    switch (actionType) {\n      case 'create_project':\n        if (onCreateProject) {\n          onCreateProject();\n        }\n        break;\n      case 'manage_users':\n      case 'manage_team':\n        if (onManageUsers) {\n          onManageUsers();\n        } else {\n          navigate('/team-members');\n        }\n        break;\n      case 'invite_members':\n        if (onInviteMembers) {\n          onInviteMembers();\n        }\n        break;\n      case 'org_settings':\n        navigate('/organization-settings');\n        break;\n      case 'view_analytics':\n      case 'view_reports':\n        navigate('/project-overview-analytics');\n        break;\n      case 'my_tasks':\n        navigate('/kanban-board');\n        break;\n      case 'view_projects':\n        navigate('/project-management');\n        break;\n      case 'project_settings':\n        navigate('/project-management');\n        break;\n      default:\n        console.log(`Action ${actionType} not implemented yet`);\n    }\n  };\n\n  const actions = getActionsForRole();\n\n  return (\n    <div className=\"bg-white rounded-lg border border-border p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-text-primary\">\n          Quick Actions\n        </h3>\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n          <span className=\"text-xs text-text-secondary font-medium\">\n            {userRole}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\">\n        {actions.map((action, index) => (\n          <Button\n            key={index}\n            variant={action.variant}\n            onClick={() => handleAction(action.action)}\n            iconName={action.icon}\n            iconPosition=\"left\"\n            className=\"justify-start h-auto py-3 px-4\"\n          >\n            <div className=\"flex flex-col items-start\">\n              <span className=\"font-medium\">{action.label}</span>\n            </div>\n          </Button>\n        ))}\n      </div>\n\n      {/* Role-specific tips */}\n      <div className=\"mt-6 p-4 bg-muted rounded-lg\">\n        <div className=\"flex items-start gap-3\">\n          <Icon name=\"Lightbulb\" size={16} className=\"text-accent mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-text-primary mb-1\">\n              {userRole} Tips\n            </h4>\n            <p className=\"text-xs text-text-secondary\">\n              {userRole?.toLowerCase() === 'owner' && \"You have full access to all features. Consider setting up automated reports and user permissions.\"}\n              {userRole?.toLowerCase() === 'admin' && \"You can manage projects and team members. Use bulk actions to save time on repetitive tasks.\"}\n              {userRole?.toLowerCase() === 'member' && \"Focus on your assigned tasks and collaborate with team members. Use time tracking for better productivity.\"}\n              {userRole?.toLowerCase() === 'viewer' && \"You have read-only access. Export data and reports to share insights with stakeholders.\"}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuickActions;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,IAAI,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,eAAe;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAMC,IAAI,GAAGP,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,WAAW,CAAC,CAAC;IACpC,QAAQD,IAAI;MACV,KAAK,OAAO;QACV,OAAO,CACL;UAAEE,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,YAAY;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC7F;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACpF;UAAEH,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC5F;UAAEH,KAAK,EAAE,uBAAuB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EAChG;UAAEH,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC3F;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAc,CAAC,CACpF;MAEH,KAAK,OAAO;QACV,OAAO,CACL;UAAEH,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,YAAY;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC7F;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAc,CAAC,EAClF;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACvF;UAAEH,KAAK,EAAE,kBAAkB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAmB,CAAC,EAC/F;UAAEH,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC3F;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAe,CAAC,CAClF;MAEH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAc,CAAC,EACjF;UAAEH,KAAK,EAAE,UAAU;UAAEC,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAW,CAAC,EAClF;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACvF;UAAEH,KAAK,EAAE,eAAe;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAgB,CAAC,EACtF;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,QAAQ;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACrF;UAAEH,KAAK,EAAE,eAAe;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAgB,CAAC,CACxF;MAEH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEH,KAAK,EAAE,eAAe;UAAEC,IAAI,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAgB,CAAC,EACpF;UAAEH,KAAK,EAAE,kBAAkB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAmB,CAAC,EAC/F;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAc,CAAC,EACrF;UAAEH,KAAK,EAAE,iBAAiB;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAkB,CAAC,EAC5F;UAAEH,KAAK,EAAE,YAAY;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAa,CAAC,EAC9E;UAAEH,KAAK,EAAE,mBAAmB;UAAEC,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAoB,CAAC,CAC5F;MAEH;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,UAAU,IAAK;IACnCC,OAAO,CAACC,GAAG,CAAC,qBAAqBF,UAAU,cAAcd,QAAQ,EAAE,CAAC;IAEpE,QAAQc,UAAU;MAChB,KAAK,gBAAgB;QACnB,IAAIb,eAAe,EAAE;UACnBA,eAAe,CAAC,CAAC;QACnB;QACA;MACF,KAAK,cAAc;MACnB,KAAK,aAAa;QAChB,IAAIE,aAAa,EAAE;UACjBA,aAAa,CAAC,CAAC;QACjB,CAAC,MAAM;UACLE,QAAQ,CAAC,eAAe,CAAC;QAC3B;QACA;MACF,KAAK,gBAAgB;QACnB,IAAIH,eAAe,EAAE;UACnBA,eAAe,CAAC,CAAC;QACnB;QACA;MACF,KAAK,cAAc;QACjBG,QAAQ,CAAC,wBAAwB,CAAC;QAClC;MACF,KAAK,gBAAgB;MACrB,KAAK,cAAc;QACjBA,QAAQ,CAAC,6BAA6B,CAAC;QACvC;MACF,KAAK,UAAU;QACbA,QAAQ,CAAC,eAAe,CAAC;QACzB;MACF,KAAK,eAAe;QAClBA,QAAQ,CAAC,qBAAqB,CAAC;QAC/B;MACF,KAAK,kBAAkB;QACrBA,QAAQ,CAAC,qBAAqB,CAAC;QAC/B;MACF;QACEU,OAAO,CAACC,GAAG,CAAC,UAAUF,UAAU,sBAAsB,CAAC;IAC3D;EACF,CAAC;EAED,MAAMG,OAAO,GAAGX,iBAAiB,CAAC,CAAC;EAEnC,oBACER,OAAA;IAAKoB,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3DrB,OAAA;MAAKoB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDrB,OAAA;QAAIoB,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAExD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLzB,OAAA;QAAKoB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCrB,OAAA;UAAKoB,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDzB,OAAA;UAAMoB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACtDnB;QAAQ;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENzB,OAAA;MAAKoB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEF,OAAO,CAACO,GAAG,CAAC,CAACZ,MAAM,EAAEa,KAAK,kBACzB3B,OAAA,CAACH,MAAM;QAELgB,OAAO,EAAEC,MAAM,CAACD,OAAQ;QACxBe,OAAO,EAAEA,CAAA,KAAMb,YAAY,CAACD,MAAM,CAACA,MAAM,CAAE;QAC3Ce,QAAQ,EAAEf,MAAM,CAACF,IAAK;QACtBkB,YAAY,EAAC,MAAM;QACnBV,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAE1CrB,OAAA;UAAKoB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCrB,OAAA;YAAMoB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEP,MAAM,CAACH;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC,GATDE,KAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNzB,OAAA;MAAKoB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CrB,OAAA;QAAKoB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCrB,OAAA,CAACF,IAAI;UAACiC,IAAI,EAAC,WAAW;UAACC,IAAI,EAAE,EAAG;UAACZ,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClEzB,OAAA;UAAAqB,QAAA,gBACErB,OAAA;YAAIoB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,GACvDnB,QAAQ,EAAC,OACZ;UAAA;YAAAoB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLzB,OAAA;YAAGoB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACvC,CAAAnB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,WAAW,CAAC,CAAC,MAAK,OAAO,IAAI,mGAAmG,EAC1I,CAAAR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,WAAW,CAAC,CAAC,MAAK,OAAO,IAAI,8FAA8F,EACrI,CAAAR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,WAAW,CAAC,CAAC,MAAK,QAAQ,IAAI,4GAA4G,EACpJ,CAAAR,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEQ,WAAW,CAAC,CAAC,MAAK,QAAQ,IAAI,yFAAyF;UAAA;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnB,EAAA,CAjJIL,YAAY;EAAA,QACCL,WAAW;AAAA;AAAAqC,EAAA,GADxBhC,YAAY;AAmJlB,eAAeA,YAAY;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}