{"ast": null, "code": "import { addLeadingZeros } from \"../addLeadingZeros.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nexport const lightFormatters = {\n  // Year\n  y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    const signedYear = date.getFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === \"yy\" ? year % 100 : year, token.length);\n  },\n  // Month\n  M(date, token) {\n    const month = date.getMonth();\n    return token === \"M\" ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n  // Day of the month\n  d(date, token) {\n    return addLeadingZeros(date.getDate(), token.length);\n  },\n  // AM or PM\n  a(date, token) {\n    const dayPeriodEnumValue = date.getHours() / 12 >= 1 ? \"pm\" : \"am\";\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return dayPeriodEnumValue.toUpperCase();\n      case \"aaa\":\n        return dayPeriodEnumValue;\n      case \"aaaaa\":\n        return dayPeriodEnumValue[0];\n      case \"aaaa\":\n      default:\n        return dayPeriodEnumValue === \"am\" ? \"a.m.\" : \"p.m.\";\n    }\n  },\n  // Hour [1-12]\n  h(date, token) {\n    return addLeadingZeros(date.getHours() % 12 || 12, token.length);\n  },\n  // Hour [0-23]\n  H(date, token) {\n    return addLeadingZeros(date.getHours(), token.length);\n  },\n  // Minute\n  m(date, token) {\n    return addLeadingZeros(date.getMinutes(), token.length);\n  },\n  // Second\n  s(date, token) {\n    return addLeadingZeros(date.getSeconds(), token.length);\n  },\n  // Fraction of second\n  S(date, token) {\n    const numberOfDigits = token.length;\n    const milliseconds = date.getMilliseconds();\n    const fractionalSeconds = Math.trunc(milliseconds * Math.pow(10, numberOfDigits - 3));\n    return addLeadingZeros(fractionalSeconds, token.length);\n  }\n};", "map": {"version": 3, "names": ["addLeadingZeros", "lightFormatters", "y", "date", "token", "signedYear", "getFullYear", "year", "length", "M", "month", "getMonth", "String", "d", "getDate", "a", "dayPeriodEnumValue", "getHours", "toUpperCase", "h", "H", "m", "getMinutes", "s", "getSeconds", "S", "numberOfDigits", "milliseconds", "getMilliseconds", "fractionalSeconds", "Math", "trunc", "pow"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/node_modules/date-fns/_lib/format/lightFormatters.js"], "sourcesContent": ["import { addLeadingZeros } from \"../addLeadingZeros.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nexport const lightFormatters = {\n  // Year\n  y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    const signedYear = date.getFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === \"yy\" ? year % 100 : year, token.length);\n  },\n\n  // Month\n  M(date, token) {\n    const month = date.getMonth();\n    return token === \"M\" ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n\n  // Day of the month\n  d(date, token) {\n    return addLeadingZeros(date.getDate(), token.length);\n  },\n\n  // AM or PM\n  a(date, token) {\n    const dayPeriodEnumValue = date.getHours() / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return dayPeriodEnumValue.toUpperCase();\n      case \"aaa\":\n        return dayPeriodEnumValue;\n      case \"aaaaa\":\n        return dayPeriodEnumValue[0];\n      case \"aaaa\":\n      default:\n        return dayPeriodEnumValue === \"am\" ? \"a.m.\" : \"p.m.\";\n    }\n  },\n\n  // Hour [1-12]\n  h(date, token) {\n    return addLeadingZeros(date.getHours() % 12 || 12, token.length);\n  },\n\n  // Hour [0-23]\n  H(date, token) {\n    return addLeadingZeros(date.getHours(), token.length);\n  },\n\n  // Minute\n  m(date, token) {\n    return addLeadingZeros(date.getMinutes(), token.length);\n  },\n\n  // Second\n  s(date, token) {\n    return addLeadingZeros(date.getSeconds(), token.length);\n  },\n\n  // Fraction of second\n  S(date, token) {\n    const numberOfDigits = token.length;\n    const milliseconds = date.getMilliseconds();\n    const fractionalSeconds = Math.trunc(\n      milliseconds * Math.pow(10, numberOfDigits - 3),\n    );\n    return addLeadingZeros(fractionalSeconds, token.length);\n  },\n};\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,uBAAuB;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,eAAe,GAAG;EAC7B;EACAC,CAACA,CAACC,IAAI,EAAEC,KAAK,EAAE;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,MAAMC,UAAU,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IACrC;IACA,MAAMC,IAAI,GAAGF,UAAU,GAAG,CAAC,GAAGA,UAAU,GAAG,CAAC,GAAGA,UAAU;IACzD,OAAOL,eAAe,CAACI,KAAK,KAAK,IAAI,GAAGG,IAAI,GAAG,GAAG,GAAGA,IAAI,EAAEH,KAAK,CAACI,MAAM,CAAC;EAC1E,CAAC;EAED;EACAC,CAACA,CAACN,IAAI,EAAEC,KAAK,EAAE;IACb,MAAMM,KAAK,GAAGP,IAAI,CAACQ,QAAQ,CAAC,CAAC;IAC7B,OAAOP,KAAK,KAAK,GAAG,GAAGQ,MAAM,CAACF,KAAK,GAAG,CAAC,CAAC,GAAGV,eAAe,CAACU,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EAC1E,CAAC;EAED;EACAG,CAACA,CAACV,IAAI,EAAEC,KAAK,EAAE;IACb,OAAOJ,eAAe,CAACG,IAAI,CAACW,OAAO,CAAC,CAAC,EAAEV,KAAK,CAACI,MAAM,CAAC;EACtD,CAAC;EAED;EACAO,CAACA,CAACZ,IAAI,EAAEC,KAAK,EAAE;IACb,MAAMY,kBAAkB,GAAGb,IAAI,CAACc,QAAQ,CAAC,CAAC,GAAG,EAAE,IAAI,CAAC,GAAG,IAAI,GAAG,IAAI;IAElE,QAAQb,KAAK;MACX,KAAK,GAAG;MACR,KAAK,IAAI;QACP,OAAOY,kBAAkB,CAACE,WAAW,CAAC,CAAC;MACzC,KAAK,KAAK;QACR,OAAOF,kBAAkB;MAC3B,KAAK,OAAO;QACV,OAAOA,kBAAkB,CAAC,CAAC,CAAC;MAC9B,KAAK,MAAM;MACX;QACE,OAAOA,kBAAkB,KAAK,IAAI,GAAG,MAAM,GAAG,MAAM;IACxD;EACF,CAAC;EAED;EACAG,CAACA,CAAChB,IAAI,EAAEC,KAAK,EAAE;IACb,OAAOJ,eAAe,CAACG,IAAI,CAACc,QAAQ,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAEb,KAAK,CAACI,MAAM,CAAC;EAClE,CAAC;EAED;EACAY,CAACA,CAACjB,IAAI,EAAEC,KAAK,EAAE;IACb,OAAOJ,eAAe,CAACG,IAAI,CAACc,QAAQ,CAAC,CAAC,EAAEb,KAAK,CAACI,MAAM,CAAC;EACvD,CAAC;EAED;EACAa,CAACA,CAAClB,IAAI,EAAEC,KAAK,EAAE;IACb,OAAOJ,eAAe,CAACG,IAAI,CAACmB,UAAU,CAAC,CAAC,EAAElB,KAAK,CAACI,MAAM,CAAC;EACzD,CAAC;EAED;EACAe,CAACA,CAACpB,IAAI,EAAEC,KAAK,EAAE;IACb,OAAOJ,eAAe,CAACG,IAAI,CAACqB,UAAU,CAAC,CAAC,EAAEpB,KAAK,CAACI,MAAM,CAAC;EACzD,CAAC;EAED;EACAiB,CAACA,CAACtB,IAAI,EAAEC,KAAK,EAAE;IACb,MAAMsB,cAAc,GAAGtB,KAAK,CAACI,MAAM;IACnC,MAAMmB,YAAY,GAAGxB,IAAI,CAACyB,eAAe,CAAC,CAAC;IAC3C,MAAMC,iBAAiB,GAAGC,IAAI,CAACC,KAAK,CAClCJ,YAAY,GAAGG,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEN,cAAc,GAAG,CAAC,CAChD,CAAC;IACD,OAAO1B,eAAe,CAAC6B,iBAAiB,EAAEzB,KAAK,CAACI,MAAM,CAAC;EACzD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}