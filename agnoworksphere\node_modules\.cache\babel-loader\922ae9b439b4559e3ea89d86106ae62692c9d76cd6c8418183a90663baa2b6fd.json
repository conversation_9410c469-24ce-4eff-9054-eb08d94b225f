{"ast": null, "code": "import { Parser } from \"../Parser.js\";\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns\n// | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n// |----------|-------|----|-------|-------|-------|\n// | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n// | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n// | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n// | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n// | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\nexport class YearParser extends Parser {\n  priority = 130;\n  incompatibleTokens = [\"Y\", \"R\", \"u\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n  parse(dateString, token, match) {\n    const valueCallback = year => ({\n      year,\n      isTwoDigitYear: token === \"yy\"\n    });\n    switch (token) {\n      case \"y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"yo\":\n        return mapValue(match.ordinalNumber(dateString, {\n          unit: \"year\"\n        }), valueCallback);\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n  set(date, flags, value) {\n    const currentYear = date.getFullYear();\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(value.year, currentYear);\n      date.setFullYear(normalizedTwoDigitYear, 0, 1);\n      date.setHours(0, 0, 0, 0);\n      return date;\n    }\n    const year = !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "mapValue", "normalizeTwoDigitYear", "parseNDigits", "<PERSON><PERSON><PERSON><PERSON>", "priority", "incompatibleTokens", "parse", "dateString", "token", "match", "valueCallback", "year", "isTwoDigitYear", "ordinalNumber", "unit", "length", "validate", "_date", "value", "set", "date", "flags", "currentYear", "getFullYear", "normalizedTwoDigitYear", "setFullYear", "setHours", "era"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/node_modules/date-fns/parse/_lib/parsers/YearParser.js"], "sourcesContent": ["import { Parser } from \"../Parser.js\";\n\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns\n// | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n// |----------|-------|----|-------|-------|-------|\n// | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n// | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n// | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n// | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n// | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\nexport class YearParser extends Parser {\n  priority = 130;\n  incompatibleTokens = [\"Y\", \"R\", \"u\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n\n  parse(dateString, token, match) {\n    const valueCallback = (year) => ({\n      year,\n      isTwoDigitYear: token === \"yy\",\n    });\n\n    switch (token) {\n      case \"y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"yo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"year\",\n          }),\n          valueCallback,\n        );\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n\n  set(date, flags, value) {\n    const currentYear = date.getFullYear();\n\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(\n        value.year,\n        currentYear,\n      );\n      date.setFullYear(normalizedTwoDigitYear, 0, 1);\n      date.setHours(0, 0, 0, 0);\n      return date;\n    }\n\n    const year =\n      !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n}\n"], "mappings": "AAAA,SAASA,MAAM,QAAQ,cAAc;AAErC,SAASC,QAAQ,EAAEC,qBAAqB,EAAEC,YAAY,QAAQ,aAAa;;AAE3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,SAASJ,MAAM,CAAC;EACrCK,QAAQ,GAAG,GAAG;EACdC,kBAAkB,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAEvEC,KAAKA,CAACC,UAAU,EAAEC,KAAK,EAAEC,KAAK,EAAE;IAC9B,MAAMC,aAAa,GAAIC,IAAI,KAAM;MAC/BA,IAAI;MACJC,cAAc,EAAEJ,KAAK,KAAK;IAC5B,CAAC,CAAC;IAEF,QAAQA,KAAK;MACX,KAAK,GAAG;QACN,OAAOR,QAAQ,CAACE,YAAY,CAAC,CAAC,EAAEK,UAAU,CAAC,EAAEG,aAAa,CAAC;MAC7D,KAAK,IAAI;QACP,OAAOV,QAAQ,CACbS,KAAK,CAACI,aAAa,CAACN,UAAU,EAAE;UAC9BO,IAAI,EAAE;QACR,CAAC,CAAC,EACFJ,aACF,CAAC;MACH;QACE,OAAOV,QAAQ,CAACE,YAAY,CAACM,KAAK,CAACO,MAAM,EAAER,UAAU,CAAC,EAAEG,aAAa,CAAC;IAC1E;EACF;EAEAM,QAAQA,CAACC,KAAK,EAAEC,KAAK,EAAE;IACrB,OAAOA,KAAK,CAACN,cAAc,IAAIM,KAAK,CAACP,IAAI,GAAG,CAAC;EAC/C;EAEAQ,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEH,KAAK,EAAE;IACtB,MAAMI,WAAW,GAAGF,IAAI,CAACG,WAAW,CAAC,CAAC;IAEtC,IAAIL,KAAK,CAACN,cAAc,EAAE;MACxB,MAAMY,sBAAsB,GAAGvB,qBAAqB,CAClDiB,KAAK,CAACP,IAAI,EACVW,WACF,CAAC;MACDF,IAAI,CAACK,WAAW,CAACD,sBAAsB,EAAE,CAAC,EAAE,CAAC,CAAC;MAC9CJ,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;MACzB,OAAON,IAAI;IACb;IAEA,MAAMT,IAAI,GACR,EAAE,KAAK,IAAIU,KAAK,CAAC,IAAIA,KAAK,CAACM,GAAG,KAAK,CAAC,GAAGT,KAAK,CAACP,IAAI,GAAG,CAAC,GAAGO,KAAK,CAACP,IAAI;IACpES,IAAI,CAACK,WAAW,CAACd,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IAC5BS,IAAI,CAACM,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACzB,OAAON,IAAI;EACb;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}