{"ast": null, "code": "import { BEGIN_DRAG, DROP, <PERSON>ND_DRAG, HOVER, PUBLISH_DRAG_SOURCE } from '../actions/dragDrop/index.js';\nimport { ADD_SOURCE, ADD_TARGET, REMOVE_SOURCE, REMOVE_TARGET } from '../actions/registry.js';\nimport { ALL, NONE } from '../utils/dirtiness.js';\nimport { areArraysEqual } from '../utils/equality.js';\nimport { xor } from '../utils/js_utils.js';\nexport function reduce(\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\n_state = NONE, action) {\n  switch (action.type) {\n    case HOVER:\n      break;\n    case ADD_SOURCE:\n    case ADD_TARGET:\n    case REMOVE_TARGET:\n    case REMOVE_SOURCE:\n      return NONE;\n    case BEGIN_DRAG:\n    case PUBLISH_DRAG_SOURCE:\n    case END_DRAG:\n    case DROP:\n    default:\n      return ALL;\n  }\n  const {\n    targetIds = [],\n    prevTargetIds = []\n  } = action.payload;\n  const result = xor(targetIds, prevTargetIds);\n  const didChange = result.length > 0 || !areArraysEqual(targetIds, prevTargetIds);\n  if (!didChange) {\n    return NONE;\n  }\n  // Check the target ids at the innermost position. If they are valid, add them\n  // to the result\n  const prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1];\n  const innermostTargetId = targetIds[targetIds.length - 1];\n  if (prevInnermostTargetId !== innermostTargetId) {\n    if (prevInnermostTargetId) {\n      result.push(prevInnermostTargetId);\n    }\n    if (innermostTargetId) {\n      result.push(innermostTargetId);\n    }\n  }\n  return result;\n}", "map": {"version": 3, "names": ["BEGIN_DRAG", "DROP", "END_DRAG", "HOVER", "PUBLISH_DRAG_SOURCE", "ADD_SOURCE", "ADD_TARGET", "REMOVE_SOURCE", "REMOVE_TARGET", "ALL", "NONE", "areArraysEqual", "xor", "reduce", "_state", "action", "type", "targetIds", "prevTargetIds", "payload", "result", "<PERSON><PERSON><PERSON><PERSON>", "length", "prevInnermostTargetId", "innermostTargetId", "push"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\dnd-core\\src\\reducers\\dirtyHandlerIds.ts"], "sourcesContent": ["import {\n\tB<PERSON>IN_DRAG,\n\tDROP,\n\t<PERSON><PERSON>_DRAG,\n\tHOVER,\n\tPUBLISH_DRAG_SOURCE,\n} from '../actions/dragDrop/index.js'\nimport {\n\tADD_SOURCE,\n\tADD_TARGET,\n\tREMOVE_SOURCE,\n\tREMOVE_TARGET,\n} from '../actions/registry.js'\nimport type { Action } from '../interfaces.js'\nimport { ALL, NONE } from '../utils/dirtiness.js'\nimport { areArraysEqual } from '../utils/equality.js'\nimport { xor } from '../utils/js_utils.js'\n\nexport type State = string[]\n\nexport interface DirtyHandlerIdPayload {\n\ttargetIds: string[]\n\tprevTargetIds: string[]\n}\n\nexport function reduce(\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\t_state: State = NONE,\n\taction: Action<DirtyHandlerIdPayload>,\n): State {\n\tswitch (action.type) {\n\t\tcase HOVER:\n\t\t\tbreak\n\t\tcase ADD_SOURCE:\n\t\tcase ADD_TARGET:\n\t\tcase REMOVE_TARGET:\n\t\tcase REMOVE_SOURCE:\n\t\t\treturn NONE\n\t\tcase BEGIN_DRAG:\n\t\tcase PUBLISH_DRAG_SOURCE:\n\t\tcase END_DRAG:\n\t\tcase DROP:\n\t\tdefault:\n\t\t\treturn ALL\n\t}\n\n\tconst { targetIds = [], prevTargetIds = [] } = action.payload\n\tconst result = xor(targetIds, prevTargetIds)\n\tconst didChange =\n\t\tresult.length > 0 || !areArraysEqual(targetIds, prevTargetIds)\n\n\tif (!didChange) {\n\t\treturn NONE\n\t}\n\n\t// Check the target ids at the innermost position. If they are valid, add them\n\t// to the result\n\tconst prevInnermostTargetId = prevTargetIds[prevTargetIds.length - 1]\n\tconst innermostTargetId = targetIds[targetIds.length - 1]\n\tif (prevInnermostTargetId !== innermostTargetId) {\n\t\tif (prevInnermostTargetId) {\n\t\t\tresult.push(prevInnermostTargetId)\n\t\t}\n\t\tif (innermostTargetId) {\n\t\t\tresult.push(innermostTargetId)\n\t\t}\n\t}\n\n\treturn result\n}\n"], "mappings": "AAAA,SACCA,UAAU,EACVC,IAAI,EACJC,QAAQ,EACRC,KAAK,EACLC,mBAAmB,QACb,8BAA8B;AACrC,SACCC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,aAAa,QACP,wBAAwB;AAE/B,SAASC,GAAG,EAAEC,IAAI,QAAQ,uBAAuB;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,GAAG,QAAQ,sBAAsB;AAS1C,OAAO,SAASC,MAAMA;AACrB;AACAC,MAAa,GAAGJ,IAAI,EACpBK,MAAqC,EAC7B;EACR,QAAQA,MAAM,CAACC,IAAI;IAClB,KAAKb,KAAK;MACT;IACD,KAAKE,UAAU;IACf,KAAKC,UAAU;IACf,KAAKE,aAAa;IAClB,KAAKD,aAAa;MACjB,OAAOG,IAAI;IACZ,KAAKV,UAAU;IACf,KAAKI,mBAAmB;IACxB,KAAKF,QAAQ;IACb,KAAKD,IAAI;IACT;MACC,OAAOQ,GAAG;;EAGZ,MAAM;IAAEQ,SAAS,GAAG,EAAE;IAAEC,aAAa,GAAG;EAAE,CAAE,GAAGH,MAAM,CAACI,OAAO;EAC7D,MAAMC,MAAM,GAAGR,GAAG,CAACK,SAAS,EAAEC,aAAa,CAAC;EAC5C,MAAMG,SAAS,GACdD,MAAM,CAACE,MAAM,GAAG,CAAC,IAAI,CAACX,cAAc,CAACM,SAAS,EAAEC,aAAa,CAAC;EAE/D,IAAI,CAACG,SAAS,EAAE;IACf,OAAOX,IAAI;;EAGZ;EACA;EACA,MAAMa,qBAAqB,GAAGL,aAAa,CAACA,aAAa,CAACI,MAAM,GAAG,CAAC,CAAC;EACrE,MAAME,iBAAiB,GAAGP,SAAS,CAACA,SAAS,CAACK,MAAM,GAAG,CAAC,CAAC;EACzD,IAAIC,qBAAqB,KAAKC,iBAAiB,EAAE;IAChD,IAAID,qBAAqB,EAAE;MAC1BH,MAAM,CAACK,IAAI,CAACF,qBAAqB,CAAC;;IAEnC,IAAIC,iBAAiB,EAAE;MACtBJ,MAAM,CAACK,IAAI,CAACD,iBAAiB,CAAC;;;EAIhC,OAAOJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}