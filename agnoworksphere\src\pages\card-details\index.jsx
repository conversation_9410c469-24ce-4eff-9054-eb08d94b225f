// ... imports
import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import Header from '../../components/ui/Header';
import CardHeader from './components/CardHeader';
import CardDescription from './components/CardDescription';
import MemberAssignment from './components/MemberAssignment';
import DueDatePicker from './components/DueDatePicker';
import LabelManager from './components/LabelManager';
import ChecklistManager from './components/ChecklistManager';
import ActivityTimeline from './components/ActivityTimeline';

const CardDetails = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const cardId = searchParams.get('id');

  const currentUserRole = 'Admin';
  const canEdit = ['Member', 'Admin', 'Owner'].includes(currentUserRole);
  const canDelete = ['Admin', 'Owner'].includes(currentUserRole);
  const canComment = ['Member', 'Admin', 'Owner'].includes(currentUserRole);

  const [cardData, setCardData] = useState({
    id: cardId || '1',
    title: 'Implement User Authentication System',
    description: `Design and implement a comprehensive user authentication system...`,
    columnTitle: 'In Progress',
    assignedMembers: [1, 2, 3],
    dueDate: new Date('2025-01-30T23:59:59'),
    labels: [1, 5, 6],
    checklist: [/* checklist items */],
    completed: false,
    createdAt: '2025-01-25T09:00:00',
    updatedAt: '2025-01-27T15:30:00'
  });

  const handleTitleChange = (newTitle) => {
    setCardData(prev => ({ ...prev, title: newTitle, updatedAt: new Date().toISOString() }));
  };

  const handleDescriptionChange = (newDescription) => {
    setCardData(prev => ({ ...prev, description: newDescription, updatedAt: new Date().toISOString() }));
  };

  const handleMembersChange = (newMembers) => {
    setCardData(prev => ({ ...prev, assignedMembers: newMembers, updatedAt: new Date().toISOString() }));
  };

  const handleDueDateChange = (newDueDate) => {
    setCardData(prev => ({ ...prev, dueDate: newDueDate, updatedAt: new Date().toISOString() }));
  };

  const handleLabelsChange = (newLabels) => {
    setCardData(prev => ({ ...prev, labels: newLabels, updatedAt: new Date().toISOString() }));
  };

  const handleChecklistChange = (newChecklist) => {
    setCardData(prev => ({ ...prev, checklist: newChecklist, updatedAt: new Date().toISOString() }));
  };

  const handleAddComment = (comment) => {
    console.log('New comment added:', comment);
  };

  const handleClose = () => navigate('/kanban-board');

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this card?')) {
      console.log('Card deleted:', cardData.id);
      navigate('/kanban-board');
    }
  };

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  if (!cardData) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-lg font-medium text-text-primary mb-2">Card not found</div>
            <div className="text-text-secondary mb-4">The requested card could not be found.</div>
            <button onClick={handleClose} className="text-primary hover:underline">
              Return to Board
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />

      {/* Modal Overlay */}
      <div className="fixed inset-0 bg-black/50 z-1000 pt-16">
        <div className="flex items-start justify-center min-h-full p-4 overflow-y-auto">
          {/* Modal Content */}
          <div className="w-full max-w-4xl bg-surface rounded-lg shadow-focused my-8 max-h-screen overflow-y-auto">
            {/* Card Header */}
            <CardHeader
              card={cardData}
              onTitleChange={handleTitleChange}
              onClose={handleClose}
              onDelete={handleDelete}
              canEdit={canEdit}
              canDelete={canDelete}
            />

            {/* Main Content */}
            <div className="flex flex-col lg:flex-row">
              <div className="flex-1 lg:w-3/5 p-6 space-y-8">
                <CardDescription card={cardData} onDescriptionChange={handleDescriptionChange} canEdit={canEdit} />
                <ChecklistManager card={cardData} onChecklistChange={handleChecklistChange} canEdit={canEdit} />
                <ActivityTimeline card={cardData} onAddComment={handleAddComment} canComment={canComment} />
              </div>

              <div className="lg:w-2/5 p-6 bg-muted/30 border-l border-border space-y-6">
                <MemberAssignment card={cardData} onMembersChange={handleMembersChange} canEdit={canEdit} />
                <DueDatePicker card={cardData} onDueDateChange={handleDueDateChange} canEdit={canEdit} />
                <LabelManager card={cardData} onLabelsChange={handleLabelsChange} canEdit={canEdit} />

                <div className="space-y-4 pt-4 border-t border-border">
                  <h4 className="font-medium text-text-primary">Card Information</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Created:</span>
                      <span className="text-text-primary">{new Date(cardData.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Last updated:</span>
                      <span className="text-text-primary">{new Date(cardData.updatedAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-text-secondary">Card ID:</span>
                      <span className="text-text-primary font-mono text-xs">#{cardData.id}</span>
                    </div>
                  </div>
                </div>

                {(canEdit || canDelete) && (
                  <div className="space-y-2 pt-4 border-t border-border">
                    <h4 className="font-medium text-text-primary text-sm">Actions</h4>
                    <div className="space-y-2">
                      {canEdit && <button className="w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md">Archive Card</button>}
                      {canEdit && <button className="w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md">Copy Card</button>}
                      {canEdit && <button className="w-full text-left px-3 py-2 text-sm hover:bg-muted rounded-md">Move Card</button>}
                      {canDelete && <button onClick={handleDelete} className="w-full text-left px-3 py-2 text-sm text-destructive hover:bg-destructive/10 rounded-md">Delete Card</button>}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardDetails;
