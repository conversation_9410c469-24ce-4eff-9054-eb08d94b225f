import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Input from '../../../components/ui/Input';
import Select from '../../../components/ui/Select';

const AddCardModal = ({ isOpen, onClose, onSave, columnId, members }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    priority: 'medium',
    assignedTo: [],
    dueDate: '',
    labels: []
  });

  const [errors, setErrors] = useState({});

  const priorityOptions = [
    { value: 'low', label: 'Low Priority' },
    { value: 'medium', label: 'Medium Priority' },
    { value: 'high', label: 'High Priority' }
  ];

  const memberOptions = members.map(member => ({
    value: member.id,
    label: member.name,
    description: member.role
  }));

  const labelOptions = [
    { value: 'bug', label: 'Bug', color: '#ef4444' },
    { value: 'feature', label: 'Feature', color: '#3b82f6' },
    { value: 'improvement', label: 'Improvement', color: '#10b981' },
    { value: 'documentation', label: 'Documentation', color: '#f59e0b' },
    { value: 'testing', label: 'Testing', color: '#8b5cf6' }
  ].map(label => ({
    value: label.value,
    label: label.label
  }));

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: null }));
    }
  };

  const handleAISuggestion = () => {
    if (!formData.title.trim()) {
      alert("Please enter a card title first.");
      return;
    }

    const mockAI = {
      description: `This card is about: ${formData.title}. Please implement the feature, test it thoroughly, and document it.`,
      labels: ['feature', 'testing'],
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000) // 7 days from now
        .toISOString()
        .split('T')[0]
    };

    setFormData(prev => ({
      ...prev,
      description: mockAI.description,
      labels: mockAI.labels,
      dueDate: mockAI.dueDate
    }));
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Card title is required';
    }

    if (formData.title.length > 100) {
      newErrors.title = 'Title must be less than 100 characters';
    }

    if (formData.description.length > 500) {
      newErrors.description = 'Description must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const newCard = {
      id: Date.now().toString(),
      columnId,
      title: formData.title.trim(),
      description: formData.description.trim(),
      priority: formData.priority,
      assignedTo: formData.assignedTo,
      dueDate: formData.dueDate || null,
      labels: formData.labels.map(labelValue => {
        const labelData = [
          { value: 'bug', label: 'Bug', color: '#ef4444' },
          { value: 'feature', label: 'Feature', color: '#3b82f6' },
          { value: 'improvement', label: 'Improvement', color: '#10b981' },
          { value: 'documentation', label: 'Documentation', color: '#f59e0b' },
          { value: 'testing', label: 'Testing', color: '#8b5cf6' }
        ].find(l => l.value === labelValue);
        return {
          id: labelValue,
          name: labelData.label,
          color: labelData.color
        };
      }),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      checklist: [],
      comments: [],
      attachments: []
    };

    onSave(newCard);
    handleClose();
  };

  const handleClose = () => {
    setFormData({
      title: '',
      description: '',
      priority: 'medium',
      assignedTo: [],
      dueDate: '',
      labels: []
    });
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={handleClose}
      />

      {/* Modal */}
      <div className="relative bg-surface rounded-lg shadow-focused w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-lg font-semibold text-text-primary">Add New Card</h2>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
          >
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <Input
            label="Card Title"
            type="text"
            placeholder="Enter card title..."
            value={formData.title}
            onChange={(e) => handleInputChange('title', e.target.value)}
            error={errors.title}
            required
          />

          {/* ✨ AI Suggestion Button */}
          <div className="flex justify-end -mt-2 mb-2">
            <button
              type="button"
              onClick={handleAISuggestion}
              className="text-sm text-primary underline hover:no-underline"
            >
              ✨ Generate with AI
            </button>
          </div>

          <div>
            <label className="block text-sm font-medium text-text-primary mb-2">
              Description
            </label>
            <textarea
              placeholder="Enter card description..."
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              className="w-full px-3 py-2 border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-primary resize-none"
              rows={3}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-destructive">{errors.description}</p>
            )}
          </div>

          <Select
            label="Priority"
            options={priorityOptions}
            value={formData.priority}
            onChange={(value) => handleInputChange('priority', value)}
          />

          <Select
            label="Assign Members"
            options={memberOptions}
            value={formData.assignedTo}
            onChange={(value) => handleInputChange('assignedTo', value)}
            multiple
            searchable
            placeholder="Select team members..."
          />

          <Input
            label="Due Date"
            type="date"
            value={formData.dueDate}
            onChange={(e) => handleInputChange('dueDate', e.target.value)}
          />

          <Select
            label="Labels"
            options={labelOptions}
            value={formData.labels}
            onChange={(value) => handleInputChange('labels', value)}
            multiple
            placeholder="Select labels..."
          />

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="default"
            >
              Create Card
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddCardModal;
