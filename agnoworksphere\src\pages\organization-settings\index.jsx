import React, { useState } from 'react';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Icon from '../../components/AppIcon';
import GeneralSettings from './components/GeneralSettings';
import SecuritySettings from './components/SecuritySettings';
import IntegrationSettings from './components/IntegrationSettings';
import MemberManagement from './components/MemberManagement';
import BillingSettings from './components/BillingSettings';

const OrganizationSettings = () => {
  const [activeTab, setActiveTab] = useState('general');

  const tabs = [
    {
      id: 'general',
      label: 'General',
      icon: 'Settings',
      component: GeneralSettings
    },
    {
      id: 'security',
      label: 'Security',
      icon: 'Shield',
      component: SecuritySettings
    },
    {
      id: 'integrations',
      label: 'Integrations',
      icon: 'Puzzle',
      component: IntegrationSettings
    },
    {
      id: 'members',
      label: 'Members',
      icon: 'Users',
      component: MemberManagement
    },
    {
      id: 'billing',
      label: 'Billing',
      icon: 'CreditCard',
      component: BillingSettings
    }
  ];

  const ActiveComponent = tabs.find(tab => tab.id === activeTab)?.component;

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-16">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <Breadcrumb />
          
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-12 h-12 bg-primary rounded-lg flex items-center justify-center">
                <Icon name="Building2" size={24} color="white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">Organization Settings</h1>
                <p className="text-muted-foreground">
                  Manage your organization's configuration, security, and preferences
                </p>
              </div>
            </div>
          </div>

          {/* Settings Layout */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-card rounded-lg border border-border p-4 sticky top-24">
                <nav className="space-y-1">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-smooth ${
                        activeTab === tab.id
                          ? 'bg-primary text-primary-foreground'
                          : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                      }`}
                    >
                      <Icon name={tab.icon} size={18} />
                      <span className="font-medium">{tab.label}</span>
                    </button>
                  ))}
                </nav>

                {/* Organization Info */}
                <div className="mt-6 pt-6 border-t border-border">
                  <div className="text-xs text-muted-foreground mb-2">CURRENT ORGANIZATION</div>
                  <div className="flex items-center space-x-2">
                    <div className="w-8 h-8 bg-secondary rounded flex items-center justify-center text-xs font-medium text-secondary-foreground">
                      AW
                    </div>
                    <div>
                      <div className="text-sm font-medium text-foreground">Agno WorkSphere</div>
                      <div className="text-xs text-muted-foreground">Professional Plan</div>
                    </div>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Members</span>
                    <span className="text-foreground font-medium">47/100</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Projects</span>
                    <span className="text-foreground font-medium">23</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span className="text-muted-foreground">Storage</span>
                    <span className="text-foreground font-medium">15.7 GB</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="bg-card rounded-lg border border-border">
                {/* Tab Header */}
                <div className="border-b border-border p-6">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-muted rounded-lg flex items-center justify-center">
                      <Icon 
                        name={tabs.find(tab => tab.id === activeTab)?.icon || 'Settings'} 
                        size={20} 
                      />
                    </div>
                    <div>
                      <h2 className="text-xl font-semibold text-foreground">
                        {tabs.find(tab => tab.id === activeTab)?.label} Settings
                      </h2>
                      <p className="text-sm text-muted-foreground">
                        {activeTab === 'general' && 'Configure basic organization information and preferences'}
                        {activeTab === 'security' && 'Manage security policies and authentication settings'}
                        {activeTab === 'integrations' && 'Connect with external services and manage API access'}
                        {activeTab === 'members' && 'Manage team members and invitation settings'}
                        {activeTab === 'billing' && 'View subscription details and manage billing information'}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Tab Content */}
                <div className="p-6">
                  {ActiveComponent && <ActiveComponent />}
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default OrganizationSettings;