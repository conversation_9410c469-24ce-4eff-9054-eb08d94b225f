import React, { useState } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';

const ChecklistManager = ({ card, onChecklistChange, canEdit }) => {
  const [newItemText, setNewItemText] = useState('');
  const [isAddingItem, setIsAddingItem] = useState(false);
  const [editingItemId, setEditingItemId] = useState(null);
  const [editingText, setEditingText] = useState('');

  const checklist = card.checklist || [];
  const completedCount = checklist.filter(item => item.completed).length;
  const progressPercentage = checklist.length > 0 ? (completedCount / checklist.length) * 100 : 0;

  const handleAddItem = () => {
    if (newItemText.trim()) {
      const newItem = {
        id: Date.now(),
        text: newItemText.trim(),
        completed: false,
        createdAt: new Date().toISOString()
      };
      onChecklistChange([...checklist, newItem]);
      setNewItemText('');
      setIsAddingItem(false);
    }
  };

  const handleToggleItem = (itemId) => {
    const updatedChecklist = checklist.map(item =>
      item.id === itemId ? { ...item, completed: !item.completed } : item
    );
    onChecklistChange(updatedChecklist);
  };

  const handleDeleteItem = (itemId) => {
    const updatedChecklist = checklist.filter(item => item.id !== itemId);
    onChecklistChange(updatedChecklist);
  };

  const handleEditItem = (itemId, newText) => {
    if (newText.trim()) {
      const updatedChecklist = checklist.map(item =>
        item.id === itemId ? { ...item, text: newText.trim() } : item
      );
      onChecklistChange(updatedChecklist);
    }
    setEditingItemId(null);
    setEditingText('');
  };

  const startEditing = (item) => {
    setEditingItemId(item.id);
    setEditingText(item.text);
  };

  const cancelEditing = () => {
    setEditingItemId(null);
    setEditingText('');
  };

  const handleKeyPress = (e, action, ...args) => {
    if (e.key === 'Enter') {
      action(...args);
    } else if (e.key === 'Escape') {
      if (action === handleAddItem) {
        setIsAddingItem(false);
        setNewItemText('');
      } else {
        cancelEditing();
      }
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Icon name="CheckSquare" size={16} className="text-text-secondary" />
          <h4 className="font-medium text-text-primary">Checklist</h4>
          {checklist.length > 0 && (
            <span className="text-sm text-text-secondary">
              {completedCount}/{checklist.length}
            </span>
          )}
        </div>
        {canEdit && !isAddingItem && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsAddingItem(true)}
          >
            <Icon name="Plus" size={16} />
          </Button>
        )}
      </div>

      {checklist.length > 0 && (
        <div className="space-y-2">
          <div className="w-full bg-muted rounded-full h-2">
            <div
              className="bg-success h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
          <div className="text-xs text-text-secondary">
            {Math.round(progressPercentage)}% complete
          </div>
        </div>
      )}

      <div className="space-y-2">
        {checklist.map((item) => (
          <div key={item.id} className="flex items-start space-x-3 p-2 rounded-md hover:bg-muted/50 transition-micro">
            <button
              onClick={() => canEdit && handleToggleItem(item.id)}
              className={`mt-0.5 w-4 h-4 rounded border-2 flex items-center justify-center transition-micro ${
                item.completed
                  ? 'bg-success border-success text-white' :'border-border hover:border-primary'
              } ${!canEdit ? 'cursor-default' : ''}`}
              disabled={!canEdit}
            >
              {item.completed && <Icon name="Check" size={12} />}
            </button>
            
            <div className="flex-1 min-w-0">
              {editingItemId === item.id ? (
                <div className="space-y-2">
                  <input
                    type="text"
                    value={editingText}
                    onChange={(e) => setEditingText(e.target.value)}
                    onKeyDown={(e) => handleKeyPress(e, handleEditItem, item.id, editingText)}
                    className="w-full p-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
                    autoFocus
                  />
                  <div className="flex items-center space-x-2">
                    <Button size="sm" onClick={() => handleEditItem(item.id, editingText)}>
                      Save
                    </Button>
                    <Button variant="ghost" size="sm" onClick={cancelEditing}>
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <div
                  className={`text-sm cursor-pointer ${
                    item.completed ? 'line-through text-text-secondary' : 'text-text-primary'
                  } ${canEdit ? 'hover:bg-muted rounded px-2 py-1 -mx-2 -my-1' : ''}`}
                  onClick={() => canEdit && startEditing(item)}
                >
                  {item.text}
                </div>
              )}
            </div>
            
            {canEdit && editingItemId !== item.id && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => handleDeleteItem(item.id)}
                className="opacity-0 group-hover:opacity-100 text-text-secondary hover:text-destructive"
              >
                <Icon name="Trash2" size={14} />
              </Button>
            )}
          </div>
        ))}

        {isAddingItem && (
          <div className="space-y-2 p-2 border border-border rounded-md">
            <input
              type="text"
              value={newItemText}
              onChange={(e) => setNewItemText(e.target.value)}
              onKeyDown={(e) => handleKeyPress(e, handleAddItem)}
              placeholder="Add an item..."
              className="w-full p-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
              autoFocus
            />
            <div className="flex items-center space-x-2">
              <Button size="sm" onClick={handleAddItem}>
                Add
              </Button>
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={() => {
                  setIsAddingItem(false);
                  setNewItemText('');
                }}
              >
                Cancel
              </Button>
            </div>
          </div>
        )}

        {checklist.length === 0 && !isAddingItem && (
          <div className="text-text-secondary text-sm italic">No checklist items</div>
        )}
      </div>
    </div>
  );
};

export default ChecklistManager;