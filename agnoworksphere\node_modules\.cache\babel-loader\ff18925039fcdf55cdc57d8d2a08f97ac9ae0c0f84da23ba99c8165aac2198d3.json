{"ast": null, "code": "import { monthsInYear } from \"./constants.js\";\n\n/**\n * @name yearsToMonths\n * @category Conversion Helpers\n * @summary Convert years to months.\n *\n * @description\n * Convert a number of years to a full number of months.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in months\n *\n * @example\n * // Convert 2 years into months\n * const result = yearsToMonths(2)\n * //=> 24\n */\nexport function yearsToMonths(years) {\n  return Math.trunc(years * monthsInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToMonths;", "map": {"version": 3, "names": ["monthsInYear", "yearsToMonths", "years", "Math", "trunc"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/node_modules/date-fns/yearsToMonths.js"], "sourcesContent": ["import { monthsInYear } from \"./constants.js\";\n\n/**\n * @name yearsToMonths\n * @category Conversion Helpers\n * @summary Convert years to months.\n *\n * @description\n * Convert a number of years to a full number of months.\n *\n * @param years - The number of years to be converted\n *\n * @returns The number of years converted in months\n *\n * @example\n * // Convert 2 years into months\n * const result = yearsToMonths(2)\n * //=> 24\n */\nexport function yearsToMonths(years) {\n  return Math.trunc(years * monthsInYear);\n}\n\n// Fallback for modularized imports:\nexport default yearsToMonths;\n"], "mappings": "AAAA,SAASA,YAAY,QAAQ,gBAAgB;;AAE7C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,OAAOC,IAAI,CAACC,KAAK,CAACF,KAAK,GAAGF,YAAY,CAAC;AACzC;;AAEA;AACA,eAAeC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}