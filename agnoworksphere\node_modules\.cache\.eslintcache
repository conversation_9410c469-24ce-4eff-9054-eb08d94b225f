[{"C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx": "1", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx": "2", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx": "3", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx": "4", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx": "5", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx": "6", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx": "7", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx": "8", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx": "9", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx": "10", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx": "11", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx": "12", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx": "13", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx": "14", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx": "15", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx": "16", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx": "17", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx": "18", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx": "19", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx": "20", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx": "21", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx": "22", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx": "23", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx": "24", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx": "25", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx": "26", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx": "27", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js": "28", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx": "29", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx": "30", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx": "31", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx": "32", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx": "33", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx": "34", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx": "35", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx": "36", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx": "37", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx": "38", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx": "39", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx": "40", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx": "41", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx": "42", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx": "43", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx": "44", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx": "45", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx": "46", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx": "47", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx": "48", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx": "49", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx": "50", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx": "51", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx": "52", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx": "53", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx": "54", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx": "55", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx": "56", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx": "57", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx": "58", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx": "59", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx": "60", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx": "61", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx": "62", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx": "63", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx": "64", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx": "65", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx": "66", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx": "67", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx": "68", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx": "69", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx": "70", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx": "71", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx": "72", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js": "73", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx": "74", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx": "75", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx": "76", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx": "77", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx": "78", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js": "79", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js": "80", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx": "81", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx": "82", "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx": "83"}, {"size": 271, "mtime": 1753662156000, "results": "84", "hashOfConfig": "85"}, {"size": 271, "mtime": 1753663554000, "results": "86", "hashOfConfig": "85"}, {"size": 1946, "mtime": 1753938884000, "results": "87", "hashOfConfig": "85"}, {"size": 1104, "mtime": 1753966474128, "results": "88", "hashOfConfig": "85"}, {"size": 263, "mtime": 1753662156000, "results": "89", "hashOfConfig": "85"}, {"size": 2597, "mtime": 1753662156000, "results": "90", "hashOfConfig": "85"}, {"size": 1443, "mtime": 1753896368000, "results": "91", "hashOfConfig": "85"}, {"size": 1851, "mtime": 1753662960000, "results": "92", "hashOfConfig": "85"}, {"size": 1800, "mtime": 1753662960000, "results": "93", "hashOfConfig": "85"}, {"size": 16051, "mtime": 1753979430019, "results": "94", "hashOfConfig": "85"}, {"size": 7262, "mtime": 1753931840000, "results": "95", "hashOfConfig": "85"}, {"size": 15415, "mtime": 1753979341750, "results": "96", "hashOfConfig": "85"}, {"size": 6671, "mtime": 1753660406000, "results": "97", "hashOfConfig": "85"}, {"size": 11787, "mtime": 1753663554000, "results": "98", "hashOfConfig": "85"}, {"size": 6799, "mtime": 1753930442000, "results": "99", "hashOfConfig": "85"}, {"size": 6894, "mtime": 1753660406000, "results": "100", "hashOfConfig": "85"}, {"size": 28604, "mtime": 1753979520661, "results": "101", "hashOfConfig": "85"}, {"size": 619, "mtime": 1753662156000, "results": "102", "hashOfConfig": "85"}, {"size": 3229, "mtime": 1753931434000, "results": "103", "hashOfConfig": "85"}, {"size": 12210, "mtime": 1753662960000, "results": "104", "hashOfConfig": "85"}, {"size": 3119, "mtime": 1753662156000, "results": "105", "hashOfConfig": "85"}, {"size": 9775, "mtime": 1753662156000, "results": "106", "hashOfConfig": "85"}, {"size": 1661, "mtime": 1753662960000, "results": "107", "hashOfConfig": "85"}, {"size": 7921, "mtime": 1753662960000, "results": "108", "hashOfConfig": "85"}, {"size": 2069, "mtime": 1753662960000, "results": "109", "hashOfConfig": "85"}, {"size": 4355, "mtime": 1753662960000, "results": "110", "hashOfConfig": "85"}, {"size": 1270, "mtime": 1753662960000, "results": "111", "hashOfConfig": "85"}, {"size": 1130, "mtime": 1753896930000, "results": "112", "hashOfConfig": "85"}, {"size": 13729, "mtime": 1753977342166, "results": "113", "hashOfConfig": "85"}, {"size": 3061, "mtime": 1753662960000, "results": "114", "hashOfConfig": "85"}, {"size": 7619, "mtime": 1753932988000, "results": "115", "hashOfConfig": "85"}, {"size": 8654, "mtime": 1753904228000, "results": "116", "hashOfConfig": "85"}, {"size": 5902, "mtime": 1753662960000, "results": "117", "hashOfConfig": "85"}, {"size": 4222, "mtime": 1753662960000, "results": "118", "hashOfConfig": "85"}, {"size": 4319, "mtime": 1753662960000, "results": "119", "hashOfConfig": "85"}, {"size": 3004, "mtime": 1753902306000, "results": "120", "hashOfConfig": "85"}, {"size": 7240, "mtime": 1753662960000, "results": "121", "hashOfConfig": "85"}, {"size": 6960, "mtime": 1753662960000, "results": "122", "hashOfConfig": "85"}, {"size": 2962, "mtime": 1753662960000, "results": "123", "hashOfConfig": "85"}, {"size": 4192, "mtime": 1753662960000, "results": "124", "hashOfConfig": "85"}, {"size": 2269, "mtime": 1753662960000, "results": "125", "hashOfConfig": "85"}, {"size": 8238, "mtime": 1753662960000, "results": "126", "hashOfConfig": "85"}, {"size": 5011, "mtime": 1753662960000, "results": "127", "hashOfConfig": "85"}, {"size": 5667, "mtime": 1753662960000, "results": "128", "hashOfConfig": "85"}, {"size": 6178, "mtime": 1753662960000, "results": "129", "hashOfConfig": "85"}, {"size": 3454, "mtime": 1753662960000, "results": "130", "hashOfConfig": "85"}, {"size": 7881, "mtime": 1753662960000, "results": "131", "hashOfConfig": "85"}, {"size": 6935, "mtime": 1753662960000, "results": "132", "hashOfConfig": "85"}, {"size": 5068, "mtime": 1753662960000, "results": "133", "hashOfConfig": "85"}, {"size": 3299, "mtime": 1753662960000, "results": "134", "hashOfConfig": "85"}, {"size": 10393, "mtime": 1753660406000, "results": "135", "hashOfConfig": "85"}, {"size": 12779, "mtime": 1753660406000, "results": "136", "hashOfConfig": "85"}, {"size": 18772, "mtime": 1753660406000, "results": "137", "hashOfConfig": "85"}, {"size": 16373, "mtime": 1753660406000, "results": "138", "hashOfConfig": "85"}, {"size": 19344, "mtime": 1753660406000, "results": "139", "hashOfConfig": "85"}, {"size": 13322, "mtime": 1753939328000, "results": "140", "hashOfConfig": "85"}, {"size": 5370, "mtime": 1753663554000, "results": "141", "hashOfConfig": "85"}, {"size": 5637, "mtime": 1753663554000, "results": "142", "hashOfConfig": "85"}, {"size": 1689, "mtime": 1753663554000, "results": "143", "hashOfConfig": "85"}, {"size": 5556, "mtime": 1753663554000, "results": "144", "hashOfConfig": "85"}, {"size": 17413, "mtime": 1753667086000, "results": "145", "hashOfConfig": "85"}, {"size": 13209, "mtime": 1753667086000, "results": "146", "hashOfConfig": "85"}, {"size": 19464, "mtime": 1753667086000, "results": "147", "hashOfConfig": "85"}, {"size": 7859, "mtime": 1753660406000, "results": "148", "hashOfConfig": "85"}, {"size": 13656, "mtime": 1753660406000, "results": "149", "hashOfConfig": "85"}, {"size": 13718, "mtime": 1753660406000, "results": "150", "hashOfConfig": "85"}, {"size": 2946, "mtime": 1753916458000, "results": "151", "hashOfConfig": "85"}, {"size": 5117, "mtime": 1753916458000, "results": "152", "hashOfConfig": "85"}, {"size": 3744, "mtime": 1753916090000, "results": "153", "hashOfConfig": "85"}, {"size": 5879, "mtime": 1753916458000, "results": "154", "hashOfConfig": "85"}, {"size": 6371, "mtime": 1753979035139, "results": "155", "hashOfConfig": "85"}, {"size": 5794, "mtime": 1753916090000, "results": "156", "hashOfConfig": "85"}, {"size": 139, "mtime": 1753662156000, "results": "157", "hashOfConfig": "85"}, {"size": 7859, "mtime": 1753916090000, "results": "158", "hashOfConfig": "85"}, {"size": 5802, "mtime": 1753916090000, "results": "159", "hashOfConfig": "85"}, {"size": 4753, "mtime": 1753662156000, "results": "160", "hashOfConfig": "85"}, {"size": 329, "mtime": 1753662156000, "results": "161", "hashOfConfig": "85"}, {"size": 6088, "mtime": 1753662960000, "results": "162", "hashOfConfig": "85"}, {"size": 7591, "mtime": 1753976931728, "results": "163", "hashOfConfig": "85"}, {"size": 9757, "mtime": 1753977135191, "results": "164", "hashOfConfig": "85"}, {"size": 4743, "mtime": 1753979063817, "results": "165", "hashOfConfig": "85"}, {"size": 623, "mtime": 1753979082425, "results": "166", "hashOfConfig": "85"}, {"size": 5337, "mtime": 1753979469770, "results": "167", "hashOfConfig": "85"}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "7s4ywu", {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\App.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\Routes.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\contexts\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ScrollToTop.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ErrorBoundary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\NotFound.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\index.jsx", ["417", "418", "419"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\index.jsx", ["420"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\index.jsx", ["421"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\index.jsx", ["422"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\index.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppIcon.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Button.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Header.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Input.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Select.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\LoginForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\SecurityBadges.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\login\\components\\CredentialsHelper.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\organizationService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationForm.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\register\\components\\RegistrationBenefits.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddCardModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\BoardColumn.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\AddColumnModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Breadcrumb.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ChecklistManager.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\MemberAssignment.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\DueDatePicker.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\CardDescription.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\ActivityTimeline.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\card-details\\components\\LabelManager.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\InviteMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\EditRoleModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberTable.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\MemberActivityModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\RemoveMemberModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\team-members\\components\\BulkActionsBar.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\GeneralSettings.jsx", ["423"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\SecuritySettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\BillingSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\IntegrationSettings.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-settings\\components\\MemberManagement.jsx", ["424", "425"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\RoleBasedHeader.jsx", ["426"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\NotificationCenter.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\StatsOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\organization-dashboard\\components\\IntegrationCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\TasksTab.jsx", ["427"], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\ProjectOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\project-management\\components\\SettingsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\PersonalInfoTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\SecurityTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\user-profile-settings\\components\\NotificationsTab.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\KPICard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\DashboardHeader.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ActivityFeed.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\ProjectCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\QuickActions.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TeamOverview.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\cn.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\NotificationPanel.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\role-based-dashboard\\components\\TaskSummary.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Checkbox.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\AppImage.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\pages\\kanban-board\\components\\TaskCard.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\authService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\utils\\apiService.js", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\CreateProjectModal.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\ui\\Textarea.jsx", [], [], "C:\\Users\\<USER>\\PM\\agnoworksphere\\src\\components\\modals\\InviteMemberModal.jsx", [], [], {"ruleId": "428", "severity": 1, "message": "429", "line": 30, "column": 15, "nodeType": "430", "endLine": 30, "endColumn": 81}, {"ruleId": "428", "severity": 1, "message": "429", "line": 33, "column": 15, "nodeType": "430", "endLine": 33, "endColumn": 81}, {"ruleId": "428", "severity": 1, "message": "429", "line": 36, "column": 15, "nodeType": "430", "endLine": 36, "endColumn": 81}, {"ruleId": "431", "severity": 1, "message": "432", "line": 1, "column": 27, "nodeType": "433", "messageId": "434", "endLine": 1, "endColumn": 36}, {"ruleId": "435", "severity": 1, "message": "436", "line": 33, "column": 6, "nodeType": "437", "endLine": 33, "endColumn": 25, "suggestions": "438"}, {"ruleId": "431", "severity": 1, "message": "439", "line": 13, "column": 27, "nodeType": "433", "messageId": "434", "endLine": 13, "endColumn": 45}, {"ruleId": "431", "severity": 1, "message": "440", "line": 20, "column": 10, "nodeType": "433", "messageId": "434", "endLine": 20, "endColumn": 18}, {"ruleId": "441", "severity": 1, "message": "442", "line": 158, "column": 5, "nodeType": "443", "messageId": "444", "endLine": 176, "endColumn": 6}, {"ruleId": "431", "severity": 1, "message": "445", "line": 188, "column": 9, "nodeType": "433", "messageId": "434", "endLine": 188, "endColumn": 21}, {"ruleId": "431", "severity": 1, "message": "446", "line": 94, "column": 9, "nodeType": "433", "messageId": "434", "endLine": 94, "endColumn": 21}, {"ruleId": "431", "severity": 1, "message": "447", "line": 129, "column": 9, "nodeType": "433", "messageId": "434", "endLine": 129, "endColumn": 24}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadDashboardData'. Either include it or remove the dependency array.", "ArrayExpression", ["448"], "'setSidebarExpanded' is assigned a value but never used.", "'logoFile' is assigned a value but never used.", "default-case", "Expected a default case.", "SwitchStatement", "missingDefaultCase", "'getRoleColor' is assigned a value but never used.", "'roleFeatures' is assigned a value but never used.", "'priorityOptions' is assigned a value but never used.", {"desc": "449", "fix": "450"}, "Update the dependencies array to be: [authLoading, loadDashboardData, user]", {"range": "451", "text": "452"}, [1289, 1308], "[authLoading, loadDashboardData, user]"]