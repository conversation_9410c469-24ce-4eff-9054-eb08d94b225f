#!/usr/bin/env python3
"""
Enhanced FastAPI server with RBAC, email notifications, and organization management
"""
import sys
import os
import time
import uuid
from typing import Optional, List
from datetime import datetime

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAPI, HTTPException, Header, BackgroundTasks, Depends
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr
from app.config import settings
from app.core.security import hash_password, verify_password, create_access_token, verify_token
from app.services.email_service import email_service

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Enhanced Agno WorkSphere API with RBAC and email notifications"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for testing (replace with database in production)
users_db = {}
organizations_db = {}
organization_members_db = {}
projects_db = {}
invitations_db = {}

# Pydantic models
class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: str
    organization_name: Optional[str] = None

class UserLogin(BaseModel):
    email: EmailStr
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"
    expires_in: int = 900

class UserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str
    email_verified: bool = True
    created_at: float

class OrganizationResponse(BaseModel):
    id: str
    name: str
    description: Optional[str] = None
    created_at: float
    member_count: int = 1

class OrganizationMemberResponse(BaseModel):
    id: str
    user_id: str
    organization_id: str
    role: str
    joined_at: float
    user: UserResponse

class AuthResponse(BaseModel):
    user: UserResponse
    tokens: TokenResponse
    organization: Optional[OrganizationResponse] = None
    role: Optional[str] = None

class OrganizationCreate(BaseModel):
    name: str
    description: Optional[str] = None
    allowed_domains: Optional[List[str]] = None

class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None
    organization_id: str

class InviteMember(BaseModel):
    email: EmailStr
    role: str = "member"  # viewer, member, admin

class DashboardStats(BaseModel):
    total_organizations: int
    total_projects: int
    total_members: int
    recent_activity: List[dict]


def get_user_from_token(authorization: Optional[str] = Header(None)):
    """Get user from authorization token"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    try:
        token = authorization.replace("Bearer ", "")
        payload = verify_token(token)
        user_id = payload.get("sub")
        
        # Find user in database
        for email, user in users_db.items():
            if user["id"] == user_id:
                return user
        
        raise HTTPException(status_code=401, detail="User not found")
    except Exception as e:
        raise HTTPException(status_code=401, detail="Invalid token")


def check_organization_access(user_id: str, organization_id: str, required_roles: List[str] = None):
    """Check if user has access to organization with required role"""
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id and member["organization_id"] == organization_id:
            if required_roles and member["role"] not in required_roles:
                raise HTTPException(status_code=403, detail="Insufficient permissions")
            return member
    
    raise HTTPException(status_code=403, detail="Access denied to organization")


def check_domain_access(user_email: str, organization_id: str):
    """Check if user's email domain is allowed for organization"""
    org = organizations_db.get(organization_id)
    if not org or not org.get("allowed_domains"):
        return True  # No domain restrictions
    
    user_domain = user_email.split("@")[1].lower()
    allowed_domains = [domain.lower() for domain in org["allowed_domains"]]
    
    return user_domain in allowed_domains


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "success": True,
        "data": {
            "message": "Welcome to Agno WorkSphere API (Enhanced)",
            "version": settings.app_version,
            "environment": settings.environment,
            "features": ["RBAC", "Email Notifications", "Domain Restrictions"]
        },
        "timestamp": time.time()
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "version": settings.app_version,
            "environment": settings.environment,
            "mode": "enhanced",
            "email_configured": bool(settings.smtp_user and settings.smtp_pass)
        },
        "timestamp": time.time()
    }


@app.post("/api/v1/auth/register", response_model=dict)
async def register(
    user_data: UserRegister,
    background_tasks: BackgroundTasks
):
    """Register a new user and create organization"""
    # Check if user already exists
    if user_data.email in users_db:
        raise HTTPException(status_code=409, detail="User with this email already exists")
    
    # Create user
    user_id = str(uuid.uuid4())
    hashed_password = hash_password(user_data.password)
    
    user = {
        "id": user_id,
        "email": user_data.email,
        "password_hash": hashed_password,
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "email_verified": True,  # Auto-verify for demo
        "created_at": time.time()
    }
    
    users_db[user_data.email] = user
    
    # Create default organization for the user
    org_name = user_data.organization_name or f"{user_data.first_name}'s Organization"
    org_id = str(uuid.uuid4())
    
    organization = {
        "id": org_id,
        "name": org_name,
        "description": f"Default organization for {user_data.first_name} {user_data.last_name}",
        "created_by": user_id,
        "created_at": time.time(),
        "allowed_domains": [user_data.email.split("@")[1]]  # Allow same domain by default
    }
    
    organizations_db[org_id] = organization
    
    # Add user as organization owner
    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "user_id": user_id,
        "organization_id": org_id,
        "role": "owner",
        "joined_at": time.time(),
        "invited_by": None
    }
    
    organization_members_db[member_id] = member
    
    # Generate token
    access_token = create_access_token({
        "sub": user_id,
        "email": user_data.email,
        "role": "owner",
        "org_id": org_id
    })
    
    # Send welcome email in background
    background_tasks.add_task(
        email_service.send_welcome_email,
        user_data.email,
        f"{user_data.first_name} {user_data.last_name}",
        org_name
    )
    
    return {
        "success": True,
        "data": AuthResponse(
            user=UserResponse(**user),
            tokens=TokenResponse(access_token=access_token),
            organization=OrganizationResponse(
                **organization,
                member_count=1
            ),
            role="owner"
        ),
        "message": "Registration successful! Welcome email sent."
    }


@app.post("/api/v1/auth/login", response_model=dict)
async def login(user_data: UserLogin):
    """Login user"""
    # Find user
    user = users_db.get(user_data.email)
    if not user or not verify_password(user_data.password, user["password_hash"]):
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    # Get user's primary organization and role
    primary_org = None
    user_role = None
    
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user["id"]:
            org = organizations_db.get(member["organization_id"])
            if org:
                primary_org = org
                user_role = member["role"]
                break
    
    # Generate token
    token_data = {
        "sub": user["id"],
        "email": user["email"]
    }
    
    if primary_org:
        token_data.update({
            "role": user_role,
            "org_id": primary_org["id"]
        })
    
    access_token = create_access_token(token_data)
    
    response_data = AuthResponse(
        user=UserResponse(**user),
        tokens=TokenResponse(access_token=access_token),
        role=user_role
    )
    
    if primary_org:
        # Count members
        member_count = sum(1 for m in organization_members_db.values() 
                          if m["organization_id"] == primary_org["id"])
        
        response_data.organization = OrganizationResponse(
            **primary_org,
            member_count=member_count
        )
    
    return {
        "success": True,
        "data": response_data,
        "message": "Login successful"
    }


@app.get("/api/v1/dashboard/stats")
async def get_dashboard_stats(current_user: dict = Depends(get_user_from_token)):
    """Get dashboard statistics based on user role"""
    user_id = current_user["id"]

    # Get user's organizations
    user_orgs = []
    user_role_in_primary_org = None

    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                user_orgs.append({
                    "organization": org,
                    "role": member["role"]
                })
                if not user_role_in_primary_org:
                    user_role_in_primary_org = member["role"]

    # Count projects user has access to
    accessible_projects = []
    for project_id, project in projects_db.items():
        # Check if user has access to project's organization
        for org_info in user_orgs:
            if project["organization_id"] == org_info["organization"]["id"]:
                accessible_projects.append(project)
                break

    # Count total members across user's organizations
    total_members = 0
    for org_info in user_orgs:
        org_id = org_info["organization"]["id"]
        org_member_count = sum(1 for m in organization_members_db.values()
                              if m["organization_id"] == org_id)
        total_members += org_member_count

    # Recent activity (mock data for now)
    recent_activity = [
        {
            "type": "project_created",
            "message": "New project created",
            "timestamp": time.time() - 3600,
            "user": f"{current_user['first_name']} {current_user['last_name']}"
        },
        {
            "type": "member_joined",
            "message": "New member joined organization",
            "timestamp": time.time() - 7200,
            "user": "System"
        }
    ]

    return {
        "success": True,
        "data": DashboardStats(
            total_organizations=len(user_orgs),
            total_projects=len(accessible_projects),
            total_members=total_members,
            recent_activity=recent_activity
        ),
        "user_role": user_role_in_primary_org
    }


@app.get("/api/v1/users/profile")
async def get_profile(current_user: dict = Depends(get_user_from_token)):
    """Get current user profile with organization info"""
    user_id = current_user["id"]

    # Get user's organizations and roles
    organizations = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                member_count = sum(1 for m in organization_members_db.values()
                                 if m["organization_id"] == org["id"])

                organizations.append({
                    "organization": OrganizationResponse(**org, member_count=member_count),
                    "role": member["role"],
                    "joined_at": member["joined_at"]
                })

    return {
        "success": True,
        "data": {
            "user": UserResponse(**current_user),
            "organizations": organizations
        }
    }


@app.get("/api/v1/organizations")
async def get_organizations(current_user: dict = Depends(get_user_from_token)):
    """Get organizations user has access to"""
    user_id = current_user["id"]
    user_email = current_user["email"]

    organizations = []
    for member_id, member in organization_members_db.items():
        if member["user_id"] == user_id:
            org = organizations_db.get(member["organization_id"])
            if org:
                # Check domain access
                if check_domain_access(user_email, org["id"]):
                    member_count = sum(1 for m in organization_members_db.values()
                                     if m["organization_id"] == org["id"])

                    organizations.append({
                        "organization": OrganizationResponse(**org, member_count=member_count),
                        "role": member["role"]
                    })

    return {
        "success": True,
        "data": organizations
    }


@app.post("/api/v1/organizations")
async def create_organization(
    org_data: OrganizationCreate,
    current_user: dict = Depends(get_user_from_token)
):
    """Create a new organization (owner/admin only)"""
    user_id = current_user["id"]

    # Check if user has permission to create organizations
    # For now, allow any authenticated user to create organizations

    org_id = str(uuid.uuid4())
    organization = {
        "id": org_id,
        "name": org_data.name,
        "description": org_data.description,
        "created_by": user_id,
        "created_at": time.time(),
        "allowed_domains": org_data.allowed_domains or []
    }

    organizations_db[org_id] = organization

    # Add creator as owner
    member_id = str(uuid.uuid4())
    member = {
        "id": member_id,
        "user_id": user_id,
        "organization_id": org_id,
        "role": "owner",
        "joined_at": time.time(),
        "invited_by": None
    }

    organization_members_db[member_id] = member

    return {
        "success": True,
        "data": {
            "organization": OrganizationResponse(**organization, member_count=1),
            "role": "owner"
        },
        "message": "Organization created successfully"
    }


@app.get("/api/v1/organizations/{org_id}/members")
async def get_organization_members(
    org_id: str,
    current_user: dict = Depends(get_user_from_token)
):
    """Get organization members (member+ role required)"""
    user_id = current_user["id"]

    # Check access
    check_organization_access(user_id, org_id, ["member", "admin", "owner"])

    members = []
    for member_id, member in organization_members_db.items():
        if member["organization_id"] == org_id:
            # Get user info
            user_info = None
            for email, user in users_db.items():
                if user["id"] == member["user_id"]:
                    user_info = user
                    break

            if user_info:
                members.append(OrganizationMemberResponse(
                    **member,
                    user=UserResponse(**user_info)
                ))

    return {
        "success": True,
        "data": members
    }


@app.post("/api/v1/organizations/{org_id}/invite")
async def invite_member(
    org_id: str,
    invite_data: InviteMember,
    background_tasks: BackgroundTasks,
    current_user: dict = Depends(get_user_from_token)
):
    """Invite member to organization (admin+ role required)"""
    user_id = current_user["id"]

    # Check access
    member_info = check_organization_access(user_id, org_id, ["admin", "owner"])

    # Check domain restrictions
    if not check_domain_access(invite_data.email, org_id):
        raise HTTPException(
            status_code=403,
            detail="Email domain not allowed for this organization"
        )

    # Check if user already exists
    existing_user = users_db.get(invite_data.email)

    if existing_user:
        # Check if already a member
        for member_id, member in organization_members_db.items():
            if (member["user_id"] == existing_user["id"] and
                member["organization_id"] == org_id):
                raise HTTPException(status_code=409, detail="User is already a member")

        # Add existing user to organization
        member_id = str(uuid.uuid4())
        member = {
            "id": member_id,
            "user_id": existing_user["id"],
            "organization_id": org_id,
            "role": invite_data.role,
            "joined_at": time.time(),
            "invited_by": user_id
        }

        organization_members_db[member_id] = member

        message = "User added to organization successfully"
    else:
        # Create invitation for new user
        invitation_id = str(uuid.uuid4())
        invitation = {
            "id": invitation_id,
            "email": invite_data.email,
            "organization_id": org_id,
            "role": invite_data.role,
            "invited_by": user_id,
            "created_at": time.time(),
            "expires_at": time.time() + (7 * 24 * 3600)  # 7 days
        }

        invitations_db[invitation_id] = invitation

        # Send invitation email
        org = organizations_db[org_id]
        inviter_name = f"{current_user['first_name']} {current_user['last_name']}"
        invitation_url = f"http://localhost:3000/invite/{invitation_id}"

        background_tasks.add_task(
            email_service.send_invitation_email,
            invite_data.email,
            inviter_name,
            org["name"],
            invite_data.role,
            invitation_url
        )

        message = "Invitation sent successfully"

    return {
        "success": True,
        "message": message
    }


@app.get("/api/v1/projects")
async def get_projects(
    organization_id: Optional[str] = None,
    current_user: dict = Depends(get_user_from_token)
):
    """Get projects user has access to"""
    user_id = current_user["id"]

    accessible_projects = []

    if organization_id:
        # Check access to specific organization
        check_organization_access(user_id, organization_id)

        # Get projects for this organization
        for project_id, project in projects_db.items():
            if project["organization_id"] == organization_id:
                accessible_projects.append(project)
    else:
        # Get all projects user has access to
        user_org_ids = []
        for member_id, member in organization_members_db.items():
            if member["user_id"] == user_id:
                user_org_ids.append(member["organization_id"])

        for project_id, project in projects_db.items():
            if project["organization_id"] in user_org_ids:
                accessible_projects.append(project)

    return {
        "success": True,
        "data": accessible_projects
    }


@app.post("/api/v1/projects")
async def create_project(
    project_data: ProjectCreate,
    current_user: dict = Depends(get_user_from_token)
):
    """Create project (member+ role required)"""
    user_id = current_user["id"]

    # Check access to organization
    check_organization_access(user_id, project_data.organization_id, ["member", "admin", "owner"])

    project_id = str(uuid.uuid4())
    project = {
        "id": project_id,
        "name": project_data.name,
        "description": project_data.description,
        "organization_id": project_data.organization_id,
        "status": "active",
        "created_by": user_id,
        "created_at": time.time()
    }

    projects_db[project_id] = project

    return {
        "success": True,
        "data": project,
        "message": "Project created successfully"
    }


if __name__ == "__main__":
    import uvicorn
    print(f"🚀 Starting {settings.app_name} (Enhanced Mode)")
    print(f"📍 Server will be available at: http://localhost:3001")
    print(f"📚 API Documentation: http://localhost:3001/docs")
    print(f"🔧 Environment: {settings.environment}")
    print(f"📧 Email configured: {bool(settings.smtp_user and settings.smtp_pass)}")

    uvicorn.run(
        "enhanced_server:app",
        host="0.0.0.0",
        port=3001,
        reload=settings.debug,
        log_level="info"
    )
