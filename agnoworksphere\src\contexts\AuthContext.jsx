// src/contexts/AuthContext.jsx

import React, { createContext, useContext } from 'react';

// Create context
const AuthContext = createContext();

// Mock context provider (authentication removed)
export const AuthProvider = ({ children }) => {
  // Mock user object for testing purposes
  const user = {
    id: '550e8400-e29b-41d4-a716-446655440000',
    email: '<EMAIL>',
    firstName: 'Demo',
    lastName: 'User',
    role: 'admin',
    isAuthenticated: true
  };

  const login = () => {
    // Authentication removed - always return success
    return Promise.resolve({ success: true });
  };

  const logout = () => {
    // Authentication removed - always return success
    return Promise.resolve({ success: true });
  };

  const register = () => {
    // Authentication removed - always return success
    return Promise.resolve({ success: true });
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, register, isAuthenticated: true }}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook
export const useAuth = () => useContext(AuthContext);
