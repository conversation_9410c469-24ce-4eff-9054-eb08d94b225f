{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\kanban-board\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useNavigate } from 'react-router-dom';\nimport Header from '../../components/ui/Header';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport BoardHeader from './components/BoardHeader';\nimport BoardColumn from './components/BoardColumn';\nimport AddCardModal from './components/AddCardModal';\nimport AddColumnModal from './components/AddColumnModal';\nimport InviteMemberModal from './components/InviteMemberModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KanbanBoard = () => {\n  _s();\n  const navigate = useNavigate();\n\n  // Mock data\n  const [board] = useState({\n    id: 'board-1',\n    title: 'Project Management Board',\n    description: 'Main project tracking board for Q4 initiatives',\n    isPrivate: false,\n    createdAt: '2025-01-15T10:00:00Z',\n    updatedAt: '2025-01-28T05:54:23Z'\n  });\n  const [members] = useState([{\n    id: 'user-1',\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    role: 'Admin',\n    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'\n  }, {\n    id: 'user-2',\n    name: 'Michael Chen',\n    email: '<EMAIL>',\n    role: 'Member',\n    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'\n  }, {\n    id: 'user-3',\n    name: 'Emily Rodriguez',\n    email: '<EMAIL>',\n    role: 'Member',\n    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'\n  }, {\n    id: 'user-4',\n    name: 'David Kim',\n    email: '<EMAIL>',\n    role: 'Viewer',\n    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'\n  }, {\n    id: 'user-5',\n    name: 'Lisa Wang',\n    email: '<EMAIL>',\n    role: 'Member',\n    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'\n  }]);\n  const [columns, setColumns] = useState([{\n    id: 'col-1',\n    title: 'To Do',\n    status: 'todo',\n    order: 1,\n    createdAt: '2025-01-15T10:00:00Z'\n  }, {\n    id: 'col-2',\n    title: 'In Progress',\n    status: 'in-progress',\n    order: 2,\n    createdAt: '2025-01-15T10:00:00Z'\n  }, {\n    id: 'col-3',\n    title: 'Review',\n    status: 'review',\n    order: 3,\n    createdAt: '2025-01-15T10:00:00Z'\n  }, {\n    id: 'col-4',\n    title: 'Done',\n    status: 'done',\n    order: 4,\n    createdAt: '2025-01-15T10:00:00Z'\n  }]);\n  const [cards, setCards] = useState([{\n    id: 'card-1',\n    columnId: 'col-1',\n    title: 'Design user authentication flow',\n    description: 'Create wireframes and mockups for the login and registration process',\n    priority: 'high',\n    assignedTo: ['user-1', 'user-2'],\n    dueDate: '2025-02-05',\n    labels: [{\n      id: 'design',\n      name: 'Design',\n      color: '#3b82f6'\n    }, {\n      id: 'ux',\n      name: 'UX',\n      color: '#8b5cf6'\n    }],\n    checklist: [{\n      id: 'check-1',\n      text: 'Research competitor flows',\n      completed: true\n    }, {\n      id: 'check-2',\n      text: 'Create wireframes',\n      completed: false\n    }, {\n      id: 'check-3',\n      text: 'Design mockups',\n      completed: false\n    }],\n    comments: [{\n      id: 'comment-1',\n      author: 'user-2',\n      content: 'Should we include social login options?',\n      createdAt: '2025-01-27T14:30:00Z'\n    }],\n    attachments: [],\n    createdAt: '2025-01-25T09:00:00Z',\n    updatedAt: '2025-01-27T14:30:00Z'\n  }, {\n    id: 'card-2',\n    columnId: 'col-1',\n    title: 'Set up project repository',\n    description: 'Initialize Git repository with proper folder structure and documentation',\n    priority: 'medium',\n    assignedTo: ['user-3'],\n    dueDate: '2025-01-30',\n    labels: [{\n      id: 'development',\n      name: 'Development',\n      color: '#10b981'\n    }],\n    checklist: [],\n    comments: [],\n    attachments: [],\n    createdAt: '2025-01-26T11:00:00Z',\n    updatedAt: '2025-01-26T11:00:00Z'\n  }, {\n    id: 'card-3',\n    columnId: 'col-2',\n    title: 'Implement user registration API',\n    description: 'Build backend endpoints for user registration with validation and email verification',\n    priority: 'high',\n    assignedTo: ['user-2', 'user-5'],\n    dueDate: '2025-02-10',\n    labels: [{\n      id: 'backend',\n      name: 'Backend',\n      color: '#f59e0b'\n    }, {\n      id: 'api',\n      name: 'API',\n      color: '#ef4444'\n    }],\n    checklist: [{\n      id: 'check-4',\n      text: 'Design database schema',\n      completed: true\n    }, {\n      id: 'check-5',\n      text: 'Implement validation',\n      completed: true\n    }, {\n      id: 'check-6',\n      text: 'Add email verification',\n      completed: false\n    }, {\n      id: 'check-7',\n      text: 'Write unit tests',\n      completed: false\n    }],\n    comments: [{\n      id: 'comment-2',\n      author: 'user-1',\n      content: 'Make sure to include proper error handling',\n      createdAt: '2025-01-26T16:45:00Z'\n    }, {\n      id: 'comment-3',\n      author: 'user-5',\n      content: 'Working on the email service integration',\n      createdAt: '2025-01-27T10:15:00Z'\n    }],\n    attachments: [{\n      id: 'att-1',\n      name: 'api-spec.pdf',\n      size: '2.4 MB'\n    }],\n    createdAt: '2025-01-24T13:00:00Z',\n    updatedAt: '2025-01-27T10:15:00Z'\n  }, {\n    id: 'card-4',\n    columnId: 'col-3',\n    title: 'Review dashboard components',\n    description: 'Code review for the new dashboard UI components',\n    priority: 'medium',\n    assignedTo: ['user-1', 'user-4'],\n    dueDate: '2025-01-29',\n    labels: [{\n      id: 'review',\n      name: 'Review',\n      color: '#8b5cf6'\n    }, {\n      id: 'frontend',\n      name: 'Frontend',\n      color: '#06b6d4'\n    }],\n    checklist: [{\n      id: 'check-8',\n      text: 'Check code quality',\n      completed: true\n    }, {\n      id: 'check-9',\n      text: 'Test responsiveness',\n      completed: false\n    }, {\n      id: 'check-10',\n      text: 'Verify accessibility',\n      completed: false\n    }],\n    comments: [],\n    attachments: [],\n    createdAt: '2025-01-23T15:30:00Z',\n    updatedAt: '2025-01-27T09:20:00Z'\n  }, {\n    id: 'card-5',\n    columnId: 'col-4',\n    title: 'Update project documentation',\n    description: 'Refresh README and API documentation with latest changes',\n    priority: 'low',\n    assignedTo: ['user-3'],\n    dueDate: null,\n    labels: [{\n      id: 'documentation',\n      name: 'Documentation',\n      color: '#f59e0b'\n    }],\n    checklist: [{\n      id: 'check-11',\n      text: 'Update README',\n      completed: true\n    }, {\n      id: 'check-12',\n      text: 'Update API docs',\n      completed: true\n    }, {\n      id: 'check-13',\n      text: 'Add deployment guide',\n      completed: true\n    }],\n    comments: [{\n      id: 'comment-4',\n      author: 'user-1',\n      content: 'Great work on the documentation!',\n      createdAt: '2025-01-25T12:00:00Z'\n    }],\n    attachments: [],\n    createdAt: '2025-01-20T10:00:00Z',\n    updatedAt: '2025-01-25T12:00:00Z'\n  }]);\n\n  // Modal states\n  const [showAddCardModal, setShowAddCardModal] = useState(false);\n  const [showAddColumnModal, setShowAddColumnModal] = useState(false);\n  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);\n  const [selectedColumnId, setSelectedColumnId] = useState(null);\n\n  // Filter and search states\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeFilters, setActiveFilters] = useState({});\n\n  // Filter cards based on search and filters\n  const filteredCards = cards.filter(card => {\n    var _activeFilters$priori, _activeFilters$assign, _activeFilters$dueDat;\n    // Search filter\n    if (searchQuery) {\n      var _card$labels;\n      const query = searchQuery.toLowerCase();\n      const matchesSearch = card.title.toLowerCase().includes(query) || card.description.toLowerCase().includes(query) || ((_card$labels = card.labels) === null || _card$labels === void 0 ? void 0 : _card$labels.some(label => label.name.toLowerCase().includes(query)));\n      if (!matchesSearch) return false;\n    }\n\n    // Priority filter\n    if (((_activeFilters$priori = activeFilters.priority) === null || _activeFilters$priori === void 0 ? void 0 : _activeFilters$priori.length) > 0) {\n      if (!activeFilters.priority.includes(card.priority)) return false;\n    }\n\n    // Assignee filter\n    if (((_activeFilters$assign = activeFilters.assignee) === null || _activeFilters$assign === void 0 ? void 0 : _activeFilters$assign.length) > 0) {\n      var _card$assignedTo;\n      const hasAssignee = (_card$assignedTo = card.assignedTo) === null || _card$assignedTo === void 0 ? void 0 : _card$assignedTo.some(assigneeId => activeFilters.assignee.includes(assigneeId));\n      if (!hasAssignee) return false;\n    }\n\n    // Due date filter\n    if (((_activeFilters$dueDat = activeFilters.dueDate) === null || _activeFilters$dueDat === void 0 ? void 0 : _activeFilters$dueDat.length) > 0) {\n      const today = new Date();\n      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;\n      const matchesDueDate = activeFilters.dueDate.some(filter => {\n        if (filter === 'overdue') {\n          return cardDueDate && cardDueDate < today;\n        }\n        if (filter === 'today') {\n          return cardDueDate && cardDueDate.toDateString() === today.toDateString();\n        }\n        if (filter === 'this-week') {\n          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;\n        }\n        if (filter === 'this-month') {\n          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());\n          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;\n        }\n        return false;\n      });\n      if (!matchesDueDate) return false;\n    }\n    return true;\n  });\n\n  // Handle card movement between columns\n  const handleCardMove = (cardId, sourceColumnId, targetColumnId) => {\n    setCards(prevCards => prevCards.map(card => card.id === cardId ? {\n      ...card,\n      columnId: targetColumnId,\n      updatedAt: new Date().toISOString()\n    } : card));\n  };\n\n  // Handle card click - navigate to card details\n  const handleCardClick = card => {\n    navigate('/card-details', {\n      state: {\n        card,\n        members\n      }\n    });\n  };\n\n  // Handle adding new card\n  const handleAddCard = columnId => {\n    setSelectedColumnId(columnId);\n    setShowAddCardModal(true);\n  };\n  const handleSaveCard = newCard => {\n    setCards(prevCards => [...prevCards, newCard]);\n  };\n\n  // Handle adding new column\n  const handleSaveColumn = newColumn => {\n    setColumns(prevColumns => [...prevColumns, newColumn]);\n  };\n\n  // Handle column operations\n  const handleEditColumn = (columnId, updates) => {\n    setColumns(prevColumns => prevColumns.map(col => col.id === columnId ? {\n      ...col,\n      ...updates,\n      updatedAt: new Date().toISOString()\n    } : col));\n  };\n  const handleDeleteColumn = columnId => {\n    var _columns$;\n    // Move cards from deleted column to first column\n    const firstColumnId = (_columns$ = columns[0]) === null || _columns$ === void 0 ? void 0 : _columns$.id;\n    if (firstColumnId && firstColumnId !== columnId) {\n      setCards(prevCards => prevCards.map(card => card.columnId === columnId ? {\n        ...card,\n        columnId: firstColumnId\n      } : card));\n    }\n    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));\n  };\n\n  // Handle member invitation\n  const handleMemberInvite = () => {\n    setShowInviteMemberModal(true);\n  };\n  const handleSendInvitation = invitation => {\n    console.log('Invitation sent:', invitation);\n    // In real app, this would send the invitation via API\n  };\n\n  // Handle board updates\n  const handleBoardUpdate = updates => {\n    console.log('Board updated:', updates);\n    // In real app, this would update the board via API\n  };\n\n  // Get cards for a specific column\n  const getCardsForColumn = columnId => {\n    return filteredCards.filter(card => card.columnId === columnId);\n  };\n  return /*#__PURE__*/_jsxDEV(DndProvider, {\n    backend: HTML5Backend,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-background\",\n      children: [/*#__PURE__*/_jsxDEV(BoardHeader, {\n        board: board,\n        members: members,\n        onBoardUpdate: handleBoardUpdate,\n        onMemberInvite: handleMemberInvite,\n        onFilterChange: setActiveFilters,\n        onSearchChange: setSearchQuery,\n        searchQuery: searchQuery,\n        activeFilters: activeFilters\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 383,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 p-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex space-x-6 overflow-x-auto pb-6\",\n          children: [columns.sort((a, b) => a.order - b.order).map(column => /*#__PURE__*/_jsxDEV(BoardColumn, {\n            column: column,\n            cards: getCardsForColumn(column.id),\n            onCardMove: handleCardMove,\n            onCardClick: handleCardClick,\n            onAddCard: handleAddCard,\n            onEditColumn: handleEditColumn,\n            onDeleteColumn: handleDeleteColumn,\n            members: members\n          }, column.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 401,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"outline\",\n              onClick: () => setShowAddColumnModal(true),\n              className: \"w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors\",\n              iconName: \"Plus\",\n              iconPosition: \"left\",\n              children: \"Add Column\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 416,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 415,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 396,\n          columnNumber: 11\n        }, this), filteredCards.length === 0 && searchQuery && /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center justify-center py-12\",\n          children: [/*#__PURE__*/_jsxDEV(Icon, {\n            name: \"Search\",\n            size: 48,\n            className: \"text-text-secondary mb-4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-text-primary mb-2\",\n            children: \"No cards found\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 432,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-text-secondary text-center max-w-md\",\n            children: \"No cards match your search criteria. Try adjusting your search terms or filters.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            onClick: () => {\n              setSearchQuery('');\n              setActiveFilters({});\n            },\n            className: \"mt-4\",\n            children: \"Clear Search & Filters\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 430,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AddCardModal, {\n        isOpen: showAddCardModal,\n        onClose: () => {\n          setShowAddCardModal(false);\n          setSelectedColumnId(null);\n        },\n        onSave: handleSaveCard,\n        columnId: selectedColumnId,\n        members: members\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AddColumnModal, {\n        isOpen: showAddColumnModal,\n        onClose: () => setShowAddColumnModal(false),\n        onSave: handleSaveColumn\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(InviteMemberModal, {\n        isOpen: showInviteMemberModal,\n        onClose: () => setShowInviteMemberModal(false),\n        onInvite: handleSendInvitation\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 468,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 381,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 380,\n    columnNumber: 5\n  }, this);\n};\n_s(KanbanBoard, \"aIqlwy0Tw3nuWFTlI2Bkkt4jUEU=\", false, function () {\n  return [useNavigate];\n});\n_c = KanbanBoard;\nexport default KanbanBoard;\nvar _c;\n$RefreshReg$(_c, \"KanbanBoard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "DndProvider", "HTML5Backend", "useNavigate", "Header", "Breadcrumb", "Icon", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "BoardColumn", "AddCardModal", "AddColumnModal", "InviteMemberModal", "jsxDEV", "_jsxDEV", "KanbanBoard", "_s", "navigate", "board", "id", "title", "description", "isPrivate", "createdAt", "updatedAt", "members", "name", "email", "role", "avatar", "columns", "setColumns", "status", "order", "cards", "setCards", "columnId", "priority", "assignedTo", "dueDate", "labels", "color", "checklist", "text", "completed", "comments", "author", "content", "attachments", "size", "showAddCardModal", "setShowAddCardModal", "showAddColumnModal", "setShowAddColumnModal", "showInviteMemberModal", "setShowInviteMemberModal", "selectedColumnId", "setSelectedColumnId", "searchQuery", "setSearch<PERSON>uery", "activeFilters", "setActiveFilters", "filteredCards", "filter", "card", "_activeFilters$priori", "_activeFilters$assign", "_activeFilters$dueDat", "_card$labels", "query", "toLowerCase", "matchesSearch", "includes", "some", "label", "length", "assignee", "_card$assignedTo", "<PERSON>As<PERSON>ee", "assigneeId", "today", "Date", "cardDueDate", "matchesDueDate", "toDateString", "weekFromNow", "getTime", "monthFromNow", "getFullYear", "getMonth", "getDate", "handleCardMove", "cardId", "sourceColumnId", "targetColumnId", "prevCards", "map", "toISOString", "handleCardClick", "state", "handleAddCard", "handleSaveCard", "newCard", "handleSaveColumn", "newColumn", "prevColumns", "handleEditColumn", "updates", "col", "handleDeleteColumn", "_columns$", "firstColumnId", "handleMemberInvite", "handleSendInvitation", "invitation", "console", "log", "handleBoardUpdate", "getCardsForColumn", "backend", "children", "className", "onBoardUpdate", "onMemberInvite", "onFilterChange", "onSearchChange", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sort", "a", "b", "column", "onCardMove", "onCardClick", "onAddCard", "onEditColumn", "onDeleteColumn", "variant", "onClick", "iconName", "iconPosition", "isOpen", "onClose", "onSave", "onInvite", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/kanban-board/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useNavigate } from 'react-router-dom';\nimport Header from '../../components/ui/Header';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport BoardHeader from './components/BoardHeader';\nimport BoardColumn from './components/BoardColumn';\n\nimport AddCardModal from './components/AddCardModal';\nimport AddColumnModal from './components/AddColumnModal';\nimport InviteMemberModal from './components/InviteMemberModal';\n\nconst KanbanBoard = () => {\n  const navigate = useNavigate();\n  \n  // Mock data\n  const [board] = useState({\n    id: 'board-1',\n    title: 'Project Management Board',\n    description: 'Main project tracking board for Q4 initiatives',\n    isPrivate: false,\n    createdAt: '2025-01-15T10:00:00Z',\n    updatedAt: '2025-01-28T05:54:23Z'\n  });\n\n  const [members] = useState([\n    {\n      id: 'user-1',\n      name: 'Sarah Johnson',\n      email: '<EMAIL>',\n      role: 'Admin',\n      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'\n    },\n    {\n      id: 'user-2',\n      name: 'Michael Chen',\n      email: '<EMAIL>',\n      role: 'Member',\n      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'\n    },\n    {\n      id: 'user-3',\n      name: 'Emily Rodriguez',\n      email: '<EMAIL>',\n      role: 'Member',\n      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'\n    },\n    {\n      id: 'user-4',\n      name: 'David Kim',\n      email: '<EMAIL>',\n      role: 'Viewer',\n      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'\n    },\n    {\n      id: 'user-5',\n      name: 'Lisa Wang',\n      email: '<EMAIL>',\n      role: 'Member',\n      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'\n    }\n  ]);\n\n  const [columns, setColumns] = useState([\n    {\n      id: 'col-1',\n      title: 'To Do',\n      status: 'todo',\n      order: 1,\n      createdAt: '2025-01-15T10:00:00Z'\n    },\n    {\n      id: 'col-2',\n      title: 'In Progress',\n      status: 'in-progress',\n      order: 2,\n      createdAt: '2025-01-15T10:00:00Z'\n    },\n    {\n      id: 'col-3',\n      title: 'Review',\n      status: 'review',\n      order: 3,\n      createdAt: '2025-01-15T10:00:00Z'\n    },\n    {\n      id: 'col-4',\n      title: 'Done',\n      status: 'done',\n      order: 4,\n      createdAt: '2025-01-15T10:00:00Z'\n    }\n  ]);\n\n  const [cards, setCards] = useState([\n    {\n      id: 'card-1',\n      columnId: 'col-1',\n      title: 'Design user authentication flow',\n      description: 'Create wireframes and mockups for the login and registration process',\n      priority: 'high',\n      assignedTo: ['user-1', 'user-2'],\n      dueDate: '2025-02-05',\n      labels: [\n        { id: 'design', name: 'Design', color: '#3b82f6' },\n        { id: 'ux', name: 'UX', color: '#8b5cf6' }\n      ],\n      checklist: [\n        { id: 'check-1', text: 'Research competitor flows', completed: true },\n        { id: 'check-2', text: 'Create wireframes', completed: false },\n        { id: 'check-3', text: 'Design mockups', completed: false }\n      ],\n      comments: [\n        {\n          id: 'comment-1',\n          author: 'user-2',\n          content: 'Should we include social login options?',\n          createdAt: '2025-01-27T14:30:00Z'\n        }\n      ],\n      attachments: [],\n      createdAt: '2025-01-25T09:00:00Z',\n      updatedAt: '2025-01-27T14:30:00Z'\n    },\n    {\n      id: 'card-2',\n      columnId: 'col-1',\n      title: 'Set up project repository',\n      description: 'Initialize Git repository with proper folder structure and documentation',\n      priority: 'medium',\n      assignedTo: ['user-3'],\n      dueDate: '2025-01-30',\n      labels: [\n        { id: 'development', name: 'Development', color: '#10b981' }\n      ],\n      checklist: [],\n      comments: [],\n      attachments: [],\n      createdAt: '2025-01-26T11:00:00Z',\n      updatedAt: '2025-01-26T11:00:00Z'\n    },\n    {\n      id: 'card-3',\n      columnId: 'col-2',\n      title: 'Implement user registration API',\n      description: 'Build backend endpoints for user registration with validation and email verification',\n      priority: 'high',\n      assignedTo: ['user-2', 'user-5'],\n      dueDate: '2025-02-10',\n      labels: [\n        { id: 'backend', name: 'Backend', color: '#f59e0b' },\n        { id: 'api', name: 'API', color: '#ef4444' }\n      ],\n      checklist: [\n        { id: 'check-4', text: 'Design database schema', completed: true },\n        { id: 'check-5', text: 'Implement validation', completed: true },\n        { id: 'check-6', text: 'Add email verification', completed: false },\n        { id: 'check-7', text: 'Write unit tests', completed: false }\n      ],\n      comments: [\n        {\n          id: 'comment-2',\n          author: 'user-1',\n          content: 'Make sure to include proper error handling',\n          createdAt: '2025-01-26T16:45:00Z'\n        },\n        {\n          id: 'comment-3',\n          author: 'user-5',\n          content: 'Working on the email service integration',\n          createdAt: '2025-01-27T10:15:00Z'\n        }\n      ],\n      attachments: [\n        { id: 'att-1', name: 'api-spec.pdf', size: '2.4 MB' }\n      ],\n      createdAt: '2025-01-24T13:00:00Z',\n      updatedAt: '2025-01-27T10:15:00Z'\n    },\n    {\n      id: 'card-4',\n      columnId: 'col-3',\n      title: 'Review dashboard components',\n      description: 'Code review for the new dashboard UI components',\n      priority: 'medium',\n      assignedTo: ['user-1', 'user-4'],\n      dueDate: '2025-01-29',\n      labels: [\n        { id: 'review', name: 'Review', color: '#8b5cf6' },\n        { id: 'frontend', name: 'Frontend', color: '#06b6d4' }\n      ],\n      checklist: [\n        { id: 'check-8', text: 'Check code quality', completed: true },\n        { id: 'check-9', text: 'Test responsiveness', completed: false },\n        { id: 'check-10', text: 'Verify accessibility', completed: false }\n      ],\n      comments: [],\n      attachments: [],\n      createdAt: '2025-01-23T15:30:00Z',\n      updatedAt: '2025-01-27T09:20:00Z'\n    },\n    {\n      id: 'card-5',\n      columnId: 'col-4',\n      title: 'Update project documentation',\n      description: 'Refresh README and API documentation with latest changes',\n      priority: 'low',\n      assignedTo: ['user-3'],\n      dueDate: null,\n      labels: [\n        { id: 'documentation', name: 'Documentation', color: '#f59e0b' }\n      ],\n      checklist: [\n        { id: 'check-11', text: 'Update README', completed: true },\n        { id: 'check-12', text: 'Update API docs', completed: true },\n        { id: 'check-13', text: 'Add deployment guide', completed: true }\n      ],\n      comments: [\n        {\n          id: 'comment-4',\n          author: 'user-1',\n          content: 'Great work on the documentation!',\n          createdAt: '2025-01-25T12:00:00Z'\n        }\n      ],\n      attachments: [],\n      createdAt: '2025-01-20T10:00:00Z',\n      updatedAt: '2025-01-25T12:00:00Z'\n    }\n  ]);\n\n  // Modal states\n  const [showAddCardModal, setShowAddCardModal] = useState(false);\n  const [showAddColumnModal, setShowAddColumnModal] = useState(false);\n  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);\n  const [selectedColumnId, setSelectedColumnId] = useState(null);\n\n  // Filter and search states\n  const [searchQuery, setSearchQuery] = useState('');\n  const [activeFilters, setActiveFilters] = useState({});\n\n  // Filter cards based on search and filters\n  const filteredCards = cards.filter(card => {\n    // Search filter\n    if (searchQuery) {\n      const query = searchQuery.toLowerCase();\n      const matchesSearch = \n        card.title.toLowerCase().includes(query) ||\n        card.description.toLowerCase().includes(query) ||\n        card.labels?.some(label => label.name.toLowerCase().includes(query));\n      \n      if (!matchesSearch) return false;\n    }\n\n    // Priority filter\n    if (activeFilters.priority?.length > 0) {\n      if (!activeFilters.priority.includes(card.priority)) return false;\n    }\n\n    // Assignee filter\n    if (activeFilters.assignee?.length > 0) {\n      const hasAssignee = card.assignedTo?.some(assigneeId => \n        activeFilters.assignee.includes(assigneeId)\n      );\n      if (!hasAssignee) return false;\n    }\n\n    // Due date filter\n    if (activeFilters.dueDate?.length > 0) {\n      const today = new Date();\n      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;\n      \n      const matchesDueDate = activeFilters.dueDate.some(filter => {\n        if (filter === 'overdue') {\n          return cardDueDate && cardDueDate < today;\n        }\n        if (filter === 'today') {\n          return cardDueDate && cardDueDate.toDateString() === today.toDateString();\n        }\n        if (filter === 'this-week') {\n          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);\n          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;\n        }\n        if (filter === 'this-month') {\n          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());\n          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;\n        }\n        return false;\n      });\n      \n      if (!matchesDueDate) return false;\n    }\n\n    return true;\n  });\n\n  // Handle card movement between columns\n  const handleCardMove = (cardId, sourceColumnId, targetColumnId) => {\n    setCards(prevCards => \n      prevCards.map(card => \n        card.id === cardId \n          ? { ...card, columnId: targetColumnId, updatedAt: new Date().toISOString() }\n          : card\n      )\n    );\n  };\n\n  // Handle card click - navigate to card details\n  const handleCardClick = (card) => {\n    navigate('/card-details', { state: { card, members } });\n  };\n\n  // Handle adding new card\n  const handleAddCard = (columnId) => {\n    setSelectedColumnId(columnId);\n    setShowAddCardModal(true);\n  };\n\n  const handleSaveCard = (newCard) => {\n    setCards(prevCards => [...prevCards, newCard]);\n  };\n\n  // Handle adding new column\n  const handleSaveColumn = (newColumn) => {\n    setColumns(prevColumns => [...prevColumns, newColumn]);\n  };\n\n  // Handle column operations\n  const handleEditColumn = (columnId, updates) => {\n    setColumns(prevColumns =>\n      prevColumns.map(col =>\n        col.id === columnId\n          ? { ...col, ...updates, updatedAt: new Date().toISOString() }\n          : col\n      )\n    );\n  };\n\n  const handleDeleteColumn = (columnId) => {\n    // Move cards from deleted column to first column\n    const firstColumnId = columns[0]?.id;\n    if (firstColumnId && firstColumnId !== columnId) {\n      setCards(prevCards =>\n        prevCards.map(card =>\n          card.columnId === columnId\n            ? { ...card, columnId: firstColumnId }\n            : card\n        )\n      );\n    }\n    \n    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));\n  };\n\n  // Handle member invitation\n  const handleMemberInvite = () => {\n    setShowInviteMemberModal(true);\n  };\n\n  const handleSendInvitation = (invitation) => {\n    console.log('Invitation sent:', invitation);\n    // In real app, this would send the invitation via API\n  };\n\n  // Handle board updates\n  const handleBoardUpdate = (updates) => {\n    console.log('Board updated:', updates);\n    // In real app, this would update the board via API\n  };\n\n  // Get cards for a specific column\n  const getCardsForColumn = (columnId) => {\n    return filteredCards.filter(card => card.columnId === columnId);\n  };\n\n  return (\n    <DndProvider backend={HTML5Backend}>\n      <div className=\"min-h-screen bg-background\">\n        {/* Board Header */}\n        <BoardHeader\n          board={board}\n          members={members}\n          onBoardUpdate={handleBoardUpdate}\n          onMemberInvite={handleMemberInvite}\n          onFilterChange={setActiveFilters}\n          onSearchChange={setSearchQuery}\n          searchQuery={searchQuery}\n          activeFilters={activeFilters}\n        />\n\n        {/* Board Content */}\n        <div className=\"flex-1 p-6\">\n          <div className=\"flex space-x-6 overflow-x-auto pb-6\">\n            {/* Columns */}\n            {columns\n              .sort((a, b) => a.order - b.order)\n              .map(column => (\n                <BoardColumn\n                  key={column.id}\n                  column={column}\n                  cards={getCardsForColumn(column.id)}\n                  onCardMove={handleCardMove}\n                  onCardClick={handleCardClick}\n                  onAddCard={handleAddCard}\n                  onEditColumn={handleEditColumn}\n                  onDeleteColumn={handleDeleteColumn}\n                  members={members}\n                />\n              ))}\n\n            {/* Add Column Button */}\n            <div className=\"flex-shrink-0\">\n              <Button\n                variant=\"outline\"\n                onClick={() => setShowAddColumnModal(true)}\n                className=\"w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors\"\n                iconName=\"Plus\"\n                iconPosition=\"left\"\n              >\n                Add Column\n              </Button>\n            </div>\n          </div>\n\n          {/* Empty State */}\n          {filteredCards.length === 0 && searchQuery && (\n            <div className=\"flex flex-col items-center justify-center py-12\">\n              <Icon name=\"Search\" size={48} className=\"text-text-secondary mb-4\" />\n              <h3 className=\"text-lg font-medium text-text-primary mb-2\">No cards found</h3>\n              <p className=\"text-text-secondary text-center max-w-md\">\n                No cards match your search criteria. Try adjusting your search terms or filters.\n              </p>\n              <Button\n                variant=\"outline\"\n                onClick={() => {\n                  setSearchQuery('');\n                  setActiveFilters({});\n                }}\n                className=\"mt-4\"\n              >\n                Clear Search & Filters\n              </Button>\n            </div>\n          )}\n        </div>\n\n        {/* Modals */}\n        <AddCardModal\n          isOpen={showAddCardModal}\n          onClose={() => {\n            setShowAddCardModal(false);\n            setSelectedColumnId(null);\n          }}\n          onSave={handleSaveCard}\n          columnId={selectedColumnId}\n          members={members}\n        />\n\n        <AddColumnModal\n          isOpen={showAddColumnModal}\n          onClose={() => setShowAddColumnModal(false)}\n          onSave={handleSaveColumn}\n        />\n\n        <InviteMemberModal\n          isOpen={showInviteMemberModal}\n          onClose={() => setShowInviteMemberModal(false)}\n          onInvite={handleSendInvitation}\n        />\n      </div>\n    </DndProvider>\n  );\n};\n\nexport default KanbanBoard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,WAAW;AACvC,SAASC,YAAY,QAAQ,yBAAyB;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,WAAW,MAAM,0BAA0B;AAElD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,cAAc,MAAM,6BAA6B;AACxD,OAAOC,iBAAiB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,QAAQ,GAAGd,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAM,CAACe,KAAK,CAAC,GAAGnB,QAAQ,CAAC;IACvBoB,EAAE,EAAE,SAAS;IACbC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,gDAAgD;IAC7DC,SAAS,EAAE,KAAK;IAChBC,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,CAAC,GAAG1B,QAAQ,CAAC,CACzB;IACEoB,EAAE,EAAE,QAAQ;IACZO,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,2BAA2B;IAClCC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE;EACV,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZO,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,0BAA0B;IACjCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZO,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,6BAA6B;IACpCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZO,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE;EACV,CAAC,EACD;IACEV,EAAE,EAAE,QAAQ;IACZO,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE;EACV,CAAC,CACF,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,CACrC;IACEoB,EAAE,EAAE,OAAO;IACXC,KAAK,EAAE,OAAO;IACdY,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,CAAC;IACRV,SAAS,EAAE;EACb,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,KAAK,EAAE,aAAa;IACpBY,MAAM,EAAE,aAAa;IACrBC,KAAK,EAAE,CAAC;IACRV,SAAS,EAAE;EACb,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,KAAK,EAAE,QAAQ;IACfY,MAAM,EAAE,QAAQ;IAChBC,KAAK,EAAE,CAAC;IACRV,SAAS,EAAE;EACb,CAAC,EACD;IACEJ,EAAE,EAAE,OAAO;IACXC,KAAK,EAAE,MAAM;IACbY,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,CAAC;IACRV,SAAS,EAAE;EACb,CAAC,CACF,CAAC;EAEF,MAAM,CAACW,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAC,CACjC;IACEoB,EAAE,EAAE,QAAQ;IACZiB,QAAQ,EAAE,OAAO;IACjBhB,KAAK,EAAE,iCAAiC;IACxCC,WAAW,EAAE,sEAAsE;IACnFgB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAErB,EAAE,EAAE,QAAQ;MAAEO,IAAI,EAAE,QAAQ;MAAEe,KAAK,EAAE;IAAU,CAAC,EAClD;MAAEtB,EAAE,EAAE,IAAI;MAAEO,IAAI,EAAE,IAAI;MAAEe,KAAK,EAAE;IAAU,CAAC,CAC3C;IACDC,SAAS,EAAE,CACT;MAAEvB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,2BAA2B;MAAEC,SAAS,EAAE;IAAK,CAAC,EACrE;MAAEzB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,mBAAmB;MAAEC,SAAS,EAAE;IAAM,CAAC,EAC9D;MAAEzB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,gBAAgB;MAAEC,SAAS,EAAE;IAAM,CAAC,CAC5D;IACDC,QAAQ,EAAE,CACR;MACE1B,EAAE,EAAE,WAAW;MACf2B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,yCAAyC;MAClDxB,SAAS,EAAE;IACb,CAAC,CACF;IACDyB,WAAW,EAAE,EAAE;IACfzB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZiB,QAAQ,EAAE,OAAO;IACjBhB,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,0EAA0E;IACvFgB,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtBC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAErB,EAAE,EAAE,aAAa;MAAEO,IAAI,EAAE,aAAa;MAAEe,KAAK,EAAE;IAAU,CAAC,CAC7D;IACDC,SAAS,EAAE,EAAE;IACbG,QAAQ,EAAE,EAAE;IACZG,WAAW,EAAE,EAAE;IACfzB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZiB,QAAQ,EAAE,OAAO;IACjBhB,KAAK,EAAE,iCAAiC;IACxCC,WAAW,EAAE,sFAAsF;IACnGgB,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAErB,EAAE,EAAE,SAAS;MAAEO,IAAI,EAAE,SAAS;MAAEe,KAAK,EAAE;IAAU,CAAC,EACpD;MAAEtB,EAAE,EAAE,KAAK;MAAEO,IAAI,EAAE,KAAK;MAAEe,KAAK,EAAE;IAAU,CAAC,CAC7C;IACDC,SAAS,EAAE,CACT;MAAEvB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,wBAAwB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAClE;MAAEzB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAChE;MAAEzB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,wBAAwB;MAAEC,SAAS,EAAE;IAAM,CAAC,EACnE;MAAEzB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,kBAAkB;MAAEC,SAAS,EAAE;IAAM,CAAC,CAC9D;IACDC,QAAQ,EAAE,CACR;MACE1B,EAAE,EAAE,WAAW;MACf2B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,4CAA4C;MACrDxB,SAAS,EAAE;IACb,CAAC,EACD;MACEJ,EAAE,EAAE,WAAW;MACf2B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,0CAA0C;MACnDxB,SAAS,EAAE;IACb,CAAC,CACF;IACDyB,WAAW,EAAE,CACX;MAAE7B,EAAE,EAAE,OAAO;MAAEO,IAAI,EAAE,cAAc;MAAEuB,IAAI,EAAE;IAAS,CAAC,CACtD;IACD1B,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZiB,QAAQ,EAAE,OAAO;IACjBhB,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,iDAAiD;IAC9DgB,QAAQ,EAAE,QAAQ;IAClBC,UAAU,EAAE,CAAC,QAAQ,EAAE,QAAQ,CAAC;IAChCC,OAAO,EAAE,YAAY;IACrBC,MAAM,EAAE,CACN;MAAErB,EAAE,EAAE,QAAQ;MAAEO,IAAI,EAAE,QAAQ;MAAEe,KAAK,EAAE;IAAU,CAAC,EAClD;MAAEtB,EAAE,EAAE,UAAU;MAAEO,IAAI,EAAE,UAAU;MAAEe,KAAK,EAAE;IAAU,CAAC,CACvD;IACDC,SAAS,EAAE,CACT;MAAEvB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,oBAAoB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC9D;MAAEzB,EAAE,EAAE,SAAS;MAAEwB,IAAI,EAAE,qBAAqB;MAAEC,SAAS,EAAE;IAAM,CAAC,EAChE;MAAEzB,EAAE,EAAE,UAAU;MAAEwB,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAM,CAAC,CACnE;IACDC,QAAQ,EAAE,EAAE;IACZG,WAAW,EAAE,EAAE;IACfzB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACEL,EAAE,EAAE,QAAQ;IACZiB,QAAQ,EAAE,OAAO;IACjBhB,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,0DAA0D;IACvEgB,QAAQ,EAAE,KAAK;IACfC,UAAU,EAAE,CAAC,QAAQ,CAAC;IACtBC,OAAO,EAAE,IAAI;IACbC,MAAM,EAAE,CACN;MAAErB,EAAE,EAAE,eAAe;MAAEO,IAAI,EAAE,eAAe;MAAEe,KAAK,EAAE;IAAU,CAAC,CACjE;IACDC,SAAS,EAAE,CACT;MAAEvB,EAAE,EAAE,UAAU;MAAEwB,IAAI,EAAE,eAAe;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC1D;MAAEzB,EAAE,EAAE,UAAU;MAAEwB,IAAI,EAAE,iBAAiB;MAAEC,SAAS,EAAE;IAAK,CAAC,EAC5D;MAAEzB,EAAE,EAAE,UAAU;MAAEwB,IAAI,EAAE,sBAAsB;MAAEC,SAAS,EAAE;IAAK,CAAC,CAClE;IACDC,QAAQ,EAAE,CACR;MACE1B,EAAE,EAAE,WAAW;MACf2B,MAAM,EAAE,QAAQ;MAChBC,OAAO,EAAE,kCAAkC;MAC3CxB,SAAS,EAAE;IACb,CAAC,CACF;IACDyB,WAAW,EAAE,EAAE;IACfzB,SAAS,EAAE,sBAAsB;IACjCC,SAAS,EAAE;EACb,CAAC,CACF,CAAC;;EAEF;EACA,MAAM,CAAC0B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAACqD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGtD,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAACuD,qBAAqB,EAAEC,wBAAwB,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EACzE,MAAM,CAACyD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG1D,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAM,CAAC2D,WAAW,EAAEC,cAAc,CAAC,GAAG5D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6D,aAAa,EAAEC,gBAAgB,CAAC,GAAG9D,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEtD;EACA,MAAM+D,aAAa,GAAG5B,KAAK,CAAC6B,MAAM,CAACC,IAAI,IAAI;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA;IACzC;IACA,IAAIT,WAAW,EAAE;MAAA,IAAAU,YAAA;MACf,MAAMC,KAAK,GAAGX,WAAW,CAACY,WAAW,CAAC,CAAC;MACvC,MAAMC,aAAa,GACjBP,IAAI,CAAC5C,KAAK,CAACkD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,KAAK,CAAC,IACxCL,IAAI,CAAC3C,WAAW,CAACiD,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,KAAK,CAAC,MAAAD,YAAA,GAC9CJ,IAAI,CAACxB,MAAM,cAAA4B,YAAA,uBAAXA,YAAA,CAAaK,IAAI,CAACC,KAAK,IAAIA,KAAK,CAAChD,IAAI,CAAC4C,WAAW,CAAC,CAAC,CAACE,QAAQ,CAACH,KAAK,CAAC,CAAC;MAEtE,IAAI,CAACE,aAAa,EAAE,OAAO,KAAK;IAClC;;IAEA;IACA,IAAI,EAAAN,qBAAA,GAAAL,aAAa,CAACvB,QAAQ,cAAA4B,qBAAA,uBAAtBA,qBAAA,CAAwBU,MAAM,IAAG,CAAC,EAAE;MACtC,IAAI,CAACf,aAAa,CAACvB,QAAQ,CAACmC,QAAQ,CAACR,IAAI,CAAC3B,QAAQ,CAAC,EAAE,OAAO,KAAK;IACnE;;IAEA;IACA,IAAI,EAAA6B,qBAAA,GAAAN,aAAa,CAACgB,QAAQ,cAAAV,qBAAA,uBAAtBA,qBAAA,CAAwBS,MAAM,IAAG,CAAC,EAAE;MAAA,IAAAE,gBAAA;MACtC,MAAMC,WAAW,IAAAD,gBAAA,GAAGb,IAAI,CAAC1B,UAAU,cAAAuC,gBAAA,uBAAfA,gBAAA,CAAiBJ,IAAI,CAACM,UAAU,IAClDnB,aAAa,CAACgB,QAAQ,CAACJ,QAAQ,CAACO,UAAU,CAC5C,CAAC;MACD,IAAI,CAACD,WAAW,EAAE,OAAO,KAAK;IAChC;;IAEA;IACA,IAAI,EAAAX,qBAAA,GAAAP,aAAa,CAACrB,OAAO,cAAA4B,qBAAA,uBAArBA,qBAAA,CAAuBQ,MAAM,IAAG,CAAC,EAAE;MACrC,MAAMK,KAAK,GAAG,IAAIC,IAAI,CAAC,CAAC;MACxB,MAAMC,WAAW,GAAGlB,IAAI,CAACzB,OAAO,GAAG,IAAI0C,IAAI,CAACjB,IAAI,CAACzB,OAAO,CAAC,GAAG,IAAI;MAEhE,MAAM4C,cAAc,GAAGvB,aAAa,CAACrB,OAAO,CAACkC,IAAI,CAACV,MAAM,IAAI;QAC1D,IAAIA,MAAM,KAAK,SAAS,EAAE;UACxB,OAAOmB,WAAW,IAAIA,WAAW,GAAGF,KAAK;QAC3C;QACA,IAAIjB,MAAM,KAAK,OAAO,EAAE;UACtB,OAAOmB,WAAW,IAAIA,WAAW,CAACE,YAAY,CAAC,CAAC,KAAKJ,KAAK,CAACI,YAAY,CAAC,CAAC;QAC3E;QACA,IAAIrB,MAAM,KAAK,WAAW,EAAE;UAC1B,MAAMsB,WAAW,GAAG,IAAIJ,IAAI,CAACD,KAAK,CAACM,OAAO,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;UACvE,OAAOJ,WAAW,IAAIA,WAAW,IAAIF,KAAK,IAAIE,WAAW,IAAIG,WAAW;QAC1E;QACA,IAAItB,MAAM,KAAK,YAAY,EAAE;UAC3B,MAAMwB,YAAY,GAAG,IAAIN,IAAI,CAACD,KAAK,CAACQ,WAAW,CAAC,CAAC,EAAER,KAAK,CAACS,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAET,KAAK,CAACU,OAAO,CAAC,CAAC,CAAC;UACzF,OAAOR,WAAW,IAAIA,WAAW,IAAIF,KAAK,IAAIE,WAAW,IAAIK,YAAY;QAC3E;QACA,OAAO,KAAK;MACd,CAAC,CAAC;MAEF,IAAI,CAACJ,cAAc,EAAE,OAAO,KAAK;IACnC;IAEA,OAAO,IAAI;EACb,CAAC,CAAC;;EAEF;EACA,MAAMQ,cAAc,GAAGA,CAACC,MAAM,EAAEC,cAAc,EAAEC,cAAc,KAAK;IACjE3D,QAAQ,CAAC4D,SAAS,IAChBA,SAAS,CAACC,GAAG,CAAChC,IAAI,IAChBA,IAAI,CAAC7C,EAAE,KAAKyE,MAAM,GACd;MAAE,GAAG5B,IAAI;MAAE5B,QAAQ,EAAE0D,cAAc;MAAEtE,SAAS,EAAE,IAAIyD,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC;IAAE,CAAC,GAC1EjC,IACN,CACF,CAAC;EACH,CAAC;;EAED;EACA,MAAMkC,eAAe,GAAIlC,IAAI,IAAK;IAChC/C,QAAQ,CAAC,eAAe,EAAE;MAAEkF,KAAK,EAAE;QAAEnC,IAAI;QAAEvC;MAAQ;IAAE,CAAC,CAAC;EACzD,CAAC;;EAED;EACA,MAAM2E,aAAa,GAAIhE,QAAQ,IAAK;IAClCqB,mBAAmB,CAACrB,QAAQ,CAAC;IAC7Be,mBAAmB,CAAC,IAAI,CAAC;EAC3B,CAAC;EAED,MAAMkD,cAAc,GAAIC,OAAO,IAAK;IAClCnE,QAAQ,CAAC4D,SAAS,IAAI,CAAC,GAAGA,SAAS,EAAEO,OAAO,CAAC,CAAC;EAChD,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAIC,SAAS,IAAK;IACtCzE,UAAU,CAAC0E,WAAW,IAAI,CAAC,GAAGA,WAAW,EAAED,SAAS,CAAC,CAAC;EACxD,CAAC;;EAED;EACA,MAAME,gBAAgB,GAAGA,CAACtE,QAAQ,EAAEuE,OAAO,KAAK;IAC9C5E,UAAU,CAAC0E,WAAW,IACpBA,WAAW,CAACT,GAAG,CAACY,GAAG,IACjBA,GAAG,CAACzF,EAAE,KAAKiB,QAAQ,GACf;MAAE,GAAGwE,GAAG;MAAE,GAAGD,OAAO;MAAEnF,SAAS,EAAE,IAAIyD,IAAI,CAAC,CAAC,CAACgB,WAAW,CAAC;IAAE,CAAC,GAC3DW,GACN,CACF,CAAC;EACH,CAAC;EAED,MAAMC,kBAAkB,GAAIzE,QAAQ,IAAK;IAAA,IAAA0E,SAAA;IACvC;IACA,MAAMC,aAAa,IAAAD,SAAA,GAAGhF,OAAO,CAAC,CAAC,CAAC,cAAAgF,SAAA,uBAAVA,SAAA,CAAY3F,EAAE;IACpC,IAAI4F,aAAa,IAAIA,aAAa,KAAK3E,QAAQ,EAAE;MAC/CD,QAAQ,CAAC4D,SAAS,IAChBA,SAAS,CAACC,GAAG,CAAChC,IAAI,IAChBA,IAAI,CAAC5B,QAAQ,KAAKA,QAAQ,GACtB;QAAE,GAAG4B,IAAI;QAAE5B,QAAQ,EAAE2E;MAAc,CAAC,GACpC/C,IACN,CACF,CAAC;IACH;IAEAjC,UAAU,CAAC0E,WAAW,IAAIA,WAAW,CAAC1C,MAAM,CAAC6C,GAAG,IAAIA,GAAG,CAACzF,EAAE,KAAKiB,QAAQ,CAAC,CAAC;EAC3E,CAAC;;EAED;EACA,MAAM4E,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzD,wBAAwB,CAAC,IAAI,CAAC;EAChC,CAAC;EAED,MAAM0D,oBAAoB,GAAIC,UAAU,IAAK;IAC3CC,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEF,UAAU,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIV,OAAO,IAAK;IACrCQ,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAET,OAAO,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMW,iBAAiB,GAAIlF,QAAQ,IAAK;IACtC,OAAO0B,aAAa,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAAC5B,QAAQ,KAAKA,QAAQ,CAAC;EACjE,CAAC;EAED,oBACEtB,OAAA,CAACb,WAAW;IAACsH,OAAO,EAAErH,YAAa;IAAAsH,QAAA,eACjC1G,OAAA;MAAK2G,SAAS,EAAC,4BAA4B;MAAAD,QAAA,gBAEzC1G,OAAA,CAACN,WAAW;QACVU,KAAK,EAAEA,KAAM;QACbO,OAAO,EAAEA,OAAQ;QACjBiG,aAAa,EAAEL,iBAAkB;QACjCM,cAAc,EAAEX,kBAAmB;QACnCY,cAAc,EAAE/D,gBAAiB;QACjCgE,cAAc,EAAElE,cAAe;QAC/BD,WAAW,EAAEA,WAAY;QACzBE,aAAa,EAAEA;MAAc;QAAAkE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eAGFnH,OAAA;QAAK2G,SAAS,EAAC,YAAY;QAAAD,QAAA,gBACzB1G,OAAA;UAAK2G,SAAS,EAAC,qCAAqC;UAAAD,QAAA,GAEjD1F,OAAO,CACLoG,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAAClG,KAAK,GAAGmG,CAAC,CAACnG,KAAK,CAAC,CACjC+D,GAAG,CAACqC,MAAM,iBACTvH,OAAA,CAACL,WAAW;YAEV4H,MAAM,EAAEA,MAAO;YACfnG,KAAK,EAAEoF,iBAAiB,CAACe,MAAM,CAAClH,EAAE,CAAE;YACpCmH,UAAU,EAAE3C,cAAe;YAC3B4C,WAAW,EAAErC,eAAgB;YAC7BsC,SAAS,EAAEpC,aAAc;YACzBqC,YAAY,EAAE/B,gBAAiB;YAC/BgC,cAAc,EAAE7B,kBAAmB;YACnCpF,OAAO,EAAEA;UAAQ,GARZ4G,MAAM,CAAClH,EAAE;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASf,CACF,CAAC,eAGJnH,OAAA;YAAK2G,SAAS,EAAC,eAAe;YAAAD,QAAA,eAC5B1G,OAAA,CAACP,MAAM;cACLoI,OAAO,EAAC,SAAS;cACjBC,OAAO,EAAEA,CAAA,KAAMvF,qBAAqB,CAAC,IAAI,CAAE;cAC3CoE,SAAS,EAAC,0GAA0G;cACpHoB,QAAQ,EAAC,MAAM;cACfC,YAAY,EAAC,MAAM;cAAAtB,QAAA,EACpB;YAED;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAGLnE,aAAa,CAACa,MAAM,KAAK,CAAC,IAAIjB,WAAW,iBACxC5C,OAAA;UAAK2G,SAAS,EAAC,iDAAiD;UAAAD,QAAA,gBAC9D1G,OAAA,CAACR,IAAI;YAACoB,IAAI,EAAC,QAAQ;YAACuB,IAAI,EAAE,EAAG;YAACwE,SAAS,EAAC;UAA0B;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACrEnH,OAAA;YAAI2G,SAAS,EAAC,4CAA4C;YAAAD,QAAA,EAAC;UAAc;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC9EnH,OAAA;YAAG2G,SAAS,EAAC,0CAA0C;YAAAD,QAAA,EAAC;UAExD;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJnH,OAAA,CAACP,MAAM;YACLoI,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAEA,CAAA,KAAM;cACbjF,cAAc,CAAC,EAAE,CAAC;cAClBE,gBAAgB,CAAC,CAAC,CAAC,CAAC;YACtB,CAAE;YACF4D,SAAS,EAAC,MAAM;YAAAD,QAAA,EACjB;UAED;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNnH,OAAA,CAACJ,YAAY;QACXqI,MAAM,EAAE7F,gBAAiB;QACzB8F,OAAO,EAAEA,CAAA,KAAM;UACb7F,mBAAmB,CAAC,KAAK,CAAC;UAC1BM,mBAAmB,CAAC,IAAI,CAAC;QAC3B,CAAE;QACFwF,MAAM,EAAE5C,cAAe;QACvBjE,QAAQ,EAAEoB,gBAAiB;QAC3B/B,OAAO,EAAEA;MAAQ;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,eAEFnH,OAAA,CAACH,cAAc;QACboI,MAAM,EAAE3F,kBAAmB;QAC3B4F,OAAO,EAAEA,CAAA,KAAM3F,qBAAqB,CAAC,KAAK,CAAE;QAC5C4F,MAAM,EAAE1C;MAAiB;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1B,CAAC,eAEFnH,OAAA,CAACF,iBAAiB;QAChBmI,MAAM,EAAEzF,qBAAsB;QAC9B0F,OAAO,EAAEA,CAAA,KAAMzF,wBAAwB,CAAC,KAAK,CAAE;QAC/C2F,QAAQ,EAAEjC;MAAqB;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAElB,CAAC;AAACjH,EAAA,CA5cID,WAAW;EAAA,QACEZ,WAAW;AAAA;AAAAgJ,EAAA,GADxBpI,WAAW;AA8cjB,eAAeA,WAAW;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}