{"ast": null, "code": "// src/utils/apiService.js\n\n// API service for connecting to the backend\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && {\n      'X-Organization-ID': organizationId\n    })\n  };\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async response => {\n  const result = await response.json();\n  if (!response.ok) {\n    var _result$error;\n    throw new Error(((_result$error = result.error) === null || _result$error === void 0 ? void 0 : _result$error.message) || result.message || 'API request failed');\n  }\n  return result;\n};\nconst apiService = {\n  // Organizations\n  organizations: {\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to fetch organizations:', error);\n        throw error;\n      }\n    },\n    create: async orgData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/v1/organizations`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(orgData)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to create organization:', error);\n        throw error;\n      }\n    },\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch organization:', error);\n        throw error;\n      }\n    },\n    update: async (id, updateData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData)\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update organization:', error);\n        throw error;\n      }\n    },\n    delete: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to delete organization:', error);\n        throw error;\n      }\n    },\n    getMembers: async (id, params = {}) => {\n      try {\n        const queryString = new URLSearchParams(params).toString();\n        const url = `${API_BASE_URL}/v1/organizations/${id}/members${queryString ? `?${queryString}` : ''}`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to fetch organization members:', error);\n        throw error;\n      }\n    },\n    inviteMember: async (id, inviteData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/v1/organizations/${id}/invite`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(inviteData)\n        });\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Failed to invite member:', error);\n        throw error;\n      }\n    }\n  },\n  // Projects\n  projects: {\n    getAll: async (organizationId, params = {}) => {\n      try {\n        const queryParams = organizationId ? {\n          organization_id: organizationId,\n          ...params\n        } : params;\n        const queryString = new URLSearchParams(queryParams).toString();\n        const url = `${API_BASE_URL}/v1/projects${queryString ? `?${queryString}` : ''}`;\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to fetch projects:', error);\n        throw error;\n      }\n    },\n    create: async (organizationId, projectData) => {\n      try {\n        const dataWithOrg = {\n          ...projectData,\n          organization_id: organizationId\n        };\n        const response = await fetch(`${API_BASE_URL}/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(dataWithOrg)\n        });\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to create project:', error);\n        throw error;\n      }\n    },\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch project:', error);\n        throw error;\n      }\n    },\n    update: async (id, updateData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData)\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update project:', error);\n        throw error;\n      }\n    },\n    delete: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to delete project:', error);\n        throw error;\n      }\n    }\n  },\n  // Boards\n  boards: {\n    getByProject: async projectId => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${projectId}/boards`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch boards:', error);\n        throw error;\n      }\n    },\n    getById: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch board:', error);\n        throw error;\n      }\n    },\n    create: async (projectId, boardData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${projectId}/boards`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(boardData)\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to create board:', error);\n        throw error;\n      }\n    },\n    update: async (id, updateData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData)\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update board:', error);\n        throw error;\n      }\n    },\n    delete: async id => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to delete board:', error);\n        throw error;\n      }\n    }\n  },\n  // Users\n  users: {\n    getProfile: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/users/profile`, {\n          method: 'GET',\n          headers: getAuthHeaders()\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch user profile:', error);\n        throw error;\n      }\n    },\n    updateProfile: async updateData => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/users/profile`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData)\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update user profile:', error);\n        throw error;\n      }\n    },\n    uploadAvatar: async file => {\n      try {\n        const formData = new FormData();\n        formData.append('avatar', file);\n        const token = localStorage.getItem('accessToken');\n        const response = await fetch(`${API_BASE_URL}/users/avatar`, {\n          method: 'POST',\n          headers: {\n            ...(token && {\n              'Authorization': `Bearer ${token}`\n            })\n          },\n          body: formData\n        });\n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to upload avatar:', error);\n        throw error;\n      }\n    }\n  }\n};\nexport default apiService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "getAuthHeaders", "organizationId", "token", "localStorage", "getItem", "headers", "handleResponse", "response", "result", "json", "ok", "_result$error", "Error", "error", "message", "apiService", "organizations", "getAll", "fetch", "method", "data", "console", "create", "orgData", "body", "JSON", "stringify", "getById", "id", "update", "updateData", "delete", "getMembers", "params", "queryString", "URLSearchParams", "toString", "url", "inviteMember", "inviteData", "projects", "queryParams", "organization_id", "projectData", "dataWithOrg", "boards", "getByProject", "projectId", "boardData", "users", "getProfile", "updateProfile", "uploadAvatar", "file", "formData", "FormData", "append"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/apiService.js"], "sourcesContent": ["// src/utils/apiService.js\n\n// API service for connecting to the backend\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// Helper function to get headers with authentication\nconst getAuthHeaders = (organizationId = null) => {\n  const token = localStorage.getItem('accessToken');\n  const headers = {\n    'Content-Type': 'application/json',\n    ...(organizationId && { 'X-Organization-ID': organizationId })\n  };\n\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n\n  return headers;\n};\n\n// Helper function to handle API responses\nconst handleResponse = async (response) => {\n  const result = await response.json();\n\n  if (!response.ok) {\n    throw new Error(result.error?.message || result.message || 'API request failed');\n  }\n\n  return result;\n};\n\nconst apiService = {\n  // Organizations\n  organizations: {\n    getAll: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/v1/organizations`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to fetch organizations:', error);\n        throw error;\n      }\n    },\n\n    create: async (orgData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/v1/organizations`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(orgData),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to create organization:', error);\n        throw error;\n      }\n    },\n\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch organization:', error);\n        throw error;\n      }\n    },\n\n    update: async (id, updateData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update organization:', error);\n        throw error;\n      }\n    },\n\n    delete: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to delete organization:', error);\n        throw error;\n      }\n    },\n\n    getMembers: async (id, params = {}) => {\n      try {\n        const queryString = new URLSearchParams(params).toString();\n        const url = `${API_BASE_URL}/v1/organizations/${id}/members${queryString ? `?${queryString}` : ''}`;\n\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to fetch organization members:', error);\n        throw error;\n      }\n    },\n\n    inviteMember: async (id, inviteData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/v1/organizations/${id}/invite`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(inviteData),\n        });\n\n        const result = await handleResponse(response);\n        return result;\n      } catch (error) {\n        console.error('Failed to invite member:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Projects\n  projects: {\n    getAll: async (organizationId, params = {}) => {\n      try {\n        const queryParams = organizationId ? { organization_id: organizationId, ...params } : params;\n        const queryString = new URLSearchParams(queryParams).toString();\n        const url = `${API_BASE_URL}/v1/projects${queryString ? `?${queryString}` : ''}`;\n\n        const response = await fetch(url, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to fetch projects:', error);\n        throw error;\n      }\n    },\n\n    create: async (organizationId, projectData) => {\n      try {\n        const dataWithOrg = {\n          ...projectData,\n          organization_id: organizationId\n        };\n\n        const response = await fetch(`${API_BASE_URL}/v1/projects`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(dataWithOrg),\n        });\n\n        const result = await handleResponse(response);\n        return result.data;\n      } catch (error) {\n        console.error('Failed to create project:', error);\n        throw error;\n      }\n    },\n\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch project:', error);\n        throw error;\n      }\n    },\n\n    update: async (id, updateData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update project:', error);\n        throw error;\n      }\n    },\n\n    delete: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to delete project:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Boards\n  boards: {\n    getByProject: async (projectId) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${projectId}/boards`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch boards:', error);\n        throw error;\n      }\n    },\n\n    getById: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch board:', error);\n        throw error;\n      }\n    },\n\n    create: async (projectId, boardData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/projects/${projectId}/boards`, {\n          method: 'POST',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(boardData),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to create board:', error);\n        throw error;\n      }\n    },\n\n    update: async (id, updateData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update board:', error);\n        throw error;\n      }\n    },\n\n    delete: async (id) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {\n          method: 'DELETE',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to delete board:', error);\n        throw error;\n      }\n    }\n  },\n\n  // Users\n  users: {\n    getProfile: async () => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/users/profile`, {\n          method: 'GET',\n          headers: getAuthHeaders(),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to fetch user profile:', error);\n        throw error;\n      }\n    },\n\n    updateProfile: async (updateData) => {\n      try {\n        const response = await fetch(`${API_BASE_URL}/users/profile`, {\n          method: 'PUT',\n          headers: getAuthHeaders(),\n          body: JSON.stringify(updateData),\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to update user profile:', error);\n        throw error;\n      }\n    },\n\n    uploadAvatar: async (file) => {\n      try {\n        const formData = new FormData();\n        formData.append('avatar', file);\n\n        const token = localStorage.getItem('accessToken');\n        const response = await fetch(`${API_BASE_URL}/users/avatar`, {\n          method: 'POST',\n          headers: {\n            ...(token && { 'Authorization': `Bearer ${token}` })\n          },\n          body: formData,\n        });\n        \n        return await handleResponse(response);\n      } catch (error) {\n        console.error('Failed to upload avatar:', error);\n        throw error;\n      }\n    }\n  }\n};\n\nexport default apiService;\n"], "mappings": "AAAA;;AAEA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,cAAc,GAAGA,CAACC,cAAc,GAAG,IAAI,KAAK;EAChD,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EACjD,MAAMC,OAAO,GAAG;IACd,cAAc,EAAE,kBAAkB;IAClC,IAAIJ,cAAc,IAAI;MAAE,mBAAmB,EAAEA;IAAe,CAAC;EAC/D,CAAC;EAED,IAAIC,KAAK,EAAE;IACTG,OAAO,CAAC,eAAe,CAAC,GAAG,UAAUH,KAAK,EAAE;EAC9C;EAEA,OAAOG,OAAO;AAChB,CAAC;;AAED;AACA,MAAMC,cAAc,GAAG,MAAOC,QAAQ,IAAK;EACzC,MAAMC,MAAM,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;EAEpC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;IAAA,IAAAC,aAAA;IAChB,MAAM,IAAIC,KAAK,CAAC,EAAAD,aAAA,GAAAH,MAAM,CAACK,KAAK,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,OAAO,KAAIN,MAAM,CAACM,OAAO,IAAI,oBAAoB,CAAC;EAClF;EAEA,OAAON,MAAM;AACf,CAAC;AAED,MAAMO,UAAU,GAAG;EACjB;EACAC,aAAa,EAAE;IACbC,MAAM,EAAE,MAAAA,CAAA,KAAY;MAClB,IAAI;QACF,MAAMV,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,mBAAmB,EAAE;UAC/DuB,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACY,IAAI;MACpB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDS,MAAM,EAAE,MAAOC,OAAO,IAAK;MACzB,IAAI;QACF,MAAMhB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,mBAAmB,EAAE;UAC/DuB,MAAM,EAAE,MAAM;UACdd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACH,OAAO;QAC9B,CAAC,CAAC;QAEF,MAAMf,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACY,IAAI;MACpB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDc,OAAO,EAAE,MAAOC,EAAE,IAAK;MACrB,IAAI;QACF,MAAMrB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,kBAAkBgC,EAAE,EAAE,EAAE;UAClET,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDgB,MAAM,EAAE,MAAAA,CAAOD,EAAE,EAAEE,UAAU,KAAK;MAChC,IAAI;QACF,MAAMvB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,kBAAkBgC,EAAE,EAAE,EAAE;UAClET,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACI,UAAU;QACjC,CAAC,CAAC;QAEF,OAAO,MAAMxB,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDkB,MAAM,EAAE,MAAOH,EAAE,IAAK;MACpB,IAAI;QACF,MAAMrB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,kBAAkBgC,EAAE,EAAE,EAAE;UAClET,MAAM,EAAE,QAAQ;UAChBd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDmB,UAAU,EAAE,MAAAA,CAAOJ,EAAE,EAAEK,MAAM,GAAG,CAAC,CAAC,KAAK;MACrC,IAAI;QACF,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACF,MAAM,CAAC,CAACG,QAAQ,CAAC,CAAC;QAC1D,MAAMC,GAAG,GAAG,GAAGzC,YAAY,qBAAqBgC,EAAE,WAAWM,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE;QAEnG,MAAM3B,QAAQ,GAAG,MAAMW,KAAK,CAACmB,GAAG,EAAE;UAChClB,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACY,IAAI;MACpB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QAC7D,MAAMA,KAAK;MACb;IACF,CAAC;IAEDyB,YAAY,EAAE,MAAAA,CAAOV,EAAE,EAAEW,UAAU,KAAK;MACtC,IAAI;QACF,MAAMhC,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,qBAAqBgC,EAAE,SAAS,EAAE;UAC5ET,MAAM,EAAE,MAAM;UACdd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACa,UAAU;QACjC,CAAC,CAAC;QAEF,MAAM/B,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM;MACf,CAAC,CAAC,OAAOK,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACA2B,QAAQ,EAAE;IACRvB,MAAM,EAAE,MAAAA,CAAOhB,cAAc,EAAEgC,MAAM,GAAG,CAAC,CAAC,KAAK;MAC7C,IAAI;QACF,MAAMQ,WAAW,GAAGxC,cAAc,GAAG;UAAEyC,eAAe,EAAEzC,cAAc;UAAE,GAAGgC;QAAO,CAAC,GAAGA,MAAM;QAC5F,MAAMC,WAAW,GAAG,IAAIC,eAAe,CAACM,WAAW,CAAC,CAACL,QAAQ,CAAC,CAAC;QAC/D,MAAMC,GAAG,GAAG,GAAGzC,YAAY,eAAesC,WAAW,GAAG,IAAIA,WAAW,EAAE,GAAG,EAAE,EAAE;QAEhF,MAAM3B,QAAQ,GAAG,MAAMW,KAAK,CAACmB,GAAG,EAAE;UAChClB,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,MAAMQ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACY,IAAI;MACpB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDS,MAAM,EAAE,MAAAA,CAAOrB,cAAc,EAAE0C,WAAW,KAAK;MAC7C,IAAI;QACF,MAAMC,WAAW,GAAG;UAClB,GAAGD,WAAW;UACdD,eAAe,EAAEzC;QACnB,CAAC;QAED,MAAMM,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,cAAc,EAAE;UAC1DuB,MAAM,EAAE,MAAM;UACdd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACkB,WAAW;QAClC,CAAC,CAAC;QAEF,MAAMpC,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;QAC7C,OAAOC,MAAM,CAACY,IAAI;MACpB,CAAC,CAAC,OAAOP,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDc,OAAO,EAAE,MAAOC,EAAE,IAAK;MACrB,IAAI;QACF,MAAMrB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,aAAagC,EAAE,EAAE,EAAE;UAC7DT,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDgB,MAAM,EAAE,MAAAA,CAAOD,EAAE,EAAEE,UAAU,KAAK;MAChC,IAAI;QACF,MAAMvB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,aAAagC,EAAE,EAAE,EAAE;UAC7DT,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACI,UAAU;QACjC,CAAC,CAAC;QAEF,OAAO,MAAMxB,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDkB,MAAM,EAAE,MAAOH,EAAE,IAAK;MACpB,IAAI;QACF,MAAMrB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,aAAagC,EAAE,EAAE,EAAE;UAC7DT,MAAM,EAAE,QAAQ;UAChBd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjD,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAgC,MAAM,EAAE;IACNC,YAAY,EAAE,MAAOC,SAAS,IAAK;MACjC,IAAI;QACF,MAAMxC,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,aAAamD,SAAS,SAAS,EAAE;UAC3E5B,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDc,OAAO,EAAE,MAAOC,EAAE,IAAK;MACrB,IAAI;QACF,MAAMrB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,WAAWgC,EAAE,EAAE,EAAE;UAC3DT,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDS,MAAM,EAAE,MAAAA,CAAOyB,SAAS,EAAEC,SAAS,KAAK;MACtC,IAAI;QACF,MAAMzC,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,aAAamD,SAAS,SAAS,EAAE;UAC3E5B,MAAM,EAAE,MAAM;UACdd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACsB,SAAS;QAChC,CAAC,CAAC;QAEF,OAAO,MAAM1C,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDgB,MAAM,EAAE,MAAAA,CAAOD,EAAE,EAAEE,UAAU,KAAK;MAChC,IAAI;QACF,MAAMvB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,WAAWgC,EAAE,EAAE,EAAE;UAC3DT,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACI,UAAU;QACjC,CAAC,CAAC;QAEF,OAAO,MAAMxB,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF,CAAC;IAEDkB,MAAM,EAAE,MAAOH,EAAE,IAAK;MACpB,IAAI;QACF,MAAMrB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,WAAWgC,EAAE,EAAE,EAAE;UAC3DT,MAAM,EAAE,QAAQ;UAChBd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C,MAAMA,KAAK;MACb;IACF;EACF,CAAC;EAED;EACAoC,KAAK,EAAE;IACLC,UAAU,EAAE,MAAAA,CAAA,KAAY;MACtB,IAAI;QACF,MAAM3C,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,gBAAgB,EAAE;UAC5DuB,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC;QAC1B,CAAC,CAAC;QAEF,OAAO,MAAMM,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACrD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDsC,aAAa,EAAE,MAAOrB,UAAU,IAAK;MACnC,IAAI;QACF,MAAMvB,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,gBAAgB,EAAE;UAC5DuB,MAAM,EAAE,KAAK;UACbd,OAAO,EAAEL,cAAc,CAAC,CAAC;UACzBwB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACI,UAAU;QACjC,CAAC,CAAC;QAEF,OAAO,MAAMxB,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD,MAAMA,KAAK;MACb;IACF,CAAC;IAEDuC,YAAY,EAAE,MAAOC,IAAI,IAAK;MAC5B,IAAI;QACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;QAC/BD,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEH,IAAI,CAAC;QAE/B,MAAMnD,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;QACjD,MAAMG,QAAQ,GAAG,MAAMW,KAAK,CAAC,GAAGtB,YAAY,eAAe,EAAE;UAC3DuB,MAAM,EAAE,MAAM;UACdd,OAAO,EAAE;YACP,IAAIH,KAAK,IAAI;cAAE,eAAe,EAAE,UAAUA,KAAK;YAAG,CAAC;UACrD,CAAC;UACDsB,IAAI,EAAE8B;QACR,CAAC,CAAC;QAEF,OAAO,MAAMhD,cAAc,CAACC,QAAQ,CAAC;MACvC,CAAC,CAAC,OAAOM,KAAK,EAAE;QACdQ,OAAO,CAACR,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;QAChD,MAAMA,KAAK;MACb;IACF;EACF;AACF,CAAC;AAED,eAAeE,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}