{"ast": null, "code": "import { createStore } from 'redux';\nimport { DragDropManagerImpl } from './classes/DragDropManagerImpl.js';\nimport { DragDropMonitorImpl } from './classes/DragDropMonitorImpl.js';\nimport { HandlerRegistryImpl } from './classes/HandlerRegistryImpl.js';\nimport { reduce } from './reducers/index.js';\nexport function createDragDropManager(backendFactory, globalContext = undefined, backendOptions = {}, debugMode = false) {\n  const store = makeStoreInstance(debugMode);\n  const monitor = new DragDropMonitorImpl(store, new HandlerRegistryImpl(store));\n  const manager = new DragDropManagerImpl(store, monitor);\n  const backend = backendFactory(manager, globalContext, backendOptions);\n  manager.receiveBackend(backend);\n  return manager;\n}\nfunction makeStoreInstance(debugMode) {\n  // TODO: if we ever make a react-native version of this,\n  // we'll need to consider how to pull off dev-tooling\n  const reduxDevTools = typeof window !== 'undefined' && window.__REDUX_DEVTOOLS_EXTENSION__;\n  return createStore(reduce, debugMode && reduxDevTools && reduxDevTools({\n    name: 'dnd-core',\n    instanceId: 'dnd-core'\n  }));\n}", "map": {"version": 3, "names": ["createStore", "DragDropManagerImpl", "DragDropMonitorImpl", "HandlerRegistryImpl", "reduce", "createDragDropManager", "backendFactory", "globalContext", "undefined", "backendOptions", "debugMode", "store", "makeStoreInstance", "monitor", "manager", "backend", "receiveBackend", "reduxDevTools", "window", "__REDUX_DEVTOOLS_EXTENSION__", "name", "instanceId"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\dnd-core\\src\\createDragDropManager.ts"], "sourcesContent": ["import type { Store } from 'redux'\nimport { createStore } from 'redux'\n\nimport { DragDropManagerImpl } from './classes/DragDropManagerImpl.js'\nimport { DragDropMonitorImpl } from './classes/DragDropMonitorImpl.js'\nimport { HandlerRegistryImpl } from './classes/HandlerRegistryImpl.js'\nimport type { BackendFactory, DragDropManager } from './interfaces.js'\nimport type { State } from './reducers/index.js'\nimport { reduce } from './reducers/index.js'\n\nexport function createDragDropManager(\n\tbackendFactory: BackendFactory,\n\tglobalContext: unknown = undefined,\n\tbackendOptions: unknown = {},\n\tdebugMode = false,\n): DragDropManager {\n\tconst store = makeStoreInstance(debugMode)\n\tconst monitor = new DragDropMonitorImpl(store, new HandlerRegistryImpl(store))\n\tconst manager = new DragDropManagerImpl(store, monitor)\n\tconst backend = backendFactory(manager, globalContext, backendOptions)\n\tmanager.receiveBackend(backend)\n\treturn manager\n}\n\nfunction makeStoreInstance(debugMode: boolean): Store<State> {\n\t// TODO: if we ever make a react-native version of this,\n\t// we'll need to consider how to pull off dev-tooling\n\tconst reduxDevTools =\n\t\ttypeof window !== 'undefined' &&\n\t\t(window as any).__REDUX_DEVTOOLS_EXTENSION__\n\treturn createStore(\n\t\treduce,\n\t\tdebugMode &&\n\t\t\treduxDevTools &&\n\t\t\treduxDevTools({\n\t\t\t\tname: 'dnd-core',\n\t\t\t\tinstanceId: 'dnd-core',\n\t\t\t}),\n\t)\n}\n"], "mappings": "AACA,SAASA,WAAW,QAAQ,OAAO;AAEnC,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,mBAAmB,QAAQ,kCAAkC;AACtE,SAASC,mBAAmB,QAAQ,kCAAkC;AAGtE,SAASC,MAAM,QAAQ,qBAAqB;AAE5C,OAAO,SAASC,qBAAqBA,CACpCC,cAA8B,EAC9BC,aAAsB,GAAGC,SAAS,EAClCC,cAAuB,GAAG,EAAE,EAC5BC,SAAS,GAAG,KAAK,EACC;EAClB,MAAMC,KAAK,GAAGC,iBAAiB,CAACF,SAAS,CAAC;EAC1C,MAAMG,OAAO,GAAG,IAAIX,mBAAmB,CAACS,KAAK,EAAE,IAAIR,mBAAmB,CAACQ,KAAK,CAAC,CAAC;EAC9E,MAAMG,OAAO,GAAG,IAAIb,mBAAmB,CAACU,KAAK,EAAEE,OAAO,CAAC;EACvD,MAAME,OAAO,GAAGT,cAAc,CAACQ,OAAO,EAAEP,aAAa,EAAEE,cAAc,CAAC;EACtEK,OAAO,CAACE,cAAc,CAACD,OAAO,CAAC;EAC/B,OAAOD,OAAO;;AAGf,SAASF,iBAAiBA,CAACF,SAAkB,EAAgB;EAC5D;EACA;EACA,MAAMO,aAAa,GAClB,OAAOC,MAAM,KAAK,WAAW,IAC7BA,MAAO,CAASC,4BAA4B;EAC7C,OAAOnB,WAAW,CACjBI,MAAM,EACNM,SAAS,IACRO,aAAa,IACbA,aAAa,CAAC;IACbG,IAAI,EAAE,UAAU;IAChBC,UAAU,EAAE;GACZ,CAAC,CACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}