import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const RoleBasedHeader = ({ userRole = 'member', currentUser, currentOrganization }) => {
  const [isOrgDropdownOpen, setIsOrgDropdownOpen] = useState(false);
  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  
  const orgDropdownRef = useRef(null);
  const userDropdownRef = useRef(null);
  const mobileMenuRef = useRef(null);

  // Default user data if not provided
  const user = currentUser || {
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: '/assets/images/avatar.jpg',
    role: 'Project Manager'
  };

  // Default organization data if not provided
  const organization = currentOrganization || {
    name: 'Acme Corporation',
    domain: 'acme.com',
    logo: '/assets/images/org-logo.png'
  };

  const availableOrganizations = [
    { id: 1, name: 'Acme Corporation', domain: 'acme.com', role: 'Admin' },
    { id: 2, name: 'TechStart Inc', domain: 'techstart.com', role: 'Member' },
    { id: 3, name: 'Global Solutions', domain: 'globalsol.com', role: 'Manager' }
  ];

  // Role-based navigation configuration
  const getNavigationItems = (role) => {
    const baseItems = [
      { label: 'Kanban Board', path: '/kanban-board', icon: 'Kanban', roles: ['viewer', 'member', 'admin', 'owner'] },
      { label: 'Team Members', path: '/team-members', icon: 'Users', roles: ['member', 'admin', 'owner'] }
    ];

    const adminItems = [
      { label: 'Organization', path: '/organization-settings', icon: 'Settings', roles: ['admin', 'owner'] }
    ];

    const ownerItems = [
      { label: 'Analytics', path: '/analytics', icon: 'BarChart3', roles: ['owner'] },
      { label: 'Billing', path: '/billing', icon: 'CreditCard', roles: ['owner'] }
    ];

    // Filter items based on user role
    const allItems = [...baseItems, ...adminItems, ...ownerItems];
    return allItems.filter(item => item.roles.includes(role.toLowerCase()));
  };

  // Get role-specific features
  const getRoleFeatures = (role) => {
    const features = {
      viewer: {
        canCreateProjects: false,
        canInviteMembers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canManageBilling: false
      },
      member: {
        canCreateProjects: true,
        canInviteMembers: false,
        canManageSettings: false,
        canViewAnalytics: false,
        canManageBilling: false
      },
      admin: {
        canCreateProjects: true,
        canInviteMembers: true,
        canManageSettings: true,
        canViewAnalytics: false,
        canManageBilling: false
      },
      owner: {
        canCreateProjects: true,
        canInviteMembers: true,
        canManageSettings: true,
        canViewAnalytics: true,
        canManageBilling: true
      }
    };
    return features[role.toLowerCase()] || features.viewer;
  };

  const navigationItems = getNavigationItems(userRole);
  const roleFeatures = getRoleFeatures(userRole);

  const isActivePath = (path) => location.pathname === path;

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (orgDropdownRef.current && !orgDropdownRef.current.contains(event.target)) {
        setIsOrgDropdownOpen(false);
      }
      if (userDropdownRef.current && !userDropdownRef.current.contains(event.target)) {
        setIsUserDropdownOpen(false);
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target)) {
        setIsMobileMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleOrganizationSwitch = (orgId) => {
    console.log('Switching to organization:', orgId);
    setIsOrgDropdownOpen(false);
  };

  const handleLogout = () => {
    console.log('Logging out...');
    // In real app, this would clear auth state and redirect
  };

  // Don't render header on auth pages
  if (location.pathname === '/login' || location.pathname === '/register') {
    return null;
  }

  // Get role badge color
  const getRoleBadgeColor = (role) => {
    const colors = {
      viewer: 'bg-gray-100 text-gray-800',
      member: 'bg-blue-100 text-blue-800',
      admin: 'bg-purple-100 text-purple-800',
      owner: 'bg-green-100 text-green-800'
    };
    return colors[role.toLowerCase()] || colors.viewer;
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-1000 bg-surface border-b border-border shadow-enterprise">
      <div className="flex items-center justify-between h-16 px-4 lg:px-6">
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/kanban-board" className="flex items-center space-x-3 hover-lift">
            <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
              <svg viewBox="0 0 24 24" className="w-5 h-5 text-primary-foreground" fill="currentColor">
                <path d="M3 3h7v7H3V3zm0 11h7v7H3v-7zm11-11h7v7h-7V3zm0 11h7v7h-7v-7z"/>
              </svg>
            </div>
            <span className="text-xl font-semibold text-text-primary">Agno WorkSphere</span>
          </Link>
        </div>

        {/* Organization Context Switcher - Desktop */}
        <div className="hidden lg:flex items-center space-x-6">
          <div className="relative" ref={orgDropdownRef}>
            <Button
              variant="ghost"
              onClick={() => setIsOrgDropdownOpen(!isOrgDropdownOpen)}
              className="flex items-center space-x-2 px-3 py-2"
            >
              <div className="w-6 h-6 bg-muted rounded-sm flex items-center justify-center">
                <span className="text-xs font-medium text-text-primary">
                  {organization.name.charAt(0)}
                </span>
              </div>
              <span className="font-medium text-text-primary">{organization.name}</span>
              <Icon name="ChevronDown" size={16} className="text-text-secondary" />
            </Button>

            {isOrgDropdownOpen && (
              <div className="absolute top-full left-0 mt-1 w-64 bg-popover border border-border rounded-md shadow-elevated z-1010">
                <div className="p-2">
                  <div className="text-xs font-medium text-text-secondary uppercase tracking-wide px-2 py-1">
                    Switch Organization
                  </div>
                  {availableOrganizations.map((org) => (
                    <button
                      key={org.id}
                      onClick={() => handleOrganizationSwitch(org.id)}
                      className="w-full flex items-center space-x-3 px-2 py-2 rounded-sm hover:bg-muted transition-micro text-left"
                    >
                      <div className="w-8 h-8 bg-muted rounded-sm flex items-center justify-center">
                        <span className="text-xs font-medium text-text-primary">
                          {org.name.charAt(0)}
                        </span>
                      </div>
                      <div className="flex-1">
                        <div className="text-sm font-medium text-text-primary">{org.name}</div>
                        <div className="text-xs text-text-secondary">{org.domain}</div>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded ${getRoleBadgeColor(org.role)}`}>
                        {org.role}
                      </span>
                      {org.id === 1 && (
                        <Icon name="Check" size={16} className="text-success" />
                      )}
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Role-Based Navigation */}
          <nav className="flex items-center space-x-1">
            {navigationItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${
                  isActivePath(item.path)
                    ? 'bg-primary text-primary-foreground'
                    : 'text-text-secondary hover:text-text-primary hover:bg-muted'
                }`}
              >
                <Icon name={item.icon} size={16} />
                <span>{item.label}</span>
              </Link>
            ))}
          </nav>
        </div>

        {/* User Profile & Mobile Menu */}
        <div className="flex items-center space-x-2">
          {/* User Profile Dropdown */}
          <div className="relative" ref={userDropdownRef}>
            <Button
              variant="ghost"
              onClick={() => setIsUserDropdownOpen(!isUserDropdownOpen)}
              className="flex items-center space-x-2 px-2 py-2"
            >
              <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-primary-foreground">
                  {user.name.split(' ').map(n => n[0]).join('')}
                </span>
              </div>
              <div className="hidden md:block text-left">
                <div className="text-sm font-medium text-text-primary">{user.name}</div>
                <div className="text-xs text-text-secondary">{user.role}</div>
              </div>
              <Icon name="ChevronDown" size={16} className="text-text-secondary" />
            </Button>

            {isUserDropdownOpen && (
              <div className="absolute top-full right-0 mt-1 w-56 bg-popover border border-border rounded-md shadow-elevated z-1010">
                <div className="p-2">
                  <div className="px-2 py-2 border-b border-border">
                    <div className="font-medium text-text-primary">{user.name}</div>
                    <div className="text-sm text-text-secondary">{user.email}</div>
                    <span className={`inline-block text-xs px-2 py-1 rounded mt-1 ${getRoleBadgeColor(userRole)}`}>
                      {userRole.charAt(0).toUpperCase() + userRole.slice(1)}
                    </span>
                  </div>
                  <div className="py-1">
                    <Link 
                      to="/user-profile-settings"
                      className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro"
                    >
                      <Icon name="User" size={16} />
                      <span>Profile Settings</span>
                    </Link>
                    <button className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro">
                      <Icon name="Bell" size={16} />
                      <span>Notifications</span>
                    </button>
                    <button className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-text-primary hover:bg-muted rounded-sm transition-micro">
                      <Icon name="HelpCircle" size={16} />
                      <span>Help & Support</span>
                    </button>
                    <div className="border-t border-border my-1"></div>
                    <button 
                      onClick={handleLogout}
                      className="w-full flex items-center space-x-2 px-2 py-2 text-sm text-destructive hover:bg-muted rounded-sm transition-micro"
                    >
                      <Icon name="LogOut" size={16} />
                      <span>Sign Out</span>
                    </button>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Mobile Menu Toggle */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="lg:hidden"
          >
            <Icon name={isMobileMenuOpen ? "X" : "Menu"} size={20} />
          </Button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div ref={mobileMenuRef} className="lg:hidden border-t border-border bg-surface">
          <div className="px-4 py-2 space-y-1">
            {navigationItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                onClick={() => setIsMobileMenuOpen(false)}
                className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-micro ${
                  isActivePath(item.path)
                    ? 'bg-primary text-primary-foreground'
                    : 'text-text-secondary hover:text-text-primary hover:bg-muted'
                }`}
              >
                <Icon name={item.icon} size={16} />
                <span>{item.label}</span>
              </Link>
            ))}
          </div>
        </div>
      )}
    </header>
  );
};

export default RoleBasedHeader;
