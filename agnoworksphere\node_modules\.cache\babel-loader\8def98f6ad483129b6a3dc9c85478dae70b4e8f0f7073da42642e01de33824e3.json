{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\components\\\\ui\\\\Textarea.jsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const Textarea = /*#__PURE__*/React.forwardRef(_c = ({\n  className = '',\n  ...props\n}, ref) => {\n  return /*#__PURE__*/_jsxDEV(\"textarea\", {\n    ref: ref,\n    className: `\n        flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm \n        ring-offset-background placeholder:text-muted-foreground \n        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 \n        disabled:cursor-not-allowed disabled:opacity-50\n        ${className}\n      `,\n    ...props\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n});\n_c2 = Textarea;\nTextarea.displayName = 'Textarea';\nexport default Textarea;\nvar _c, _c2;\n$RefreshReg$(_c, \"Textarea$React.forwardRef\");\n$RefreshReg$(_c2, \"Textarea\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Textarea", "forwardRef", "_c", "className", "props", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c2", "displayName", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/components/ui/Textarea.jsx"], "sourcesContent": ["import React from 'react';\n\nexport const Textarea = React.forwardRef(({ className = '', ...props }, ref) => {\n  return (\n    <textarea\n      ref={ref}\n      className={`\n        flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm \n        ring-offset-background placeholder:text-muted-foreground \n        focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 \n        disabled:cursor-not-allowed disabled:opacity-50\n        ${className}\n      `}\n      {...props}\n    />\n  );\n});\n\nTextarea.displayName = 'Textarea';\n\nexport default Textarea;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,OAAO,MAAMC,QAAQ,gBAAGH,KAAK,CAACI,UAAU,CAAAC,EAAA,GAACA,CAAC;EAAEC,SAAS,GAAG,EAAE;EAAE,GAAGC;AAAM,CAAC,EAAEC,GAAG,KAAK;EAC9E,oBACEN,OAAA;IACEM,GAAG,EAAEA,GAAI;IACTF,SAAS,EAAE;AACjB;AACA;AACA;AACA;AACA,UAAUA,SAAS;AACnB,OAAQ;IAAA,GACEC;EAAK;IAAAE,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEN,CAAC,CAAC;AAACC,GAAA,GAdUV,QAAQ;AAgBrBA,QAAQ,CAACW,WAAW,GAAG,UAAU;AAEjC,eAAeX,QAAQ;AAAC,IAAAE,EAAA,EAAAQ,GAAA;AAAAE,YAAA,CAAAV,EAAA;AAAAU,YAAA,CAAAF,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}