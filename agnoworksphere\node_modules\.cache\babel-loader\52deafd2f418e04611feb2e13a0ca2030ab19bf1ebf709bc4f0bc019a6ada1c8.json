{"ast": null, "code": "// `call`, just like a function.\nexport class RawTask {\n  call() {\n    try {\n      this.task && this.task();\n    } catch (error) {\n      this.onError(error);\n    } finally {\n      this.task = null;\n      this.release(this);\n    }\n  }\n  constructor(onError, release) {\n    this.onError = onError;\n    this.release = release;\n    this.task = null;\n  }\n}", "map": {"version": 3, "names": ["RawTask", "call", "task", "error", "onError", "release", "constructor"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\@react-dnd\\asap\\src\\RawTask.ts"], "sourcesContent": ["// We wrap tasks with recyclable task objects.  A task object implements\n\nimport type { Task, TaskFn } from 'types'\n\n// `call`, just like a function.\nexport class RawTask implements Task {\n\tpublic task: TaskFn | null = null\n\n\tpublic constructor(\n\t\tprivate onError: (err: any) => void,\n\t\tprivate release: (t: RawTask) => void,\n\t) {}\n\n\tpublic call() {\n\t\ttry {\n\t\t\tthis.task && this.task()\n\t\t} catch (error) {\n\t\t\tthis.onError(error)\n\t\t} finally {\n\t\t\tthis.task = null\n\t\t\tthis.release(this)\n\t\t}\n\t}\n}\n"], "mappings": "AAIA;AACA,OAAO,MAAMA,OAAO;EAQnBC,IAAWA,CAAA,EAAG;IACb,IAAI;MACH,IAAI,CAACC,IAAI,IAAI,IAAI,CAACA,IAAI,EAAE;KACxB,CAAC,OAAOC,KAAK,EAAE;MACf,IAAI,CAACC,OAAO,CAACD,KAAK,CAAC;KACnB,SAAS;MACT,IAAI,CAACD,IAAI,GAAG,IAAI;MAChB,IAAI,CAACG,OAAO,CAAC,IAAI,CAAC;;;EAZpBC,YACSF,OAA2B,EAC3BC,OAA6B,EACpC;SAFOD,OAA2B,GAA3BA,OAA2B;SAC3BC,OAA6B,GAA7BA,OAA6B;SAJ/BH,IAAI,GAAkB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}