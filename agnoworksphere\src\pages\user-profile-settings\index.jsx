import React, { useState } from 'react';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Icon from '../../components/AppIcon';
import PersonalInfoTab from './components/PersonalInfoTab';
import SecurityTab from './components/SecurityTab';
import NotificationsTab from './components/NotificationsTab';

const UserProfileSettings = () => {
  const [activeTab, setActiveTab] = useState('personal');

  // Mock user data
  const [userData, setUserData] = useState({
    fullName: "<PERSON>",
    email: "<EMAIL>",
    jobTitle: "Senior Project Manager",
    bio: `Experienced project manager with over 8 years in leading cross-functional teams and delivering complex software projects. Passionate about agile methodologies and team collaboration.\n\nCurrently focused on digital transformation initiatives and process optimization.`,
    avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face"
  });

  const tabs = [
    {
      id: 'personal',
      label: 'Personal Info',
      icon: 'User',
      description: 'Manage your profile information and avatar'
    },
    {
      id: 'security',
      label: 'Security',
      icon: 'Shield',
      description: 'Password, two-factor authentication, and sessions'
    },
    {
      id: 'notifications',
      label: 'Notifications',
      icon: 'Bell',
      description: 'Email and in-app notification preferences'
    }
  ];

  const handlePersonalInfoSave = (formData) => {
    setUserData(prev => ({
      ...prev,
      ...formData
    }));
    console.log('Personal info updated:', formData);
  };

  const handlePasswordChange = (passwordData) => {
    console.log('Password change requested:', passwordData);
    // Mock password change - in real app, this would call an API
  };

  const handleTwoFactorToggle = (enabled) => {
    console.log('Two-factor authentication:', enabled ? 'enabled' : 'disabled');
  };

  const handleNotificationsSave = (notificationSettings) => {
    console.log('Notification settings updated:', notificationSettings);
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'personal':
        return (
          <PersonalInfoTab
            userData={userData}
            onSave={handlePersonalInfoSave}
          />
        );
      case 'security':
        return (
          <SecurityTab
            onPasswordChange={handlePasswordChange}
            onTwoFactorToggle={handleTwoFactorToggle}
          />
        );
      case 'notifications':
        return (
          <NotificationsTab
            onSave={handleNotificationsSave}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      <main className="pt-16">
        <div className="max-w-6xl mx-auto px-6 py-8">
          <Breadcrumb />
          
          {/* Page Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-2">
              <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                <Icon name="Settings" size={20} className="text-primary-foreground" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-foreground">Profile Settings</h1>
                <p className="text-muted-foreground">
                  Manage your account settings and preferences
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Sidebar Navigation */}
            <div className="lg:col-span-1">
              <div className="bg-card border border-border rounded-lg p-1 sticky top-24">
                <nav className="space-y-1">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-smooth ${
                        activeTab === tab.id
                          ? 'bg-primary text-primary-foreground shadow-sm'
                          : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                      }`}
                    >
                      <Icon name={tab.icon} size={18} />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium">{tab.label}</div>
                        <div className={`text-xs mt-0.5 ${
                          activeTab === tab.id 
                            ? 'text-primary-foreground/80' 
                            : 'text-muted-foreground'
                        }`}>
                          {tab.description}
                        </div>
                      </div>
                    </button>
                  ))}
                </nav>
              </div>

              {/* Mobile Tab Selector */}
              <div className="lg:hidden mb-6">
                <select
                  value={activeTab}
                  onChange={(e) => setActiveTab(e.target.value)}
                  className="w-full px-4 py-3 border border-border rounded-lg bg-input text-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                >
                  {tabs.map((tab) => (
                    <option key={tab.id} value={tab.id}>
                      {tab.label}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="bg-card border border-border rounded-lg p-8">
                {/* Tab Header */}
                <div className="mb-8 pb-6 border-b border-border">
                  <div className="flex items-center space-x-3">
                    <Icon 
                      name={tabs.find(tab => tab.id === activeTab)?.icon || 'Settings'} 
                      size={24} 
                      className="text-primary" 
                    />
                    <div>
                      <h2 className="text-2xl font-semibold text-foreground">
                        {tabs.find(tab => tab.id === activeTab)?.label}
                      </h2>
                      <p className="text-muted-foreground">
                        {tabs.find(tab => tab.id === activeTab)?.description}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Tab Content */}
                {renderTabContent()}
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default UserProfileSettings;