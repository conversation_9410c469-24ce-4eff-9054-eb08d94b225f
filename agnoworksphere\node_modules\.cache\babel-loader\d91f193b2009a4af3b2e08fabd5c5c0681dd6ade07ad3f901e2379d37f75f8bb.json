{"ast": null, "code": "/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */export function invariant(condition, format, ...args) {\n  if (isProduction()) {\n    if (format === undefined) {\n      throw new Error('invariant requires an error message argument');\n    }\n  }\n  if (!condition) {\n    let error;\n    if (format === undefined) {\n      error = new Error('Minified exception occurred; use the non-minified dev environment ' + 'for the full error message and additional helpful warnings.');\n    } else {\n      let argIndex = 0;\n      error = new Error(format.replace(/%s/g, function () {\n        return args[argIndex++];\n      }));\n      error.name = 'Invariant Violation';\n    }\n    error.framesToPop = 1 // we don't care about invariant's own frame\n    ;\n    throw error;\n  }\n}\nfunction isProduction() {\n  return typeof process !== 'undefined' && process.env['NODE_ENV'] === 'production';\n}", "map": {"version": 3, "names": ["invariant", "condition", "format", "args", "isProduction", "undefined", "Error", "error", "argIndex", "replace", "name", "framesToPop", "process", "env"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\@react-dnd\\invariant\\src\\index.ts"], "sourcesContent": ["/**\n * Use invariant() to assert state which your program assumes to be true.\n *\n * Provide sprintf-style format (only %s is supported) and arguments\n * to provide information about what broke and what you were\n * expecting.\n *\n * The invariant message will be stripped in production, but the invariant\n * will remain to ensure logic does not differ in production.\n */\n\nexport function invariant(condition: any, format: string, ...args: any[]) {\n\tif (isProduction()) {\n\t\tif (format === undefined) {\n\t\t\tthrow new Error('invariant requires an error message argument')\n\t\t}\n\t}\n\n\tif (!condition) {\n\t\tlet error\n\t\tif (format === undefined) {\n\t\t\terror = new Error(\n\t\t\t\t'Minified exception occurred; use the non-minified dev environment ' +\n\t\t\t\t\t'for the full error message and additional helpful warnings.',\n\t\t\t)\n\t\t} else {\n\t\t\tlet argIndex = 0\n\t\t\terror = new Error(\n\t\t\t\tformat.replace(/%s/g, function () {\n\t\t\t\t\treturn args[argIndex++]\n\t\t\t\t}),\n\t\t\t)\n\t\t\terror.name = 'Invariant Violation'\n\t\t}\n\n\t\t;(error as any).framesToPop = 1 // we don't care about invariant's own frame\n\t\tthrow error\n\t}\n}\n\nfunction isProduction() {\n\treturn (\n\t\ttypeof process !== 'undefined' && process.env['NODE_ENV'] === 'production'\n\t)\n}\n"], "mappings": "AAAA;;;;;;;;;GAWA,OAAO,SAASA,SAASA,CAACC,SAAc,EAAEC,MAAc,EAAE,GAAGC,IAAI,EAAS;EACzE,IAAIC,YAAY,EAAE,EAAE;IACnB,IAAIF,MAAM,KAAKG,SAAS,EAAE;MACzB,MAAM,IAAIC,KAAK,CAAC,8CAA8C,CAAC;;;EAIjE,IAAI,CAACL,SAAS,EAAE;IACf,IAAIM,KAAK;IACT,IAAIL,MAAM,KAAKG,SAAS,EAAE;MACzBE,KAAK,GAAG,IAAID,KAAK,CAChB,oEAAoE,GACnE,6DAA6D,CAC9D;KACD,MAAM;MACN,IAAIE,QAAQ,GAAG,CAAC;MAChBD,KAAK,GAAG,IAAID,KAAK,CAChBJ,MAAM,CAACO,OAAO,QAAQ,YAAY;QACjC,OAAON,IAAI,CAACK,QAAQ,EAAE,CAAC;OACvB,CAAC,CACF;MACDD,KAAK,CAACG,IAAI,GAAG,qBAAqB;;IAGlCH,KAAM,CAASI,WAAW,GAAG,CAAC,CAAC;IAAA;IAChC,MAAMJ,KAAK;;;AAIb,SAASH,YAAYA,CAAA,EAAG;EACvB,OACC,OAAOQ,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC,KAAK,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}