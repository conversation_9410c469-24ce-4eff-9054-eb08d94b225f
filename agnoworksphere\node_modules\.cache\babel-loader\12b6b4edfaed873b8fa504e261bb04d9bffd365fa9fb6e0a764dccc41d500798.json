{"ast": null, "code": "import { useMemo } from 'react';\nimport { TargetConnector } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js';\nexport function useDropTargetConnector(options) {\n  const manager = useDragDropManager();\n  const connector = useMemo(() => new TargetConnector(manager.getBackend()), [manager]);\n  useIsomorphicLayoutEffect(() => {\n    connector.dropTargetOptions = options || null;\n    connector.reconnect();\n    return () => connector.disconnectDropTarget();\n  }, [options]);\n  return connector;\n}", "map": {"version": 3, "names": ["useMemo", "TargetConnector", "useDragDropManager", "useIsomorphicLayoutEffect", "useDropTargetConnector", "options", "manager", "connector", "getBackend", "dropTargetOptions", "reconnect", "disconnectDropTarget"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd\\src\\hooks\\useDrop\\useDropTargetConnector.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport { TargetConnector } from '../../internals/index.js'\nimport type { DropTargetOptions } from '../../types/index.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\n\nexport function useDropTargetConnector(\n\toptions: DropTargetOptions,\n): TargetConnector {\n\tconst manager = useDragDropManager()\n\tconst connector = useMemo(\n\t\t() => new TargetConnector(manager.getBackend()),\n\t\t[manager],\n\t)\n\tuseIsomorphicLayoutEffect(() => {\n\t\tconnector.dropTargetOptions = options || null\n\t\tconnector.reconnect()\n\t\treturn () => connector.disconnectDropTarget()\n\t}, [options])\n\treturn connector\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAE/B,SAASC,eAAe,QAAQ,0BAA0B;AAE1D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC;AAE3E,OAAO,SAASC,sBAAsBA,CACrCC,OAA0B,EACR;EAClB,MAAMC,OAAO,GAAGJ,kBAAkB,EAAE;EACpC,MAAMK,SAAS,GAAGP,OAAO,CACxB,MAAM,IAAIC,eAAe,CAACK,OAAO,CAACE,UAAU,EAAE,CAAC,EAC/C,CAACF,OAAO,CAAC,CACT;EACDH,yBAAyB,CAAC,MAAM;IAC/BI,SAAS,CAACE,iBAAiB,GAAGJ,OAAO,IAAI,IAAI;IAC7CE,SAAS,CAACG,SAAS,EAAE;IACrB,OAAO,MAAMH,SAAS,CAACI,oBAAoB,EAAE;GAC7C,EAAE,CAACN,OAAO,CAAC,CAAC;EACb,OAAOE,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}