// src/utils/authService.js

// Enhanced authentication service connecting to backend API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// Helper function to handle API responses
const handleResponse = async (response) => {
  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error?.message || result.message || 'API request failed');
  }

  return result;
};

const authService = {
  // Sign up a new user with enhanced registration
  signUp: async (email, password, userData = {}) => {
    try {
      const registrationData = {
        email: email,
        password: password,
        first_name: userData.firstName || userData.first_name || '',
        last_name: userData.lastName || userData.last_name || '',
        organization_name: userData.organizationName || userData.organization_name || null
      };

      const response = await fetch(`${API_BASE_URL}/v1/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(registrationData),
      });

      const result = await handleResponse(response);

      // Store tokens
      if (result.data.tokens) {
        localStorage.setItem('accessToken', result.data.tokens.access_token);
        localStorage.setItem('userRole', result.data.role || 'member');
        localStorage.setItem('organizationId', result.data.organization?.id || '');
      }

      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name,
            lastName: result.data.user.last_name,
            emailVerified: result.data.user.email_verified,
            role: result.data.role
          },
          organization: result.data.organization,
          tokens: result.data.tokens
        },
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Sign up failed'
      };
    }
  },

  // Sign in with email and password
  signIn: async (email, password) => {
    try {
      const loginData = {
        email: email,
        password: password
      };

      const response = await fetch(`${API_BASE_URL}/v1/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(loginData),
      });

      const result = await handleResponse(response);

      // Store tokens and user info
      if (result.data.tokens) {
        localStorage.setItem('accessToken', result.data.tokens.access_token);
        localStorage.setItem('userRole', result.data.role || 'member');
        localStorage.setItem('organizationId', result.data.organization?.id || '');
      }

      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name,
            lastName: result.data.user.last_name,
            emailVerified: result.data.user.email_verified,
            role: result.data.role
          },
          organization: result.data.organization,
          tokens: result.data.tokens
        },
        error: null
      };

    } catch (error) {
      return {
        data: null,
        error: error.message || 'Sign in failed'
      };
    }
  },

  // Sign out
  signOut: async () => {
    try {
      // Clear stored tokens and user data
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('userRole');
      localStorage.removeItem('organizationId');

      return {
        error: null
      };
    } catch (error) {
      return {
        error: error.message || 'Sign out failed'
      };
    }
  },

  // Get current user profile
  getCurrentUser: async () => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        return {
          data: { user: null },
          error: null
        };
      }

      const response = await fetch(`${API_BASE_URL}/v1/users/profile`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      });

      if (!response.ok) {
        // Token might be expired
        localStorage.removeItem('accessToken');
        localStorage.removeItem('userRole');
        localStorage.removeItem('organizationId');
        return {
          data: { user: null },
          error: null
        };
      }

      const result = await response.json();

      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name,
            lastName: result.data.user.last_name,
            emailVerified: result.data.user.email_verified,
            role: localStorage.getItem('userRole') || 'member'
          },
          organizations: result.data.organizations
        },
        error: null
      };
    } catch (error) {
      return {
        data: { user: null },
        error: null // Don't expose errors for getCurrentUser
      };
    }
  },

  // Refresh access token (authentication removed - mock implementation)
  refreshToken: async () => {
    try {
      // Mock successful token refresh
      await new Promise(resolve => setTimeout(resolve, 200)); // Simulate API delay

      return {
        data: {
          accessToken: 'mock-access-token',
          refreshToken: 'mock-refresh-token'
        },
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Token refresh failed'
      };
    }
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem('accessToken');
    return !!token;
  },

  // Get stored access token
  getAccessToken: () => {
    return localStorage.getItem('accessToken');
  },

  // Get user role
  getUserRole: () => {
    return localStorage.getItem('userRole') || 'member';
  },

  // Get organization ID
  getOrganizationId: () => {
    return localStorage.getItem('organizationId');
  },

  // Get dashboard stats
  getDashboardStats: async () => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        throw new Error('No access token');
      }

      const response = await fetch(`${API_BASE_URL}/v1/dashboard/stats`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
      });

      const result = await handleResponse(response);

      return {
        data: result.data,
        userRole: result.user_role,
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Failed to get dashboard stats'
      };
    }
  },

  // Listen to auth state changes
  onAuthStateChange: (callback) => {
    // Simple implementation - check token periodically
    const checkAuth = async () => {
      const result = await authService.getCurrentUser();
      callback(result.data.user, result.error);
    };

    // Check immediately
    checkAuth();

    // Set up periodic check (every 5 minutes)
    const interval = setInterval(checkAuth, 5 * 60 * 1000);

    return {
      data: {
        subscription: {
          unsubscribe: () => clearInterval(interval)
        }
      }
    };
  }
};

export default authService;
