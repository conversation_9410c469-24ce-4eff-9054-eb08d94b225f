"""
Main API router for v1 endpoints
"""
from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, organizations, projects, boards, columns, cards, teams, upload

api_router = APIRouter(prefix="/v1")

# Include all endpoint routers
api_router.include_router(auth.router, prefix="/auth", tags=["Authentication"])
api_router.include_router(users.router, prefix="/users", tags=["Users"])
api_router.include_router(organizations.router, prefix="/organizations", tags=["Organizations"])
api_router.include_router(projects.router, prefix="/projects", tags=["Projects"])
api_router.include_router(boards.router, prefix="/boards", tags=["Boards"])
api_router.include_router(columns.router, prefix="/columns", tags=["Columns"])
api_router.include_router(cards.router, prefix="/cards", tags=["Cards"])
api_router.include_router(teams.router, prefix="/teams", tags=["Teams"])
api_router.include_router(upload.router, prefix="/upload", tags=["File Upload"])
