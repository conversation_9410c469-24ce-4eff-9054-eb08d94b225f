{"ast": null, "code": "export function getDataFromDataTransfer(dataTransfer, typesToTry, defaultValue) {\n  const result = typesToTry.reduce((resultSoFar, typeToTry) => resultSoFar || dataTransfer.getData(typeToTry), '');\n  return result != null ? result : defaultValue;\n}", "map": {"version": 3, "names": ["getDataFromDataTransfer", "dataTransfer", "typesToTry", "defaultValue", "result", "reduce", "resultSoFar", "typeToTry", "getData"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd-html5-backend\\src\\NativeDragSources\\getDataFromDataTransfer.ts"], "sourcesContent": ["export function getDataFromDataTransfer(\n\tdataTransfer: DataTransfer,\n\ttypesToTry: string[],\n\tdefaultValue: string,\n): string {\n\tconst result = typesToTry.reduce(\n\t\t(resultSoFar, typeToTry) => resultSoFar || dataTransfer.getData(typeToTry),\n\t\t'',\n\t)\n\n\treturn result != null ? result : defaultValue\n}\n"], "mappings": "AAAA,OAAO,SAASA,uBAAuBA,CACtCC,YAA0B,EAC1BC,UAAoB,EACpBC,YAAoB,EACX;EACT,MAAMC,MAAM,GAAGF,UAAU,CAACG,MAAM,CAC/B,CAACC,WAAW,EAAEC,SAAS,KAAKD,WAAW,IAAIL,YAAY,CAACO,OAAO,CAACD,SAAS,CAAC,EAC1E,EAAE,CACF;EAED,OAAOH,MAAM,IAAI,IAAI,GAAGA,MAAM,GAAGD,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}