{"ast": null, "code": "// cheap lodash replacements\nexport function memoize(fn) {\n  let result = null;\n  const memoized = () => {\n    if (result == null) {\n      result = fn();\n    }\n    return result;\n  };\n  return memoized;\n}\n/**\n * drop-in replacement for _.without\n */\nexport function without(items, item) {\n  return items.filter(i => i !== item);\n}\nexport function union(itemsA, itemsB) {\n  const set = new Set();\n  const insertItem = item => set.add(item);\n  itemsA.forEach(insertItem);\n  itemsB.forEach(insertItem);\n  const result = [];\n  set.forEach(key => result.push(key));\n  return result;\n}", "map": {"version": 3, "names": ["memoize", "fn", "result", "memoized", "without", "items", "item", "filter", "i", "union", "itemsA", "itemsB", "set", "Set", "insertItem", "add", "for<PERSON>ach", "key", "push"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd-html5-backend\\src\\utils\\js_utils.ts"], "sourcesContent": ["// cheap lodash replacements\n\nexport function memoize<T>(fn: () => T): () => T {\n\tlet result: T | null = null\n\tconst memoized = () => {\n\t\tif (result == null) {\n\t\t\tresult = fn()\n\t\t}\n\t\treturn result\n\t}\n\treturn memoized\n}\n\n/**\n * drop-in replacement for _.without\n */\nexport function without<T>(items: T[], item: T) {\n\treturn items.filter((i) => i !== item)\n}\n\nexport function union<T extends string | number>(itemsA: T[], itemsB: T[]) {\n\tconst set = new Set<T>()\n\tconst insertItem = (item: T) => set.add(item)\n\titemsA.forEach(insertItem)\n\titemsB.forEach(insertItem)\n\n\tconst result: T[] = []\n\tset.forEach((key) => result.push(key))\n\treturn result\n}\n"], "mappings": "AAAA;AAEA,OAAO,SAASA,OAAOA,CAAIC,EAAW,EAAW;EAChD,IAAIC,MAAM,GAAa,IAAI;EAC3B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;IACtB,IAAID,MAAM,IAAI,IAAI,EAAE;MACnBA,MAAM,GAAGD,EAAE,EAAE;;IAEd,OAAOC,MAAM;GACb;EACD,OAAOC,QAAQ;;AAGhB;;;AAGA,OAAO,SAASC,OAAOA,CAAIC,KAAU,EAAEC,IAAO,EAAE;EAC/C,OAAOD,KAAK,CAACE,MAAM,CAAEC,CAAC,IAAKA,CAAC,KAAKF,IAAI,CAAC;;AAGvC,OAAO,SAASG,KAAKA,CAA4BC,MAAW,EAAEC,MAAW,EAAE;EAC1E,MAAMC,GAAG,GAAG,IAAIC,GAAG,EAAK;EACxB,MAAMC,UAAU,GAAIR,IAAO,IAAKM,GAAG,CAACG,GAAG,CAACT,IAAI,CAAC;EAC7CI,MAAM,CAACM,OAAO,CAACF,UAAU,CAAC;EAC1BH,MAAM,CAACK,OAAO,CAACF,UAAU,CAAC;EAE1B,MAAMZ,MAAM,GAAQ,EAAE;EACtBU,GAAG,CAACI,OAAO,CAAEC,GAAG,IAAKf,MAAM,CAACgB,IAAI,CAACD,GAAG,CAAC,CAAC;EACtC,OAAOf,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}