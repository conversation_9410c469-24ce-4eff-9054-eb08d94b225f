#!/usr/bin/env python3
"""
Test script to verify API endpoints
"""
import requests
import json
import time

BASE_URL = "http://localhost:3001"

def test_health():
    """Test health endpoint"""
    print("🔍 Testing health endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Health check passed: {data['data']['status']}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_register():
    """Test user registration"""
    print("\n🔍 Testing user registration...")
    try:
        user_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/register",
            json=user_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Registration successful: {data['data']['user']['email']}")
            return data['data']['tokens']['access_token']
        else:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return None

def test_login():
    """Test user login"""
    print("\n🔍 Testing user login...")
    try:
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/auth/login",
            json=login_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Login successful: {data['data']['user']['email']}")
            return data['data']['tokens']['access_token']
        else:
            print(f"❌ Login failed: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ Login error: {e}")
        return None

def test_profile(token):
    """Test profile endpoint"""
    print("\n🔍 Testing profile endpoint...")
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BASE_URL}/api/v1/users/profile", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Profile retrieved: {data['data']['email']}")
            return True
        else:
            print(f"❌ Profile failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Profile error: {e}")
        return False

def test_organization(token):
    """Test organization endpoints"""
    print("\n🔍 Testing organization endpoints...")
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # Create organization
        org_data = {
            "name": "Test Organization",
            "description": "A test organization for API testing"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/organizations",
            json=org_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Organization created: {data['data']['name']}")
            
            # Get organizations
            response = requests.get(f"{BASE_URL}/api/v1/organizations", headers=headers)
            if response.status_code == 200:
                orgs = response.json()
                print(f"✅ Organizations retrieved: {len(orgs['data'])} organizations")
                return True
            else:
                print(f"❌ Get organizations failed: {response.status_code}")
                return False
        else:
            print(f"❌ Organization creation failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Organization error: {e}")
        return False

def test_project(token):
    """Test project endpoints"""
    print("\n🔍 Testing project endpoints...")
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        # Create project
        project_data = {
            "name": "Test Project",
            "description": "A test project for API testing"
        }
        
        response = requests.post(
            f"{BASE_URL}/api/v1/projects",
            json=project_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Project created: {data['data']['name']}")
            
            # Get projects
            response = requests.get(f"{BASE_URL}/api/v1/projects", headers=headers)
            if response.status_code == 200:
                projects = response.json()
                print(f"✅ Projects retrieved: {len(projects['data'])} projects")
                return True
            else:
                print(f"❌ Get projects failed: {response.status_code}")
                return False
        else:
            print(f"❌ Project creation failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Project error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Agno WorkSphere API")
    print("=" * 50)
    
    # Test health
    if not test_health():
        print("\n💥 Health check failed. Make sure the server is running.")
        return
    
    # Test registration
    token = test_register()
    if not token:
        print("\n💥 Registration failed. Cannot continue with other tests.")
        return
    
    # Test login
    login_token = test_login()
    if not login_token:
        print("\n💥 Login failed.")
        return
    
    # Use the login token for subsequent tests
    token = login_token
    
    # Test profile
    test_profile(token)
    
    # Test organization
    test_organization(token)
    
    # Test project
    test_project(token)
    
    print("\n🎉 API testing completed!")
    print("\n📋 Summary:")
    print("- Backend server is running on http://localhost:3001")
    print("- API documentation available at http://localhost:3001/docs")
    print("- Frontend should be running on http://localhost:3000")
    print("- All basic API endpoints are working correctly")

if __name__ == "__main__":
    main()
