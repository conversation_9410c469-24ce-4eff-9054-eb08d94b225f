{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleBasedDashboard = () => {\n  _s();\n  var _organizations$0$orga, _currentUser$email, _organizations$0$orga2;\n  const location = useLocation();\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        setLoading(true);\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n        }\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadDashboardData();\n  }, []);\n\n  // Show loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600\",\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-red-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-lg mb-2\",\n          children: \"Failed to load dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Mock data for different dashboard components (fallback)\n  const mockProjects = [{\n    id: 1,\n    name: \"E-commerce Platform Redesign\",\n    description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n    status: \"Active\",\n    priority: \"High\",\n    progress: 75,\n    dueDate: \"Dec 15, 2025\",\n    team: [{\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    }, {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    }, {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    }, {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    }]\n  }, {\n    id: 2,\n    name: \"Mobile App Development\",\n    description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n    status: \"Active\",\n    priority: \"Medium\",\n    progress: 45,\n    dueDate: \"Jan 30, 2026\",\n    team: [{\n      name: \"David Kim\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\"\n    }, {\n      name: \"Lisa Wang\",\n      avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\"\n    }, {\n      name: \"Tom Wilson\",\n      avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\"\n    }]\n  }, {\n    id: 3,\n    name: \"Data Analytics Dashboard\",\n    description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n    status: \"Completed\",\n    priority: \"Low\",\n    progress: 100,\n    dueDate: \"Nov 20, 2025\",\n    team: [{\n      name: \"Rachel Green\",\n      avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\"\n    }, {\n      name: \"James Brown\",\n      avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\"\n    }]\n  }];\n  const mockActivities = [{\n    id: 1,\n    type: \"task_completed\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    description: \"Completed the user interface mockups for the checkout process\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 15 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 2,\n    type: \"comment_added\",\n    user: {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    },\n    description: \"Added feedback on the mobile responsive design implementation\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 45 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 3,\n    type: \"project_created\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    description: \"Created new project for Q1 marketing campaign automation\",\n    project: \"Marketing Automation\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    isPublic: false\n  }, {\n    id: 4,\n    type: \"member_added\",\n    user: {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    },\n    description: \"Joined the mobile app development team as a senior developer\",\n    project: \"Mobile App Development\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    isPublic: true\n  }];\n  const mockTasks = [{\n    id: 1,\n    title: \"Implement payment gateway integration\",\n    status: \"In Progress\",\n    priority: \"High\",\n    dueDate: \"Dec 10, 2025\",\n    assignee: \"Sarah Johnson\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 2,\n    title: \"Design user onboarding flow\",\n    status: \"To Do\",\n    priority: \"Medium\",\n    dueDate: \"Dec 12, 2025\",\n    assignee: \"Mike Chen\",\n    project: \"Mobile App\"\n  }, {\n    id: 3,\n    title: \"Set up automated testing pipeline\",\n    status: \"Review\",\n    priority: \"High\",\n    dueDate: \"Dec 8, 2025\",\n    assignee: \"Emily Davis\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 4,\n    title: \"Create API documentation\",\n    status: \"Done\",\n    priority: \"Low\",\n    dueDate: \"Dec 5, 2025\",\n    assignee: \"Alex Rodriguez\",\n    project: \"Data Analytics\"\n  }, {\n    id: 5,\n    title: \"Optimize database queries\",\n    status: \"Blocked\",\n    priority: \"High\",\n    dueDate: \"Dec 15, 2025\",\n    assignee: \"David Kim\",\n    project: \"E-commerce Platform\"\n  }];\n  const mockTeamMembers = [{\n    id: 1,\n    name: \"Sarah Johnson\",\n    email: \"<EMAIL>\",\n    role: \"Admin\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n    department: \"Design\",\n    currentTask: \"UI/UX Design Review\",\n    tasksCompleted: 24,\n    lastActive: new Date()\n  }, {\n    id: 2,\n    name: \"Mike Chen\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Away\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n    department: \"Development\",\n    currentTask: \"Frontend Implementation\",\n    tasksCompleted: 18,\n    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n  }, {\n    id: 3,\n    name: \"Emily Davis\",\n    email: \"<EMAIL>\",\n    role: \"Owner\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n    department: \"Management\",\n    currentTask: \"Project Planning\",\n    tasksCompleted: 32,\n    lastActive: new Date()\n  }, {\n    id: 4,\n    name: \"Alex Rodriguez\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Busy\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n    department: \"Development\",\n    currentTask: \"API Integration\",\n    tasksCompleted: 15,\n    lastActive: new Date(Date.now() - 30 * 60 * 1000)\n  }, {\n    id: 5,\n    name: \"David Kim\",\n    email: \"<EMAIL>\",\n    role: \"Viewer\",\n    status: \"Offline\",\n    avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n    department: \"QA\",\n    currentTask: null,\n    tasksCompleted: 8,\n    lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n  }];\n  const mockNotifications = [{\n    id: 1,\n    type: \"task_assigned\",\n    title: \"New Task Assigned\",\n    message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n    timestamp: new Date(Date.now() - 30 * 60 * 1000),\n    read: false,\n    priority: \"high\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    actions: [{\n      label: \"View Task\",\n      variant: \"default\"\n    }, {\n      label: \"Accept\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 2,\n    type: \"deadline_reminder\",\n    title: \"Deadline Approaching\",\n    message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    read: false,\n    priority: \"medium\",\n    user: null,\n    actions: [{\n      label: \"View Details\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 3,\n    type: \"comment_mention\",\n    title: \"You were mentioned\",\n    message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    read: true,\n    priority: \"low\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    actions: [{\n      label: \"Reply\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 4,\n    type: \"system_alert\",\n    title: \"System Maintenance\",\n    message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n    read: true,\n    priority: \"medium\",\n    user: null,\n    actions: []\n  }];\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    var _realData$total_organ, _realData$total_proje, _realData$total_membe, _realData$recent_acti, _realData$recent_acti2, _realData$total_proje2, _realData$total_membe2, _realData$total_organ2, _realData$recent_acti3, _realData$recent_acti4, _realData$total_proje3, _realData$total_organ3, _realData$total_membe3, _realData$recent_acti5, _realData$recent_acti6, _realData$total_proje4, _realData$total_organ4, _realData$total_membe4, _realData$recent_acti7, _realData$recent_acti8;\n    const realData = dashboardData || {};\n    switch (userRole) {\n      case 'owner':\n        return [{\n          title: \"Organizations\",\n          value: ((_realData$total_organ = realData.total_organizations) === null || _realData$total_organ === void 0 ? void 0 : _realData$total_organ.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Active Projects\",\n          value: ((_realData$total_proje = realData.total_projects) === null || _realData$total_proje === void 0 ? void 0 : _realData$total_proje.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe = realData.total_members) === null || _realData$total_membe === void 0 ? void 0 : _realData$total_membe.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti = realData.recent_activity) === null || _realData$recent_acti === void 0 ? void 0 : (_realData$recent_acti2 = _realData$recent_acti.length) === null || _realData$recent_acti2 === void 0 ? void 0 : _realData$recent_acti2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'admin':\n        return [{\n          title: \"Active Projects\",\n          value: ((_realData$total_proje2 = realData.total_projects) === null || _realData$total_proje2 === void 0 ? void 0 : _realData$total_proje2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe2 = realData.total_members) === null || _realData$total_membe2 === void 0 ? void 0 : _realData$total_membe2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ2 = realData.total_organizations) === null || _realData$total_organ2 === void 0 ? void 0 : _realData$total_organ2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti3 = realData.recent_activity) === null || _realData$recent_acti3 === void 0 ? void 0 : (_realData$recent_acti4 = _realData$recent_acti3.length) === null || _realData$recent_acti4 === void 0 ? void 0 : _realData$recent_acti4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'member':\n        return [{\n          title: \"My Projects\",\n          value: ((_realData$total_proje3 = realData.total_projects) === null || _realData$total_proje3 === void 0 ? void 0 : _realData$total_proje3.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ3 = realData.total_organizations) === null || _realData$total_organ3 === void 0 ? void 0 : _realData$total_organ3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe3 = realData.total_members) === null || _realData$total_membe3 === void 0 ? void 0 : _realData$total_membe3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti5 = realData.recent_activity) === null || _realData$recent_acti5 === void 0 ? void 0 : (_realData$recent_acti6 = _realData$recent_acti5.length) === null || _realData$recent_acti6 === void 0 ? void 0 : _realData$recent_acti6.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'viewer':\n        return [{\n          title: \"Projects Viewed\",\n          value: ((_realData$total_proje4 = realData.total_projects) === null || _realData$total_proje4 === void 0 ? void 0 : _realData$total_proje4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Eye\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ4 = realData.total_organizations) === null || _realData$total_organ4 === void 0 ? void 0 : _realData$total_organ4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe4 = realData.total_members) === null || _realData$total_membe4 === void 0 ? void 0 : _realData$total_membe4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Activity Items\",\n          value: ((_realData$recent_acti7 = realData.recent_activity) === null || _realData$recent_acti7 === void 0 ? void 0 : (_realData$recent_acti8 = _realData$recent_acti7.length) === null || _realData$recent_acti8 === void 0 ? void 0 : _realData$recent_acti8.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"BarChart3\",\n          color: \"warning\"\n        }];\n      default:\n        return [{\n          title: \"Projects\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Members\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Activity\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = (projects.length > 0 ? projects : mockProjects).filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) || (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n  // Role switcher for demo purposes\n  const handleRoleChange = newRole => {\n    setUserRole(newRole);\n  };\n\n  // Get current organization data\n  const currentOrganization = organizations.length > 0 ? {\n    name: ((_organizations$0$orga = organizations[0].organization) === null || _organizations$0$orga === void 0 ? void 0 : _organizations$0$orga.name) || 'Your Organization',\n    domain: (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$email = currentUser.email) === null || _currentUser$email === void 0 ? void 0 : _currentUser$email.split('@')[1]) || 'company.com',\n    logo: ((_organizations$0$orga2 = organizations[0].organization) === null || _organizations$0$orga2 === void 0 ? void 0 : _organizations$0$orga2.logo_url) || '/assets/images/org-logo.png'\n  } : {\n    name: 'Your Organization',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 424,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"glass-effect border-b border-white/20 p-4 mt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-slate-700\",\n            children: [\"Demo Mode - Current Role: \", userRole]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 445,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 443,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: ['Owner', 'Admin', 'Member', 'Viewer'].map(role => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRoleChange(role),\n            className: `px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${userRole === role ? 'bg-white text-slate-800 shadow-md' : 'text-slate-600 hover:bg-white/50 hover:text-slate-800'}`,\n            children: role\n          }, role, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 442,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DashboardHeader, {\n      userRole: userRole,\n      onFilterChange: setFilterValue,\n      onSearchChange: setSearchValue,\n      searchValue: searchValue\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 466,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\",\n        children: getKPIData().map((kpi, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in\",\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(KPICard, {\n            title: kpi.title,\n            value: kpi.value,\n            change: kpi.change,\n            changeType: kpi.changeType,\n            icon: kpi.icon,\n            color: kpi.color\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 476,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2 space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-semibold text-slate-800 tracking-tight\",\n                  children: userRole === 'Viewer' ? 'Available Projects' : 'My Projects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 499,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 mt-1\",\n                  children: \"Manage and track your active projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 502,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 498,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/project-overview-analytics\",\n                className: \"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",\n                children: [\"View Analytics\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 510,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 509,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 504,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 497,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(ProjectCard, {\n                  project: project,\n                  userRole: userRole\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 518,\n                  columnNumber: 21\n                }, this)\n              }, project.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 517,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 515,\n              columnNumber: 15\n            }, this), filteredProjects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-slate-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 530,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 528,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-600 text-lg\",\n                children: \"No projects match your current filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 533,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-500 text-sm mt-1\",\n                children: \"Try adjusting your search or filter criteria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 534,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 496,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(QuickActions, {\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(ActivityFeed, {\n            activities: mockActivities,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationPanel, {\n            notifications: mockNotifications,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 544,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 492,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(TaskSummary, {\n          tasks: mockTasks,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TeamOverview, {\n          teamMembers: mockTeamMembers,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 553,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 474,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 422,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedDashboard, \"fSgEE4HqCPXR2hRjsVOuRdF11ao=\", false, function () {\n  return [useLocation];\n});\n_c = RoleBasedDashboard;\nexport default RoleBasedDashboard;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "RoleBasedHeader", "DashboardHeader", "KPICard", "ProjectCard", "ActivityFeed", "QuickActions", "TaskSummary", "TeamOverview", "NotificationPanel", "authService", "apiService", "jsxDEV", "_jsxDEV", "RoleBasedDashboard", "_s", "_organizations$0$orga", "_currentUser$email", "_organizations$0$orga2", "location", "userRole", "setUserRole", "searchValue", "setSearchValue", "filterValue", "setFilterValue", "dashboardData", "setDashboardData", "projects", "setProjects", "currentUser", "setCurrentUser", "organizations", "setOrganizations", "loading", "setLoading", "error", "setError", "showWelcome", "setShowWelcome", "welcomeMessage", "setWelcomeMessage", "loadDashboardData", "userResult", "getCurrentUser", "data", "user", "role", "getUserRole", "statsResult", "getDashboardStats", "organizationId", "getOrganizationId", "projectsResult", "getAll", "err", "console", "message", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "window", "reload", "mockProjects", "id", "name", "description", "status", "priority", "progress", "dueDate", "team", "avatar", "mockActivities", "type", "project", "timestamp", "Date", "now", "isPublic", "mockTasks", "title", "assignee", "mockTeamMembers", "email", "department", "currentTask", "tasksCompleted", "lastActive", "mockNotifications", "read", "actions", "label", "variant", "getKPIData", "_realData$total_organ", "_realData$total_proje", "_realData$total_membe", "_realData$recent_acti", "_realData$recent_acti2", "_realData$total_proje2", "_realData$total_membe2", "_realData$total_organ2", "_realData$recent_acti3", "_realData$recent_acti4", "_realData$total_proje3", "_realData$total_organ3", "_realData$total_membe3", "_realData$recent_acti5", "_realData$recent_acti6", "_realData$total_proje4", "_realData$total_organ4", "_realData$total_membe4", "_realData$recent_acti7", "_realData$recent_acti8", "realData", "value", "total_organizations", "toString", "change", "changeType", "icon", "color", "total_projects", "total_members", "recent_activity", "length", "filteredProjects", "filter", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "handleRoleChange", "newRole", "currentOrganization", "organization", "domain", "split", "logo", "logo_url", "firstName", "lastName", "map", "onFilterChange", "onSearchChange", "kpi", "index", "style", "animationDelay", "to", "activities", "notifications", "tasks", "teamMembers", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/role-based-dashboard/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\n\nconst RoleBasedDashboard = () => {\n  const location = useLocation();\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        setLoading(true);\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n        }\n\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadDashboardData();\n  }, []);\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-slate-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <p className=\"text-red-600 text-lg mb-2\">Failed to load dashboard</p>\n          <p className=\"text-slate-600 text-sm\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Mock data for different dashboard components (fallback)\n  const mockProjects = [\n    {\n      id: 1,\n      name: \"E-commerce Platform Redesign\",\n      description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n      status: \"Active\",\n      priority: \"High\",\n      progress: 75,\n      dueDate: \"Dec 15, 2025\",\n      team: [\n        { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n        { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n        { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n        { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" }\n      ]\n    },\n    {\n      id: 2,\n      name: \"Mobile App Development\",\n      description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n      status: \"Active\",\n      priority: \"Medium\",\n      progress: 45,\n      dueDate: \"Jan 30, 2026\",\n      team: [\n        { name: \"David Kim\", avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\" },\n        { name: \"Lisa Wang\", avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\" },\n        { name: \"Tom Wilson\", avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\" }\n      ]\n    },\n    {\n      id: 3,\n      name: \"Data Analytics Dashboard\",\n      description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n      status: \"Completed\",\n      priority: \"Low\",\n      progress: 100,\n      dueDate: \"Nov 20, 2025\",\n      team: [\n        { name: \"Rachel Green\", avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\" },\n        { name: \"James Brown\", avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\" }\n      ]\n    }\n  ];\n\n  const mockActivities = [\n    {\n      id: 1,\n      type: \"task_completed\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      description: \"Completed the user interface mockups for the checkout process\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 15 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 2,\n      type: \"comment_added\",\n      user: { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n      description: \"Added feedback on the mobile responsive design implementation\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 45 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 3,\n      type: \"project_created\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      description: \"Created new project for Q1 marketing campaign automation\",\n      project: \"Marketing Automation\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      isPublic: false\n    },\n    {\n      id: 4,\n      type: \"member_added\",\n      user: { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" },\n      description: \"Joined the mobile app development team as a senior developer\",\n      project: \"Mobile App Development\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      isPublic: true\n    }\n  ];\n\n  const mockTasks = [\n    {\n      id: 1,\n      title: \"Implement payment gateway integration\",\n      status: \"In Progress\",\n      priority: \"High\",\n      dueDate: \"Dec 10, 2025\",\n      assignee: \"Sarah Johnson\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 2,\n      title: \"Design user onboarding flow\",\n      status: \"To Do\",\n      priority: \"Medium\",\n      dueDate: \"Dec 12, 2025\",\n      assignee: \"Mike Chen\",\n      project: \"Mobile App\"\n    },\n    {\n      id: 3,\n      title: \"Set up automated testing pipeline\",\n      status: \"Review\",\n      priority: \"High\",\n      dueDate: \"Dec 8, 2025\",\n      assignee: \"Emily Davis\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 4,\n      title: \"Create API documentation\",\n      status: \"Done\",\n      priority: \"Low\",\n      dueDate: \"Dec 5, 2025\",\n      assignee: \"Alex Rodriguez\",\n      project: \"Data Analytics\"\n    },\n    {\n      id: 5,\n      title: \"Optimize database queries\",\n      status: \"Blocked\",\n      priority: \"High\",\n      dueDate: \"Dec 15, 2025\",\n      assignee: \"David Kim\",\n      project: \"E-commerce Platform\"\n    }\n  ];\n\n  const mockTeamMembers = [\n    {\n      id: 1,\n      name: \"Sarah Johnson\",\n      email: \"<EMAIL>\",\n      role: \"Admin\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n      department: \"Design\",\n      currentTask: \"UI/UX Design Review\",\n      tasksCompleted: 24,\n      lastActive: new Date()\n    },\n    {\n      id: 2,\n      name: \"Mike Chen\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Away\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n      department: \"Development\",\n      currentTask: \"Frontend Implementation\",\n      tasksCompleted: 18,\n      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n    },\n    {\n      id: 3,\n      name: \"Emily Davis\",\n      email: \"<EMAIL>\",\n      role: \"Owner\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n      department: \"Management\",\n      currentTask: \"Project Planning\",\n      tasksCompleted: 32,\n      lastActive: new Date()\n    },\n    {\n      id: 4,\n      name: \"Alex Rodriguez\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Busy\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n      department: \"Development\",\n      currentTask: \"API Integration\",\n      tasksCompleted: 15,\n      lastActive: new Date(Date.now() - 30 * 60 * 1000)\n    },\n    {\n      id: 5,\n      name: \"David Kim\",\n      email: \"<EMAIL>\",\n      role: \"Viewer\",\n      status: \"Offline\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n      department: \"QA\",\n      currentTask: null,\n      tasksCompleted: 8,\n      lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n    }\n  ];\n\n  const mockNotifications = [\n    {\n      id: 1,\n      type: \"task_assigned\",\n      title: \"New Task Assigned\",\n      message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n      timestamp: new Date(Date.now() - 30 * 60 * 1000),\n      read: false,\n      priority: \"high\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      actions: [\n        { label: \"View Task\", variant: \"default\" },\n        { label: \"Accept\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 2,\n      type: \"deadline_reminder\",\n      title: \"Deadline Approaching\",\n      message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      read: false,\n      priority: \"medium\",\n      user: null,\n      actions: [\n        { label: \"View Details\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 3,\n      type: \"comment_mention\",\n      title: \"You were mentioned\",\n      message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      read: true,\n      priority: \"low\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      actions: [\n        { label: \"Reply\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 4,\n      type: \"system_alert\",\n      title: \"System Maintenance\",\n      message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n      read: true,\n      priority: \"medium\",\n      user: null,\n      actions: []\n    }\n  ];\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    const realData = dashboardData || {};\n\n    switch (userRole) {\n      case 'owner':\n        return [\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'admin':\n        return [\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'member':\n        return [\n          { title: \"My Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'viewer':\n        return [\n          { title: \"Projects Viewed\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Eye\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Activity Items\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"BarChart3\", color: \"warning\" }\n        ];\n      default:\n        return [\n          { title: \"Projects\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Members\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Activity\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = (projects.length > 0 ? projects : mockProjects).filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||\n                         (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' ||\n                         (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n  // Role switcher for demo purposes\n  const handleRoleChange = (newRole) => {\n    setUserRole(newRole);\n  };\n\n  // Get current organization data\n  const currentOrganization = organizations.length > 0 ? {\n    name: organizations[0].organization?.name || 'Your Organization',\n    domain: currentUser?.email?.split('@')[1] || 'company.com',\n    logo: organizations[0].organization?.logo_url || '/assets/images/org-logo.png'\n  } : {\n    name: 'Your Organization',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\">\n      {/* Role-Based Header */}\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Enhanced Demo Role Switcher */}\n      <div className=\"glass-effect border-b border-white/20 p-4 mt-16\">\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-slate-700\">Demo Mode - Current Role: {userRole}</span>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            {['Owner', 'Admin', 'Member', 'Viewer'].map((role) => (\n              <button\n                key={role}\n                onClick={() => handleRoleChange(role)}\n                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                  userRole === role\n                    ? 'bg-white text-slate-800 shadow-md'\n                    : 'text-slate-600 hover:bg-white/50 hover:text-slate-800'\n                }`}\n              >\n                {role}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Dashboard Header */}\n      <DashboardHeader\n        userRole={userRole}\n        onFilterChange={setFilterValue}\n        onSearchChange={setSearchValue}\n        searchValue={searchValue}\n      />\n\n      {/* Main Dashboard Content */}\n      <div className=\"max-w-7xl mx-auto p-8\">\n        {/* Enhanced KPI Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\">\n          {getKPIData().map((kpi, index) => (\n            <div key={index} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <KPICard\n                title={kpi.title}\n                value={kpi.value}\n                change={kpi.change}\n                changeType={kpi.changeType}\n                icon={kpi.icon}\n                color={kpi.color}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content Grid with improved spacing */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\">\n          {/* Left Column - Projects and Quick Actions */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Projects Section with enhanced header */}\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h2 className=\"text-2xl font-semibold text-slate-800 tracking-tight\">\n                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}\n                  </h2>\n                  <p className=\"text-slate-600 mt-1\">Manage and track your active projects</p>\n                </div>\n                <Link \n                  to=\"/project-overview-analytics\"\n                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n                >\n                  View Analytics\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n                {filteredProjects.map((project, index) => (\n                  <div key={project.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                    <ProjectCard\n                      project={project}\n                      userRole={userRole}\n                    />\n                  </div>\n                ))}\n              </div>\n              \n              {filteredProjects.length === 0 && (\n                <div className=\"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\">\n                  <div className=\"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-8 h-8 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-slate-600 text-lg\">No projects match your current filters</p>\n                  <p className=\"text-slate-500 text-sm mt-1\">Try adjusting your search or filter criteria</p>\n                </div>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <QuickActions userRole={userRole} />\n          </div>\n\n          {/* Right Column - Activity Feed and Notifications */}\n          <div className=\"space-y-8\">\n            <ActivityFeed activities={mockActivities} userRole={userRole} />\n            <NotificationPanel notifications={mockNotifications} userRole={userRole} />\n          </div>\n        </div>\n\n        {/* Bottom Section - Tasks and Team with improved layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          <TaskSummary tasks={mockTasks} userRole={userRole} />\n          <TeamOverview teamMembers={mockTeamMembers} userRole={userRole} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RoleBasedDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA;EAC/B,MAAMC,QAAQ,GAAGnB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACyB,WAAW,EAAEC,cAAc,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6B,aAAa,EAAEC,gBAAgB,CAAC,GAAG9B,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAAC+B,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiC,WAAW,EAAEC,cAAc,CAAC,GAAGlC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACmC,aAAa,EAAEC,gBAAgB,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC2C,cAAc,EAAEC,iBAAiB,CAAC,GAAG5C,QAAQ,CAAC,EAAE,CAAC;;EAExD;EACAC,SAAS,CAAC,MAAM;IACd,MAAM4C,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACFP,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMQ,UAAU,GAAG,MAAMjC,WAAW,CAACkC,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,CAACE,IAAI,CAACC,IAAI,EAAE;UACxBf,cAAc,CAACY,UAAU,CAACE,IAAI,CAACC,IAAI,CAAC;UACpCzB,WAAW,CAACsB,UAAU,CAACE,IAAI,CAACC,IAAI,CAACC,IAAI,IAAIrC,WAAW,CAACsC,WAAW,CAAC,CAAC,CAAC;UACnEf,gBAAgB,CAACU,UAAU,CAACE,IAAI,CAACb,aAAa,IAAI,EAAE,CAAC;QACvD;;QAEA;QACA,MAAMiB,WAAW,GAAG,MAAMvC,WAAW,CAACwC,iBAAiB,CAAC,CAAC;QACzD,IAAID,WAAW,CAACJ,IAAI,EAAE;UACpBlB,gBAAgB,CAACsB,WAAW,CAACJ,IAAI,CAAC;QACpC;;QAEA;QACA,MAAMM,cAAc,GAAGzC,WAAW,CAAC0C,iBAAiB,CAAC,CAAC;QACtD,IAAID,cAAc,EAAE;UAClB,MAAME,cAAc,GAAG,MAAM1C,UAAU,CAACiB,QAAQ,CAAC0B,MAAM,CAACH,cAAc,CAAC;UACvEtB,WAAW,CAACwB,cAAc,IAAI,EAAE,CAAC;QACnC;QAEAhB,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOkB,GAAG,EAAE;QACZC,OAAO,CAACpB,KAAK,CAAC,gCAAgC,EAAEmB,GAAG,CAAC;QACpDlB,QAAQ,CAACkB,GAAG,CAACE,OAAO,CAAC;MACvB,CAAC,SAAS;QACRtB,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDO,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,IAAIR,OAAO,EAAE;IACX,oBACErB,OAAA;MAAK6C,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3H9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA;UAAK6C,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnGlD,OAAA;UAAG6C,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAI3B,KAAK,EAAE;IACT,oBACEvB,OAAA;MAAK6C,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3H9C,OAAA;QAAK6C,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1B9C,OAAA;UAAK6C,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9F9C,OAAA;YAAK6C,SAAS,EAAC,sBAAsB;YAACM,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAP,QAAA,eACzF9C,OAAA;cAAMsD,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAmD;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNlD,OAAA;UAAG6C,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrElD,OAAA;UAAG6C,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAEvB;QAAK;UAAAwB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjDlD,OAAA;UACE0D,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACrD,QAAQ,CAACsD,MAAM,CAAC,CAAE;UACxCf,SAAS,EAAC,sFAAsF;UAAAC,QAAA,EACjG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMW,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,8BAA8B;IACpCC,WAAW,EAAE,uHAAuH;IACpIC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACvG;MAAEP,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEP,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACrG;MAAEP,IAAI,EAAE,gBAAgB;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAE5G,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE,wGAAwG;IACrHC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEP,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAkE,CAAC,EAChG;MAAEP,IAAI,EAAE,YAAY;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAExG,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,0BAA0B;IAChCC,WAAW,EAAE,sHAAsH;IACnIC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,cAAc;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACtG;MAAEP,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAEzG,CAAC,CACF;EAED,MAAMC,cAAc,GAAG,CACrB;IACET,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,gBAAgB;IACtBvC,IAAI,EAAE;MAAE8B,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC7GN,WAAW,EAAE,+DAA+D;IAC5ES,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,eAAe;IACrBvC,IAAI,EAAE;MAAE8B,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC;IACzGN,WAAW,EAAE,+DAA+D;IAC5ES,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,iBAAiB;IACvBvC,IAAI,EAAE;MAAE8B,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC3GN,WAAW,EAAE,0DAA0D;IACvES,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,cAAc;IACpBvC,IAAI,EAAE;MAAE8B,IAAI,EAAE,gBAAgB;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC9GN,WAAW,EAAE,8DAA8D;IAC3ES,OAAO,EAAE,wBAAwB;IACjCC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IACEhB,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,uCAAuC;IAC9Cd,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBY,QAAQ,EAAE,eAAe;IACzBP,OAAO,EAAE;EACX,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,6BAA6B;IACpCd,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,QAAQ;IAClBE,OAAO,EAAE,cAAc;IACvBY,QAAQ,EAAE,WAAW;IACrBP,OAAO,EAAE;EACX,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,mCAAmC;IAC1Cd,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,aAAa;IACtBY,QAAQ,EAAE,aAAa;IACvBP,OAAO,EAAE;EACX,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,0BAA0B;IACjCd,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,KAAK;IACfE,OAAO,EAAE,aAAa;IACtBY,QAAQ,EAAE,gBAAgB;IAC1BP,OAAO,EAAE;EACX,CAAC,EACD;IACEX,EAAE,EAAE,CAAC;IACLiB,KAAK,EAAE,2BAA2B;IAClCd,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBY,QAAQ,EAAE,WAAW;IACrBP,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMQ,eAAe,GAAG,CACtB;IACEnB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBmB,KAAK,EAAE,wBAAwB;IAC/BhD,IAAI,EAAE,OAAO;IACb+B,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5Ea,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAAC;EACvB,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBmB,KAAK,EAAE,oBAAoB;IAC3BhD,IAAI,EAAE,QAAQ;IACd+B,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5Ea,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,yBAAyB;IACtCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACtD,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBmB,KAAK,EAAE,sBAAsB;IAC7BhD,IAAI,EAAE,OAAO;IACb+B,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5Ea,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAAC;EACvB,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBmB,KAAK,EAAE,yBAAyB;IAChChD,IAAI,EAAE,QAAQ;IACd+B,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5Ea,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,iBAAiB;IAC9BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EAClD,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBmB,KAAK,EAAE,oBAAoB;IAC3BhD,IAAI,EAAE,QAAQ;IACd+B,MAAM,EAAE,SAAS;IACjBK,MAAM,EAAE,oEAAoE;IAC5Ea,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACvD,CAAC,CACF;EAED,MAAMW,iBAAiB,GAAG,CACxB;IACEzB,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,eAAe;IACrBO,KAAK,EAAE,mBAAmB;IAC1BnC,OAAO,EAAE,gGAAgG;IACzG8B,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDY,IAAI,EAAE,KAAK;IACXtB,QAAQ,EAAE,MAAM;IAChBjC,IAAI,EAAE;MAAE8B,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC3GmB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAU,CAAC,EAC1C;MAAED,KAAK,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE3C,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,mBAAmB;IACzBO,KAAK,EAAE,sBAAsB;IAC7BnC,OAAO,EAAE,oGAAoG;IAC7G8B,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDY,IAAI,EAAE,KAAK;IACXtB,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,IAAI;IACVwD,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAU,CAAC;EAEjD,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,iBAAiB;IACvBO,KAAK,EAAE,oBAAoB;IAC3BnC,OAAO,EAAE,iFAAiF;IAC1F8B,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDY,IAAI,EAAE,IAAI;IACVtB,QAAQ,EAAE,KAAK;IACfjC,IAAI,EAAE;MAAE8B,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC7GmB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE1C,CAAC,EACD;IACE7B,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,cAAc;IACpBO,KAAK,EAAE,oBAAoB;IAC3BnC,OAAO,EAAE,gHAAgH;IACzH8B,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDY,IAAI,EAAE,IAAI;IACVtB,QAAQ,EAAE,QAAQ;IAClBjC,IAAI,EAAE,IAAI;IACVwD,OAAO,EAAE;EACX,CAAC,CACF;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACvB,MAAMC,QAAQ,GAAGpG,aAAa,IAAI,CAAC,CAAC;IAEpC,QAAQN,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,CACL;UAAEwE,KAAK,EAAE,eAAe;UAAEmC,KAAK,EAAE,EAAArB,qBAAA,GAAAoB,QAAQ,CAACE,mBAAmB,cAAAtB,qBAAA,uBAA5BA,qBAAA,CAA8BuB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAEzC,KAAK,EAAE,iBAAiB;UAAEmC,KAAK,EAAE,EAAApB,qBAAA,GAAAmB,QAAQ,CAACQ,cAAc,cAAA3B,qBAAA,uBAAvBA,qBAAA,CAAyBsB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAEzC,KAAK,EAAE,cAAc;UAAEmC,KAAK,EAAE,EAAAnB,qBAAA,GAAAkB,QAAQ,CAACS,aAAa,cAAA3B,qBAAA,uBAAtBA,qBAAA,CAAwBqB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAEzC,KAAK,EAAE,iBAAiB;UAAEmC,KAAK,EAAE,EAAAlB,qBAAA,GAAAiB,QAAQ,CAACU,eAAe,cAAA3B,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0B4B,MAAM,cAAA3B,sBAAA,uBAAhCA,sBAAA,CAAkCmB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEzC,KAAK,EAAE,iBAAiB;UAAEmC,KAAK,EAAE,EAAAhB,sBAAA,GAAAe,QAAQ,CAACQ,cAAc,cAAAvB,sBAAA,uBAAvBA,sBAAA,CAAyBkB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAEzC,KAAK,EAAE,cAAc;UAAEmC,KAAK,EAAE,EAAAf,sBAAA,GAAAc,QAAQ,CAACS,aAAa,cAAAvB,sBAAA,uBAAtBA,sBAAA,CAAwBiB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAEzC,KAAK,EAAE,eAAe;UAAEmC,KAAK,EAAE,EAAAd,sBAAA,GAAAa,QAAQ,CAACE,mBAAmB,cAAAf,sBAAA,uBAA5BA,sBAAA,CAA8BgB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAEzC,KAAK,EAAE,iBAAiB;UAAEmC,KAAK,EAAE,EAAAb,sBAAA,GAAAY,QAAQ,CAACU,eAAe,cAAAtB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BuB,MAAM,cAAAtB,sBAAA,uBAAhCA,sBAAA,CAAkCc,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEzC,KAAK,EAAE,aAAa;UAAEmC,KAAK,EAAE,EAAAX,sBAAA,GAAAU,QAAQ,CAACQ,cAAc,cAAAlB,sBAAA,uBAAvBA,sBAAA,CAAyBa,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACtJ;UAAEzC,KAAK,EAAE,eAAe;UAAEmC,KAAK,EAAE,EAAAV,sBAAA,GAAAS,QAAQ,CAACE,mBAAmB,cAAAX,sBAAA,uBAA5BA,sBAAA,CAA8BY,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAEzC,KAAK,EAAE,cAAc;UAAEmC,KAAK,EAAE,EAAAT,sBAAA,GAAAQ,QAAQ,CAACS,aAAa,cAAAjB,sBAAA,uBAAtBA,sBAAA,CAAwBW,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAEzC,KAAK,EAAE,iBAAiB;UAAEmC,KAAK,EAAE,EAAAR,sBAAA,GAAAO,QAAQ,CAACU,eAAe,cAAAjB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BkB,MAAM,cAAAjB,sBAAA,uBAAhCA,sBAAA,CAAkCS,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEzC,KAAK,EAAE,iBAAiB;UAAEmC,KAAK,EAAE,EAAAN,sBAAA,GAAAK,QAAQ,CAACQ,cAAc,cAAAb,sBAAA,uBAAvBA,sBAAA,CAAyBQ,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAC,EACnJ;UAAEzC,KAAK,EAAE,eAAe;UAAEmC,KAAK,EAAE,EAAAL,sBAAA,GAAAI,QAAQ,CAACE,mBAAmB,cAAAN,sBAAA,uBAA5BA,sBAAA,CAA8BO,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAEzC,KAAK,EAAE,cAAc;UAAEmC,KAAK,EAAE,EAAAJ,sBAAA,GAAAG,QAAQ,CAACS,aAAa,cAAAZ,sBAAA,uBAAtBA,sBAAA,CAAwBM,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAEzC,KAAK,EAAE,gBAAgB;UAAEmC,KAAK,EAAE,EAAAH,sBAAA,GAAAE,QAAQ,CAACU,eAAe,cAAAZ,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0Ba,MAAM,cAAAZ,sBAAA,uBAAhCA,sBAAA,CAAkCI,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH;QACE,OAAO,CACL;UAAEzC,KAAK,EAAE,UAAU;UAAEmC,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC5G;UAAEzC,KAAK,EAAE,eAAe;UAAEmC,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/G;UAAEzC,KAAK,EAAE,SAAS;UAAEmC,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EACrG;UAAEzC,KAAK,EAAE,UAAU;UAAEmC,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAC3G;IACL;EACF,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAG,CAAC9G,QAAQ,CAAC6G,MAAM,GAAG,CAAC,GAAG7G,QAAQ,GAAG8C,YAAY,EAAEiE,MAAM,CAACrD,OAAO,IAAI;IACzF,MAAMsD,aAAa,GAAGtD,OAAO,CAACV,IAAI,CAACiE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxH,WAAW,CAACuH,WAAW,CAAC,CAAC,CAAC,IAC/D,CAACvD,OAAO,CAACT,WAAW,IAAI,EAAE,EAAEgE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACxH,WAAW,CAACuH,WAAW,CAAC,CAAC,CAAC;IAClG,MAAME,aAAa,GAAGvH,WAAW,KAAK,KAAK,IACtB,CAAC8D,OAAO,CAACR,MAAM,IAAI,QAAQ,EAAE+D,WAAW,CAAC,CAAC,KAAKrH,WAAW,CAACqH,WAAW,CAAC,CAAC;IAC7F,OAAOD,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpC5H,WAAW,CAAC4H,OAAO,CAAC;EACtB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGlH,aAAa,CAACyG,MAAM,GAAG,CAAC,GAAG;IACrD7D,IAAI,EAAE,EAAA5D,qBAAA,GAAAgB,aAAa,CAAC,CAAC,CAAC,CAACmH,YAAY,cAAAnI,qBAAA,uBAA7BA,qBAAA,CAA+B4D,IAAI,KAAI,mBAAmB;IAChEwE,MAAM,EAAE,CAAAtH,WAAW,aAAXA,WAAW,wBAAAb,kBAAA,GAAXa,WAAW,CAAEiE,KAAK,cAAA9E,kBAAA,uBAAlBA,kBAAA,CAAoBoI,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,aAAa;IAC1DC,IAAI,EAAE,EAAApI,sBAAA,GAAAc,aAAa,CAAC,CAAC,CAAC,CAACmH,YAAY,cAAAjI,sBAAA,uBAA7BA,sBAAA,CAA+BqI,QAAQ,KAAI;EACnD,CAAC,GAAG;IACF3E,IAAI,EAAE,mBAAmB;IACzBwE,MAAM,EAAE,aAAa;IACrBE,IAAI,EAAE;EACR,CAAC;EAED,oBACEzI,OAAA;IAAK6C,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1F9C,OAAA,CAACZ,eAAe;MACdmB,QAAQ,EAAEA,QAAQ,CAACyH,WAAW,CAAC,CAAE;MACjC/G,WAAW,EAAEA,WAAW,GAAG;QACzB8C,IAAI,EAAE,GAAG9C,WAAW,CAAC0H,SAAS,IAAI1H,WAAW,CAAC2H,QAAQ,EAAE;QACxD1D,KAAK,EAAEjE,WAAW,CAACiE,KAAK;QACxBZ,MAAM,EAAErD,WAAW,CAACqD,MAAM,IAAI,2BAA2B;QACzDpC,IAAI,EAAE3B;MACR,CAAC,GAAG;QACFwD,IAAI,EAAE,YAAY;QAClBmB,KAAK,EAAE,EAAE;QACTZ,MAAM,EAAE,2BAA2B;QACnCpC,IAAI,EAAE3B;MACR,CAAE;MACF8H,mBAAmB,EAAEA;IAAoB;MAAAtF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFlD,OAAA;MAAK6C,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9D9C,OAAA;QAAK6C,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClE9C,OAAA;UAAK6C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9C,OAAA;YAAK6C,SAAS,EAAC;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvElD,OAAA;YAAM6C,SAAS,EAAC,oCAAoC;YAAAC,QAAA,GAAC,4BAA0B,EAACvC,QAAQ;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACNlD,OAAA;UAAK6C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EACrC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC+F,GAAG,CAAE3G,IAAI,iBAC/ClC,OAAA;YAEE0D,OAAO,EAAEA,CAAA,KAAMyE,gBAAgB,CAACjG,IAAI,CAAE;YACtCW,SAAS,EAAE,wEACTtC,QAAQ,KAAK2B,IAAI,GACb,mCAAmC,GACnC,uDAAuD,EAC1D;YAAAY,QAAA,EAEFZ;UAAI,GARAA,IAAI;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASH,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlD,OAAA,CAACX,eAAe;MACdkB,QAAQ,EAAEA,QAAS;MACnBuI,cAAc,EAAElI,cAAe;MAC/BmI,cAAc,EAAErI,cAAe;MAC/BD,WAAW,EAAEA;IAAY;MAAAsC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGFlD,OAAA;MAAK6C,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAEpC9C,OAAA;QAAK6C,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxE8C,UAAU,CAAC,CAAC,CAACiD,GAAG,CAAC,CAACG,GAAG,EAAEC,KAAK,kBAC3BjJ,OAAA;UAAiB6C,SAAS,EAAC,iBAAiB;UAACqG,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAnG,QAAA,eACxF9C,OAAA,CAACV,OAAO;YACNyF,KAAK,EAAEiE,GAAG,CAACjE,KAAM;YACjBmC,KAAK,EAAE8B,GAAG,CAAC9B,KAAM;YACjBG,MAAM,EAAE2B,GAAG,CAAC3B,MAAO;YACnBC,UAAU,EAAE0B,GAAG,CAAC1B,UAAW;YAC3BC,IAAI,EAAEyB,GAAG,CAACzB,IAAK;YACfC,KAAK,EAAEwB,GAAG,CAACxB;UAAM;YAAAzE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GARM+F,KAAK;UAAAlG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1D9C,OAAA;UAAK6C,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtC9C,OAAA;YAAK6C,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9C,OAAA;cAAK6C,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD9C,OAAA;gBAAA8C,QAAA,gBACE9C,OAAA;kBAAI6C,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EACjEvC,QAAQ,KAAK,QAAQ,GAAG,oBAAoB,GAAG;gBAAa;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACLlD,OAAA;kBAAG6C,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNlD,OAAA,CAACd,IAAI;gBACHkK,EAAE,EAAC,6BAA6B;gBAChCvG,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,GAC3K,gBAEC,eAAA9C,OAAA;kBAAK6C,SAAS,EAAC,SAAS;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC5E9C,OAAA;oBAAMsD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENlD,OAAA;cAAK6C,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD+E,gBAAgB,CAACgB,GAAG,CAAC,CAACpE,OAAO,EAAEwE,KAAK,kBACnCjJ,OAAA;gBAAsB6C,SAAS,EAAC,iBAAiB;gBAACqG,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAAnG,QAAA,eAC7F9C,OAAA,CAACT,WAAW;kBACVkF,OAAO,EAAEA,OAAQ;kBACjBlE,QAAQ,EAAEA;gBAAS;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC,GAJMuB,OAAO,CAACX,EAAE;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAEL2E,gBAAgB,CAACD,MAAM,KAAK,CAAC,iBAC5B5H,OAAA;cAAK6C,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAChG9C,OAAA;gBAAK6C,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChG9C,OAAA;kBAAK6C,SAAS,EAAC,wBAAwB;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC3F9C,OAAA;oBAAMsD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAsH;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlD,OAAA;gBAAG6C,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChFlD,OAAA;gBAAG6C,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlD,OAAA,CAACP,YAAY;YAACc,QAAQ,EAAEA;UAAS;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAGNlD,OAAA;UAAK6C,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9C,OAAA,CAACR,YAAY;YAAC6J,UAAU,EAAE9E,cAAe;YAAChE,QAAQ,EAAEA;UAAS;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChElD,OAAA,CAACJ,iBAAiB;YAAC0J,aAAa,EAAE/D,iBAAkB;YAAChF,QAAQ,EAAEA;UAAS;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlD,OAAA;QAAK6C,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9C,OAAA,CAACN,WAAW;UAAC6J,KAAK,EAAEzE,SAAU;UAACvE,QAAQ,EAAEA;QAAS;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDlD,OAAA,CAACL,YAAY;UAAC6J,WAAW,EAAEvE,eAAgB;UAAC1E,QAAQ,EAAEA;QAAS;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChD,EAAA,CA/hBID,kBAAkB;EAAA,QACLd,WAAW;AAAA;AAAAsK,EAAA,GADxBxJ,kBAAkB;AAiiBxB,eAAeA,kBAAkB;AAAC,IAAAwJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}