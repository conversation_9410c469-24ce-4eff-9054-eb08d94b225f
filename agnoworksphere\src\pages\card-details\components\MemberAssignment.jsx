import React, { useState, useRef, useEffect } from 'react';
import Icon from '../../../components/AppIcon';
import Button from '../../../components/ui/Button';
import Image from '../../../components/AppImage';

const MemberAssignment = ({ card, onMembersChange, canEdit }) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);

  // Mock organization members
  const organizationMembers = [
    {
      id: 1,
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      role: 'Project Manager'
    },
    {
      id: 2,
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      role: 'Developer'
    },
    {
      id: 3,
      name: '<PERSON>',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
      role: 'Designer'
    },
    {
      id: 4,
      name: 'David Kim',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      role: 'QA Engineer'
    },
    {
      id: 5,
      name: 'Lisa Wang',
      email: '<EMAIL>',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150',
      role: 'Business Analyst'
    }
  ];

  const filteredMembers = organizationMembers.filter(member =>
    member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const assignedMembers = organizationMembers.filter(member =>
    card.assignedMembers?.includes(member.id)
  );

  const handleMemberToggle = (memberId) => {
    const currentMembers = card.assignedMembers || [];
    const updatedMembers = currentMembers.includes(memberId)
      ? currentMembers.filter(id => id !== memberId)
      : [...currentMembers, memberId];
    
    onMembersChange(updatedMembers);
  };

  const handleRemoveMember = (memberId) => {
    const updatedMembers = (card.assignedMembers || []).filter(id => id !== memberId);
    onMembersChange(updatedMembers);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Icon name="Users" size={16} className="text-text-secondary" />
          <h4 className="font-medium text-text-primary">Members</h4>
        </div>
        {canEdit && (
          <div className="relative" ref={dropdownRef}>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsDropdownOpen(!isDropdownOpen)}
            >
              <Icon name="Plus" size={16} />
            </Button>

            {isDropdownOpen && (
              <div className="absolute top-full right-0 mt-1 w-72 bg-popover border border-border rounded-md shadow-elevated z-1010">
                <div className="p-3">
                  <div className="relative mb-3">
                    <Icon name="Search" size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary" />
                    <input
                      type="text"
                      placeholder="Search members..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-border rounded-md text-sm focus:ring-2 focus:ring-primary focus:border-transparent"
                    />
                  </div>
                  
                  <div className="max-h-48 overflow-y-auto space-y-1">
                    {filteredMembers.map((member) => (
                      <button
                        key={member.id}
                        onClick={() => handleMemberToggle(member.id)}
                        className="w-full flex items-center space-x-3 p-2 rounded-md hover:bg-muted transition-micro text-left"
                      >
                        <Image
                          src={member.avatar}
                          alt={member.name}
                          className="w-8 h-8 rounded-full object-cover"
                        />
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-text-primary truncate">{member.name}</div>
                          <div className="text-xs text-text-secondary truncate">{member.role}</div>
                        </div>
                        {card.assignedMembers?.includes(member.id) && (
                          <Icon name="Check" size={16} className="text-success" />
                        )}
                      </button>
                    ))}
                    
                    {filteredMembers.length === 0 && (
                      <div className="text-center py-4 text-text-secondary text-sm">
                        No members found
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="space-y-2">
        {assignedMembers.length > 0 ? (
          assignedMembers.map((member) => (
            <div key={member.id} className="flex items-center justify-between p-2 bg-muted rounded-md">
              <div className="flex items-center space-x-3">
                <Image
                  src={member.avatar}
                  alt={member.name}
                  className="w-8 h-8 rounded-full object-cover"
                />
                <div>
                  <div className="font-medium text-text-primary text-sm">{member.name}</div>
                  <div className="text-xs text-text-secondary">{member.role}</div>
                </div>
              </div>
              {canEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleRemoveMember(member.id)}
                  className="text-text-secondary hover:text-destructive"
                >
                  <Icon name="X" size={14} />
                </Button>
              )}
            </div>
          ))
        ) : (
          <div className="text-text-secondary text-sm italic">No members assigned</div>
        )}
      </div>
    </div>
  );
};

export default MemberAssignment;