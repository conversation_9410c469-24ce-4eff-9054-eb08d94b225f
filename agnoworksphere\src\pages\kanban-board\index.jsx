import React, { useState, useEffect } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/ui/Header';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import BoardHeader from './components/BoardHeader';
import BoardColumn from './components/BoardColumn';

import AddCardModal from './components/AddCardModal';
import AddColumnModal from './components/AddColumnModal';
import InviteMemberModal from './components/InviteMemberModal';

const KanbanBoard = () => {
  const navigate = useNavigate();
  
  // Mock data
  const [board] = useState({
    id: 'board-1',
    title: 'Project Management Board',
    description: 'Main project tracking board for Q4 initiatives',
    isPrivate: false,
    createdAt: '2025-01-15T10:00:00Z',
    updatedAt: '2025-01-28T05:54:23Z'
  });

  const [members] = useState([
    {
      id: 'user-1',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      role: 'Admin',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 'user-2',
      name: 'Michael Chen',
      email: '<EMAIL>',
      role: 'Member',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 'user-3',
      name: 'Emily Rodriguez',
      email: '<EMAIL>',
      role: 'Member',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 'user-4',
      name: 'David Kim',
      email: '<EMAIL>',
      role: 'Viewer',
      avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face'
    },
    {
      id: 'user-5',
      name: 'Lisa Wang',
      email: '<EMAIL>',
      role: 'Member',
      avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face'
    }
  ]);

  const [columns, setColumns] = useState([
    {
      id: 'col-1',
      title: 'To Do',
      status: 'todo',
      order: 1,
      createdAt: '2025-01-15T10:00:00Z'
    },
    {
      id: 'col-2',
      title: 'In Progress',
      status: 'in-progress',
      order: 2,
      createdAt: '2025-01-15T10:00:00Z'
    },
    {
      id: 'col-3',
      title: 'Review',
      status: 'review',
      order: 3,
      createdAt: '2025-01-15T10:00:00Z'
    },
    {
      id: 'col-4',
      title: 'Done',
      status: 'done',
      order: 4,
      createdAt: '2025-01-15T10:00:00Z'
    }
  ]);

  const [cards, setCards] = useState([
    {
      id: 'card-1',
      columnId: 'col-1',
      title: 'Design user authentication flow',
      description: 'Create wireframes and mockups for the login and registration process',
      priority: 'high',
      assignedTo: ['user-1', 'user-2'],
      dueDate: '2025-02-05',
      labels: [
        { id: 'design', name: 'Design', color: '#3b82f6' },
        { id: 'ux', name: 'UX', color: '#8b5cf6' }
      ],
      checklist: [
        { id: 'check-1', text: 'Research competitor flows', completed: true },
        { id: 'check-2', text: 'Create wireframes', completed: false },
        { id: 'check-3', text: 'Design mockups', completed: false }
      ],
      comments: [
        {
          id: 'comment-1',
          author: 'user-2',
          content: 'Should we include social login options?',
          createdAt: '2025-01-27T14:30:00Z'
        }
      ],
      attachments: [],
      createdAt: '2025-01-25T09:00:00Z',
      updatedAt: '2025-01-27T14:30:00Z'
    },
    {
      id: 'card-2',
      columnId: 'col-1',
      title: 'Set up project repository',
      description: 'Initialize Git repository with proper folder structure and documentation',
      priority: 'medium',
      assignedTo: ['user-3'],
      dueDate: '2025-01-30',
      labels: [
        { id: 'development', name: 'Development', color: '#10b981' }
      ],
      checklist: [],
      comments: [],
      attachments: [],
      createdAt: '2025-01-26T11:00:00Z',
      updatedAt: '2025-01-26T11:00:00Z'
    },
    {
      id: 'card-3',
      columnId: 'col-2',
      title: 'Implement user registration API',
      description: 'Build backend endpoints for user registration with validation and email verification',
      priority: 'high',
      assignedTo: ['user-2', 'user-5'],
      dueDate: '2025-02-10',
      labels: [
        { id: 'backend', name: 'Backend', color: '#f59e0b' },
        { id: 'api', name: 'API', color: '#ef4444' }
      ],
      checklist: [
        { id: 'check-4', text: 'Design database schema', completed: true },
        { id: 'check-5', text: 'Implement validation', completed: true },
        { id: 'check-6', text: 'Add email verification', completed: false },
        { id: 'check-7', text: 'Write unit tests', completed: false }
      ],
      comments: [
        {
          id: 'comment-2',
          author: 'user-1',
          content: 'Make sure to include proper error handling',
          createdAt: '2025-01-26T16:45:00Z'
        },
        {
          id: 'comment-3',
          author: 'user-5',
          content: 'Working on the email service integration',
          createdAt: '2025-01-27T10:15:00Z'
        }
      ],
      attachments: [
        { id: 'att-1', name: 'api-spec.pdf', size: '2.4 MB' }
      ],
      createdAt: '2025-01-24T13:00:00Z',
      updatedAt: '2025-01-27T10:15:00Z'
    },
    {
      id: 'card-4',
      columnId: 'col-3',
      title: 'Review dashboard components',
      description: 'Code review for the new dashboard UI components',
      priority: 'medium',
      assignedTo: ['user-1', 'user-4'],
      dueDate: '2025-01-29',
      labels: [
        { id: 'review', name: 'Review', color: '#8b5cf6' },
        { id: 'frontend', name: 'Frontend', color: '#06b6d4' }
      ],
      checklist: [
        { id: 'check-8', text: 'Check code quality', completed: true },
        { id: 'check-9', text: 'Test responsiveness', completed: false },
        { id: 'check-10', text: 'Verify accessibility', completed: false }
      ],
      comments: [],
      attachments: [],
      createdAt: '2025-01-23T15:30:00Z',
      updatedAt: '2025-01-27T09:20:00Z'
    },
    {
      id: 'card-5',
      columnId: 'col-4',
      title: 'Update project documentation',
      description: 'Refresh README and API documentation with latest changes',
      priority: 'low',
      assignedTo: ['user-3'],
      dueDate: null,
      labels: [
        { id: 'documentation', name: 'Documentation', color: '#f59e0b' }
      ],
      checklist: [
        { id: 'check-11', text: 'Update README', completed: true },
        { id: 'check-12', text: 'Update API docs', completed: true },
        { id: 'check-13', text: 'Add deployment guide', completed: true }
      ],
      comments: [
        {
          id: 'comment-4',
          author: 'user-1',
          content: 'Great work on the documentation!',
          createdAt: '2025-01-25T12:00:00Z'
        }
      ],
      attachments: [],
      createdAt: '2025-01-20T10:00:00Z',
      updatedAt: '2025-01-25T12:00:00Z'
    }
  ]);

  // Modal states
  const [showAddCardModal, setShowAddCardModal] = useState(false);
  const [showAddColumnModal, setShowAddColumnModal] = useState(false);
  const [showInviteMemberModal, setShowInviteMemberModal] = useState(false);
  const [selectedColumnId, setSelectedColumnId] = useState(null);

  // Filter and search states
  const [searchQuery, setSearchQuery] = useState('');
  const [activeFilters, setActiveFilters] = useState({});

  // Filter cards based on search and filters
  const filteredCards = cards.filter(card => {
    // Search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch = 
        card.title.toLowerCase().includes(query) ||
        card.description.toLowerCase().includes(query) ||
        card.labels?.some(label => label.name.toLowerCase().includes(query));
      
      if (!matchesSearch) return false;
    }

    // Priority filter
    if (activeFilters.priority?.length > 0) {
      if (!activeFilters.priority.includes(card.priority)) return false;
    }

    // Assignee filter
    if (activeFilters.assignee?.length > 0) {
      const hasAssignee = card.assignedTo?.some(assigneeId => 
        activeFilters.assignee.includes(assigneeId)
      );
      if (!hasAssignee) return false;
    }

    // Due date filter
    if (activeFilters.dueDate?.length > 0) {
      const today = new Date();
      const cardDueDate = card.dueDate ? new Date(card.dueDate) : null;
      
      const matchesDueDate = activeFilters.dueDate.some(filter => {
        if (filter === 'overdue') {
          return cardDueDate && cardDueDate < today;
        }
        if (filter === 'today') {
          return cardDueDate && cardDueDate.toDateString() === today.toDateString();
        }
        if (filter === 'this-week') {
          const weekFromNow = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
          return cardDueDate && cardDueDate >= today && cardDueDate <= weekFromNow;
        }
        if (filter === 'this-month') {
          const monthFromNow = new Date(today.getFullYear(), today.getMonth() + 1, today.getDate());
          return cardDueDate && cardDueDate >= today && cardDueDate <= monthFromNow;
        }
        return false;
      });
      
      if (!matchesDueDate) return false;
    }

    return true;
  });

  // Handle card movement between columns
  const handleCardMove = (cardId, sourceColumnId, targetColumnId) => {
    setCards(prevCards => 
      prevCards.map(card => 
        card.id === cardId 
          ? { ...card, columnId: targetColumnId, updatedAt: new Date().toISOString() }
          : card
      )
    );
  };

  // Handle card click - navigate to card details
  const handleCardClick = (card) => {
    navigate('/card-details', { state: { card, members } });
  };

  // Handle adding new card
  const handleAddCard = (columnId) => {
    setSelectedColumnId(columnId);
    setShowAddCardModal(true);
  };

  const handleSaveCard = (newCard) => {
    setCards(prevCards => [...prevCards, newCard]);
  };

  // Handle adding new column
  const handleSaveColumn = (newColumn) => {
    setColumns(prevColumns => [...prevColumns, newColumn]);
  };

  // Handle column operations
  const handleEditColumn = (columnId, updates) => {
    setColumns(prevColumns =>
      prevColumns.map(col =>
        col.id === columnId
          ? { ...col, ...updates, updatedAt: new Date().toISOString() }
          : col
      )
    );
  };

  const handleDeleteColumn = (columnId) => {
    // Move cards from deleted column to first column
    const firstColumnId = columns[0]?.id;
    if (firstColumnId && firstColumnId !== columnId) {
      setCards(prevCards =>
        prevCards.map(card =>
          card.columnId === columnId
            ? { ...card, columnId: firstColumnId }
            : card
        )
      );
    }
    
    setColumns(prevColumns => prevColumns.filter(col => col.id !== columnId));
  };

  // Handle member invitation
  const handleMemberInvite = () => {
    setShowInviteMemberModal(true);
  };

  const handleSendInvitation = (invitation) => {
    console.log('Invitation sent:', invitation);
    // In real app, this would send the invitation via API
  };

  // Handle board updates
  const handleBoardUpdate = (updates) => {
    console.log('Board updated:', updates);
    // In real app, this would update the board via API
  };

  // Get cards for a specific column
  const getCardsForColumn = (columnId) => {
    return filteredCards.filter(card => card.columnId === columnId);
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="min-h-screen bg-background">
        <Header />

        <main className="pt-16">
          <div className="max-w-full px-4 sm:px-6 lg:px-8 py-8">
            <Breadcrumb />

            {/* Page Header */}
            <div className="mb-6">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-primary rounded-lg flex items-center justify-center">
                  <Icon name="Kanban" size={20} className="text-primary-foreground" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-foreground">Kanban Board</h1>
                  <p className="text-muted-foreground">
                    Manage tasks and track project progress
                  </p>
                </div>
              </div>
            </div>

            {/* Board Header */}
            <BoardHeader
              board={board}
          members={members}
          onBoardUpdate={handleBoardUpdate}
          onMemberInvite={handleMemberInvite}
          onFilterChange={setActiveFilters}
          onSearchChange={setSearchQuery}
          searchQuery={searchQuery}
          activeFilters={activeFilters}
        />

        {/* Board Content */}
        <div className="flex-1 p-6">
          <div className="flex space-x-6 overflow-x-auto pb-6">
            {/* Columns */}
            {columns
              .sort((a, b) => a.order - b.order)
              .map(column => (
                <BoardColumn
                  key={column.id}
                  column={column}
                  cards={getCardsForColumn(column.id)}
                  onCardMove={handleCardMove}
                  onCardClick={handleCardClick}
                  onAddCard={handleAddCard}
                  onEditColumn={handleEditColumn}
                  onDeleteColumn={handleDeleteColumn}
                  members={members}
                />
              ))}

            {/* Add Column Button */}
            <div className="flex-shrink-0">
              <Button
                variant="outline"
                onClick={() => setShowAddColumnModal(true)}
                className="w-80 h-32 border-2 border-dashed border-border hover:border-primary hover:bg-primary/5 transition-colors"
                iconName="Plus"
                iconPosition="left"
              >
                Add Column
              </Button>
            </div>
          </div>

          {/* Empty State */}
          {filteredCards.length === 0 && searchQuery && (
            <div className="flex flex-col items-center justify-center py-12">
              <Icon name="Search" size={48} className="text-text-secondary mb-4" />
              <h3 className="text-lg font-medium text-text-primary mb-2">No cards found</h3>
              <p className="text-text-secondary text-center max-w-md">
                No cards match your search criteria. Try adjusting your search terms or filters.
              </p>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery('');
                  setActiveFilters({});
                }}
                className="mt-4"
              >
                Clear Search & Filters
              </Button>
            </div>
          )}
        </div>

        {/* Modals */}
        <AddCardModal
          isOpen={showAddCardModal}
          onClose={() => {
            setShowAddCardModal(false);
            setSelectedColumnId(null);
          }}
          onSave={handleSaveCard}
          columnId={selectedColumnId}
          members={members}
        />

        <AddColumnModal
          isOpen={showAddColumnModal}
          onClose={() => setShowAddColumnModal(false)}
          onSave={handleSaveColumn}
        />

        <InviteMemberModal
          isOpen={showInviteMemberModal}
          onClose={() => setShowInviteMemberModal(false)}
          onInvite={handleSendInvitation}
        />
          </div>
        </main>
      </div>
    </DndProvider>
  );
};

export default KanbanBoard;