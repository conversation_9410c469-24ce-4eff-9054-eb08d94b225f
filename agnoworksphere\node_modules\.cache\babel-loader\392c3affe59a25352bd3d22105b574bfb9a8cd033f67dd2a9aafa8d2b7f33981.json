{"ast": null, "code": "// src/utils/authService.js\n\n// Enhanced authentication service connecting to backend API\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// Helper function to handle API responses\nconst handleResponse = async response => {\n  const result = await response.json();\n  if (!response.ok) {\n    var _result$error;\n    throw new Error(((_result$error = result.error) === null || _result$error === void 0 ? void 0 : _result$error.message) || result.message || 'API request failed');\n  }\n  return result;\n};\nconst authService = {\n  // Sign up a new user with enhanced registration\n  signUp: async (email, password, userData = {}) => {\n    try {\n      const registrationData = {\n        email: email,\n        password: password,\n        first_name: userData.firstName || userData.first_name || '',\n        last_name: userData.lastName || userData.last_name || '',\n        organization_name: userData.organizationName || userData.organization_name || null\n      };\n      const response = await fetch(`${API_BASE_URL}/v1/auth/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(registrationData)\n      });\n      const result = await handleResponse(response);\n\n      // Store tokens\n      if (result.data.tokens) {\n        var _result$data$organiza;\n        localStorage.setItem('accessToken', result.data.tokens.access_token);\n        localStorage.setItem('userRole', result.data.role || 'member');\n        localStorage.setItem('organizationId', ((_result$data$organiza = result.data.organization) === null || _result$data$organiza === void 0 ? void 0 : _result$data$organiza.id) || '');\n      }\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified,\n            role: result.data.role\n          },\n          organization: result.data.organization,\n          tokens: result.data.tokens\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Sign up failed'\n      };\n    }\n  },\n  // Sign in with email and password\n  signIn: async (email, password) => {\n    try {\n      const loginData = {\n        email: email,\n        password: password\n      };\n      const response = await fetch(`${API_BASE_URL}/v1/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify(loginData)\n      });\n      const result = await handleResponse(response);\n\n      // Store tokens and user info\n      if (result.data.tokens) {\n        var _result$data$organiza2;\n        localStorage.setItem('accessToken', result.data.tokens.access_token);\n        localStorage.setItem('userRole', result.data.role || 'member');\n        localStorage.setItem('organizationId', ((_result$data$organiza2 = result.data.organization) === null || _result$data$organiza2 === void 0 ? void 0 : _result$data$organiza2.id) || '');\n      }\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified,\n            role: result.data.role\n          },\n          organization: result.data.organization,\n          tokens: result.data.tokens\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Sign in failed'\n      };\n    }\n  },\n  // Sign out\n  signOut: async () => {\n    try {\n      // Clear stored tokens and user data\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('userRole');\n      localStorage.removeItem('organizationId');\n      return {\n        error: null\n      };\n    } catch (error) {\n      return {\n        error: error.message || 'Sign out failed'\n      };\n    }\n  },\n  // Get current user profile\n  getCurrentUser: async () => {\n    try {\n      const token = localStorage.getItem('accessToken');\n      if (!token) {\n        return {\n          data: {\n            user: null\n          },\n          error: null\n        };\n      }\n      const response = await fetch(`${API_BASE_URL}/v1/users/profile`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      if (!response.ok) {\n        // Token might be expired\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        return {\n          data: {\n            user: null\n          },\n          error: null\n        };\n      }\n      const result = await response.json();\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified,\n            role: localStorage.getItem('userRole') || 'member'\n          },\n          organizations: result.data.organizations\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: {\n          user: null\n        },\n        error: null // Don't expose errors for getCurrentUser\n      };\n    }\n  },\n  // Refresh access token (authentication removed - mock implementation)\n  refreshToken: async () => {\n    try {\n      // Mock successful token refresh\n      await new Promise(resolve => setTimeout(resolve, 200)); // Simulate API delay\n\n      return {\n        data: {\n          accessToken: 'mock-access-token',\n          refreshToken: 'mock-refresh-token'\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Token refresh failed'\n      };\n    }\n  },\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    const token = localStorage.getItem('accessToken');\n    return !!token;\n  },\n  // Get stored access token\n  getAccessToken: () => {\n    return localStorage.getItem('accessToken');\n  },\n  // Get user role\n  getUserRole: () => {\n    return localStorage.getItem('userRole') || 'member';\n  },\n  // Get organization ID\n  getOrganizationId: () => {\n    return localStorage.getItem('organizationId');\n  },\n  // Get dashboard stats\n  getDashboardStats: async () => {\n    try {\n      const token = localStorage.getItem('accessToken');\n      if (!token) {\n        throw new Error('No access token');\n      }\n      const response = await fetch(`${API_BASE_URL}/v1/dashboard/stats`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        }\n      });\n      const result = await handleResponse(response);\n      return {\n        data: result.data,\n        userRole: result.user_role,\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Failed to get dashboard stats'\n      };\n    }\n  },\n  // Listen to auth state changes\n  onAuthStateChange: callback => {\n    // Simple implementation - check token periodically\n    const checkAuth = async () => {\n      const result = await authService.getCurrentUser();\n      callback(result.data.user, result.error);\n    };\n\n    // Check immediately\n    checkAuth();\n\n    // Set up periodic check (every 5 minutes)\n    const interval = setInterval(checkAuth, 5 * 60 * 1000);\n    return {\n      data: {\n        subscription: {\n          unsubscribe: () => clearInterval(interval)\n        }\n      }\n    };\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_URL", "handleResponse", "response", "result", "json", "ok", "_result$error", "Error", "error", "message", "authService", "signUp", "email", "password", "userData", "registrationData", "first_name", "firstName", "last_name", "lastName", "organization_name", "organizationName", "fetch", "method", "headers", "body", "JSON", "stringify", "data", "tokens", "_result$data$organiza", "localStorage", "setItem", "access_token", "role", "organization", "id", "user", "emailVerified", "email_verified", "signIn", "loginData", "_result$data$organiza2", "signOut", "removeItem", "getCurrentUser", "token", "getItem", "organizations", "refreshToken", "Promise", "resolve", "setTimeout", "accessToken", "isAuthenticated", "getAccessToken", "getUserRole", "getOrganizationId", "getDashboardStats", "userRole", "user_role", "onAuthStateChange", "callback", "checkAuth", "interval", "setInterval", "subscription", "unsubscribe", "clearInterval"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/utils/authService.js"], "sourcesContent": ["// src/utils/authService.js\n\n// Enhanced authentication service connecting to backend API\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// Helper function to handle API responses\nconst handleResponse = async (response) => {\n  const result = await response.json();\n\n  if (!response.ok) {\n    throw new Error(result.error?.message || result.message || 'API request failed');\n  }\n\n  return result;\n};\n\nconst authService = {\n  // Sign up a new user with enhanced registration\n  signUp: async (email, password, userData = {}) => {\n    try {\n      const registrationData = {\n        email: email,\n        password: password,\n        first_name: userData.firstName || userData.first_name || '',\n        last_name: userData.lastName || userData.last_name || '',\n        organization_name: userData.organizationName || userData.organization_name || null\n      };\n\n      const response = await fetch(`${API_BASE_URL}/v1/auth/register`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(registrationData),\n      });\n\n      const result = await handleResponse(response);\n\n      // Store tokens\n      if (result.data.tokens) {\n        localStorage.setItem('accessToken', result.data.tokens.access_token);\n        localStorage.setItem('userRole', result.data.role || 'member');\n        localStorage.setItem('organizationId', result.data.organization?.id || '');\n      }\n\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified,\n            role: result.data.role\n          },\n          organization: result.data.organization,\n          tokens: result.data.tokens\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Sign up failed'\n      };\n    }\n  },\n\n  // Sign in with email and password\n  signIn: async (email, password) => {\n    try {\n      const loginData = {\n        email: email,\n        password: password\n      };\n\n      const response = await fetch(`${API_BASE_URL}/v1/auth/login`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(loginData),\n      });\n\n      const result = await handleResponse(response);\n\n      // Store tokens and user info\n      if (result.data.tokens) {\n        localStorage.setItem('accessToken', result.data.tokens.access_token);\n        localStorage.setItem('userRole', result.data.role || 'member');\n        localStorage.setItem('organizationId', result.data.organization?.id || '');\n      }\n\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified,\n            role: result.data.role\n          },\n          organization: result.data.organization,\n          tokens: result.data.tokens\n        },\n        error: null\n      };\n\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Sign in failed'\n      };\n    }\n  },\n\n  // Sign out\n  signOut: async () => {\n    try {\n      // Clear stored tokens and user data\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      localStorage.removeItem('userRole');\n      localStorage.removeItem('organizationId');\n\n      return {\n        error: null\n      };\n    } catch (error) {\n      return {\n        error: error.message || 'Sign out failed'\n      };\n    }\n  },\n\n  // Get current user profile\n  getCurrentUser: async () => {\n    try {\n      const token = localStorage.getItem('accessToken');\n      if (!token) {\n        return {\n          data: { user: null },\n          error: null\n        };\n      }\n\n      const response = await fetch(`${API_BASE_URL}/v1/users/profile`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n      });\n\n      if (!response.ok) {\n        // Token might be expired\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('userRole');\n        localStorage.removeItem('organizationId');\n        return {\n          data: { user: null },\n          error: null\n        };\n      }\n\n      const result = await response.json();\n\n      return {\n        data: {\n          user: {\n            id: result.data.user.id,\n            email: result.data.user.email,\n            firstName: result.data.user.first_name,\n            lastName: result.data.user.last_name,\n            emailVerified: result.data.user.email_verified,\n            role: localStorage.getItem('userRole') || 'member'\n          },\n          organizations: result.data.organizations\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: { user: null },\n        error: null // Don't expose errors for getCurrentUser\n      };\n    }\n  },\n\n  // Refresh access token (authentication removed - mock implementation)\n  refreshToken: async () => {\n    try {\n      // Mock successful token refresh\n      await new Promise(resolve => setTimeout(resolve, 200)); // Simulate API delay\n\n      return {\n        data: {\n          accessToken: 'mock-access-token',\n          refreshToken: 'mock-refresh-token'\n        },\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Token refresh failed'\n      };\n    }\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    const token = localStorage.getItem('accessToken');\n    return !!token;\n  },\n\n  // Get stored access token\n  getAccessToken: () => {\n    return localStorage.getItem('accessToken');\n  },\n\n  // Get user role\n  getUserRole: () => {\n    return localStorage.getItem('userRole') || 'member';\n  },\n\n  // Get organization ID\n  getOrganizationId: () => {\n    return localStorage.getItem('organizationId');\n  },\n\n  // Get dashboard stats\n  getDashboardStats: async () => {\n    try {\n      const token = localStorage.getItem('accessToken');\n      if (!token) {\n        throw new Error('No access token');\n      }\n\n      const response = await fetch(`${API_BASE_URL}/v1/dashboard/stats`, {\n        method: 'GET',\n        headers: {\n          'Content-Type': 'application/json',\n          'Authorization': `Bearer ${token}`\n        },\n      });\n\n      const result = await handleResponse(response);\n\n      return {\n        data: result.data,\n        userRole: result.user_role,\n        error: null\n      };\n    } catch (error) {\n      return {\n        data: null,\n        error: error.message || 'Failed to get dashboard stats'\n      };\n    }\n  },\n\n  // Listen to auth state changes\n  onAuthStateChange: (callback) => {\n    // Simple implementation - check token periodically\n    const checkAuth = async () => {\n      const result = await authService.getCurrentUser();\n      callback(result.data.user, result.error);\n    };\n\n    // Check immediately\n    checkAuth();\n\n    // Set up periodic check (every 5 minutes)\n    const interval = setInterval(checkAuth, 5 * 60 * 1000);\n\n    return {\n      data: {\n        subscription: {\n          unsubscribe: () => clearInterval(interval)\n        }\n      }\n    };\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA;;AAEA;AACA,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,cAAc,GAAG,MAAOC,QAAQ,IAAK;EACzC,MAAMC,MAAM,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;EAEpC,IAAI,CAACF,QAAQ,CAACG,EAAE,EAAE;IAAA,IAAAC,aAAA;IAChB,MAAM,IAAIC,KAAK,CAAC,EAAAD,aAAA,GAAAH,MAAM,CAACK,KAAK,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,OAAO,KAAIN,MAAM,CAACM,OAAO,IAAI,oBAAoB,CAAC;EAClF;EAEA,OAAON,MAAM;AACf,CAAC;AAED,MAAMO,WAAW,GAAG;EAClB;EACAC,MAAM,EAAE,MAAAA,CAAOC,KAAK,EAAEC,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC,KAAK;IAChD,IAAI;MACF,MAAMC,gBAAgB,GAAG;QACvBH,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA,QAAQ;QAClBG,UAAU,EAAEF,QAAQ,CAACG,SAAS,IAAIH,QAAQ,CAACE,UAAU,IAAI,EAAE;QAC3DE,SAAS,EAAEJ,QAAQ,CAACK,QAAQ,IAAIL,QAAQ,CAACI,SAAS,IAAI,EAAE;QACxDE,iBAAiB,EAAEN,QAAQ,CAACO,gBAAgB,IAAIP,QAAQ,CAACM,iBAAiB,IAAI;MAChF,CAAC;MAED,MAAMlB,QAAQ,GAAG,MAAMoB,KAAK,CAAC,GAAGzB,YAAY,mBAAmB,EAAE;QAC/D0B,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACZ,gBAAgB;MACvC,CAAC,CAAC;MAEF,MAAMZ,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;MAE7C;MACA,IAAIC,MAAM,CAACyB,IAAI,CAACC,MAAM,EAAE;QAAA,IAAAC,qBAAA;QACtBC,YAAY,CAACC,OAAO,CAAC,aAAa,EAAE7B,MAAM,CAACyB,IAAI,CAACC,MAAM,CAACI,YAAY,CAAC;QACpEF,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE7B,MAAM,CAACyB,IAAI,CAACM,IAAI,IAAI,QAAQ,CAAC;QAC9DH,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,EAAAF,qBAAA,GAAA3B,MAAM,CAACyB,IAAI,CAACO,YAAY,cAAAL,qBAAA,uBAAxBA,qBAAA,CAA0BM,EAAE,KAAI,EAAE,CAAC;MAC5E;MAEA,OAAO;QACLR,IAAI,EAAE;UACJS,IAAI,EAAE;YACJD,EAAE,EAAEjC,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACD,EAAE;YACvBxB,KAAK,EAAET,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACzB,KAAK;YAC7BK,SAAS,EAAEd,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACrB,UAAU;YACtCG,QAAQ,EAAEhB,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACnB,SAAS;YACpCoB,aAAa,EAAEnC,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACE,cAAc;YAC9CL,IAAI,EAAE/B,MAAM,CAACyB,IAAI,CAACM;UACpB,CAAC;UACDC,YAAY,EAAEhC,MAAM,CAACyB,IAAI,CAACO,YAAY;UACtCN,MAAM,EAAE1B,MAAM,CAACyB,IAAI,CAACC;QACtB,CAAC;QACDrB,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLoB,IAAI,EAAE,IAAI;QACVpB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACA+B,MAAM,EAAE,MAAAA,CAAO5B,KAAK,EAAEC,QAAQ,KAAK;IACjC,IAAI;MACF,MAAM4B,SAAS,GAAG;QAChB7B,KAAK,EAAEA,KAAK;QACZC,QAAQ,EAAEA;MACZ,CAAC;MAED,MAAMX,QAAQ,GAAG,MAAMoB,KAAK,CAAC,GAAGzB,YAAY,gBAAgB,EAAE;QAC5D0B,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACc,SAAS;MAChC,CAAC,CAAC;MAEF,MAAMtC,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;;MAE7C;MACA,IAAIC,MAAM,CAACyB,IAAI,CAACC,MAAM,EAAE;QAAA,IAAAa,sBAAA;QACtBX,YAAY,CAACC,OAAO,CAAC,aAAa,EAAE7B,MAAM,CAACyB,IAAI,CAACC,MAAM,CAACI,YAAY,CAAC;QACpEF,YAAY,CAACC,OAAO,CAAC,UAAU,EAAE7B,MAAM,CAACyB,IAAI,CAACM,IAAI,IAAI,QAAQ,CAAC;QAC9DH,YAAY,CAACC,OAAO,CAAC,gBAAgB,EAAE,EAAAU,sBAAA,GAAAvC,MAAM,CAACyB,IAAI,CAACO,YAAY,cAAAO,sBAAA,uBAAxBA,sBAAA,CAA0BN,EAAE,KAAI,EAAE,CAAC;MAC5E;MAEA,OAAO;QACLR,IAAI,EAAE;UACJS,IAAI,EAAE;YACJD,EAAE,EAAEjC,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACD,EAAE;YACvBxB,KAAK,EAAET,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACzB,KAAK;YAC7BK,SAAS,EAAEd,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACrB,UAAU;YACtCG,QAAQ,EAAEhB,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACnB,SAAS;YACpCoB,aAAa,EAAEnC,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACE,cAAc;YAC9CL,IAAI,EAAE/B,MAAM,CAACyB,IAAI,CAACM;UACpB,CAAC;UACDC,YAAY,EAAEhC,MAAM,CAACyB,IAAI,CAACO,YAAY;UACtCN,MAAM,EAAE1B,MAAM,CAACyB,IAAI,CAACC;QACtB,CAAC;QACDrB,KAAK,EAAE;MACT,CAAC;IAEH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLoB,IAAI,EAAE,IAAI;QACVpB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACAkC,OAAO,EAAE,MAAAA,CAAA,KAAY;IACnB,IAAI;MACF;MACAZ,YAAY,CAACa,UAAU,CAAC,aAAa,CAAC;MACtCb,YAAY,CAACa,UAAU,CAAC,cAAc,CAAC;MACvCb,YAAY,CAACa,UAAU,CAAC,UAAU,CAAC;MACnCb,YAAY,CAACa,UAAU,CAAC,gBAAgB,CAAC;MAEzC,OAAO;QACLpC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLA,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACAoC,cAAc,EAAE,MAAAA,CAAA,KAAY;IAC1B,IAAI;MACF,MAAMC,KAAK,GAAGf,YAAY,CAACgB,OAAO,CAAC,aAAa,CAAC;MACjD,IAAI,CAACD,KAAK,EAAE;QACV,OAAO;UACLlB,IAAI,EAAE;YAAES,IAAI,EAAE;UAAK,CAAC;UACpB7B,KAAK,EAAE;QACT,CAAC;MACH;MAEA,MAAMN,QAAQ,GAAG,MAAMoB,KAAK,CAAC,GAAGzB,YAAY,mBAAmB,EAAE;QAC/D0B,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUsB,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,IAAI,CAAC5C,QAAQ,CAACG,EAAE,EAAE;QAChB;QACA0B,YAAY,CAACa,UAAU,CAAC,aAAa,CAAC;QACtCb,YAAY,CAACa,UAAU,CAAC,UAAU,CAAC;QACnCb,YAAY,CAACa,UAAU,CAAC,gBAAgB,CAAC;QACzC,OAAO;UACLhB,IAAI,EAAE;YAAES,IAAI,EAAE;UAAK,CAAC;UACpB7B,KAAK,EAAE;QACT,CAAC;MACH;MAEA,MAAML,MAAM,GAAG,MAAMD,QAAQ,CAACE,IAAI,CAAC,CAAC;MAEpC,OAAO;QACLwB,IAAI,EAAE;UACJS,IAAI,EAAE;YACJD,EAAE,EAAEjC,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACD,EAAE;YACvBxB,KAAK,EAAET,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACzB,KAAK;YAC7BK,SAAS,EAAEd,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACrB,UAAU;YACtCG,QAAQ,EAAEhB,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACnB,SAAS;YACpCoB,aAAa,EAAEnC,MAAM,CAACyB,IAAI,CAACS,IAAI,CAACE,cAAc;YAC9CL,IAAI,EAAEH,YAAY,CAACgB,OAAO,CAAC,UAAU,CAAC,IAAI;UAC5C,CAAC;UACDC,aAAa,EAAE7C,MAAM,CAACyB,IAAI,CAACoB;QAC7B,CAAC;QACDxC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLoB,IAAI,EAAE;UAAES,IAAI,EAAE;QAAK,CAAC;QACpB7B,KAAK,EAAE,IAAI,CAAC;MACd,CAAC;IACH;EACF,CAAC;EAED;EACAyC,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,IAAI;MACF;MACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;;MAExD,OAAO;QACLvB,IAAI,EAAE;UACJyB,WAAW,EAAE,mBAAmB;UAChCJ,YAAY,EAAE;QAChB,CAAC;QACDzC,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLoB,IAAI,EAAE,IAAI;QACVpB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACA6C,eAAe,EAAEA,CAAA,KAAM;IACrB,MAAMR,KAAK,GAAGf,YAAY,CAACgB,OAAO,CAAC,aAAa,CAAC;IACjD,OAAO,CAAC,CAACD,KAAK;EAChB,CAAC;EAED;EACAS,cAAc,EAAEA,CAAA,KAAM;IACpB,OAAOxB,YAAY,CAACgB,OAAO,CAAC,aAAa,CAAC;EAC5C,CAAC;EAED;EACAS,WAAW,EAAEA,CAAA,KAAM;IACjB,OAAOzB,YAAY,CAACgB,OAAO,CAAC,UAAU,CAAC,IAAI,QAAQ;EACrD,CAAC;EAED;EACAU,iBAAiB,EAAEA,CAAA,KAAM;IACvB,OAAO1B,YAAY,CAACgB,OAAO,CAAC,gBAAgB,CAAC;EAC/C,CAAC;EAED;EACAW,iBAAiB,EAAE,MAAAA,CAAA,KAAY;IAC7B,IAAI;MACF,MAAMZ,KAAK,GAAGf,YAAY,CAACgB,OAAO,CAAC,aAAa,CAAC;MACjD,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIvC,KAAK,CAAC,iBAAiB,CAAC;MACpC;MAEA,MAAML,QAAQ,GAAG,MAAMoB,KAAK,CAAC,GAAGzB,YAAY,qBAAqB,EAAE;QACjE0B,MAAM,EAAE,KAAK;QACbC,OAAO,EAAE;UACP,cAAc,EAAE,kBAAkB;UAClC,eAAe,EAAE,UAAUsB,KAAK;QAClC;MACF,CAAC,CAAC;MAEF,MAAM3C,MAAM,GAAG,MAAMF,cAAc,CAACC,QAAQ,CAAC;MAE7C,OAAO;QACL0B,IAAI,EAAEzB,MAAM,CAACyB,IAAI;QACjB+B,QAAQ,EAAExD,MAAM,CAACyD,SAAS;QAC1BpD,KAAK,EAAE;MACT,CAAC;IACH,CAAC,CAAC,OAAOA,KAAK,EAAE;MACd,OAAO;QACLoB,IAAI,EAAE,IAAI;QACVpB,KAAK,EAAEA,KAAK,CAACC,OAAO,IAAI;MAC1B,CAAC;IACH;EACF,CAAC;EAED;EACAoD,iBAAiB,EAAGC,QAAQ,IAAK;IAC/B;IACA,MAAMC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,MAAM5D,MAAM,GAAG,MAAMO,WAAW,CAACmC,cAAc,CAAC,CAAC;MACjDiB,QAAQ,CAAC3D,MAAM,CAACyB,IAAI,CAACS,IAAI,EAAElC,MAAM,CAACK,KAAK,CAAC;IAC1C,CAAC;;IAED;IACAuD,SAAS,CAAC,CAAC;;IAEX;IACA,MAAMC,QAAQ,GAAGC,WAAW,CAACF,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAEtD,OAAO;MACLnC,IAAI,EAAE;QACJsC,YAAY,EAAE;UACZC,WAAW,EAAEA,CAAA,KAAMC,aAAa,CAACJ,QAAQ;QAC3C;MACF;IACF,CAAC;EACH;AACF,CAAC;AAED,eAAetD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}