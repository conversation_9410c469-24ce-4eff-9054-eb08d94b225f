{"ast": null, "code": "'use strict';\n\n// do not edit .js files directly - edit src/index.jst\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;) if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n    for (i = length; i-- !== 0;) if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n      if (!equal(a[key], b[key])) return false;\n    }\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a !== a && b !== b;\n};", "map": {"version": 3, "names": ["module", "exports", "equal", "a", "b", "constructor", "length", "i", "keys", "Array", "isArray", "RegExp", "source", "flags", "valueOf", "Object", "prototype", "toString", "hasOwnProperty", "call", "key"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/node_modules/fast-deep-equal/index.js"], "sourcesContent": ["'use strict';\n\n// do not edit .js files directly - edit src/index.jst\n\n\n\nmodule.exports = function equal(a, b) {\n  if (a === b) return true;\n\n  if (a && b && typeof a == 'object' && typeof b == 'object') {\n    if (a.constructor !== b.constructor) return false;\n\n    var length, i, keys;\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length != b.length) return false;\n      for (i = length; i-- !== 0;)\n        if (!equal(a[i], b[i])) return false;\n      return true;\n    }\n\n\n\n    if (a.constructor === RegExp) return a.source === b.source && a.flags === b.flags;\n    if (a.valueOf !== Object.prototype.valueOf) return a.valueOf() === b.valueOf();\n    if (a.toString !== Object.prototype.toString) return a.toString() === b.toString();\n\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) return false;\n\n    for (i = length; i-- !== 0;)\n      if (!Object.prototype.hasOwnProperty.call(b, keys[i])) return false;\n\n    for (i = length; i-- !== 0;) {\n      var key = keys[i];\n\n      if (!equal(a[key], b[key])) return false;\n    }\n\n    return true;\n  }\n\n  // true if both NaN, false otherwise\n  return a!==a && b!==b;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AAIAA,MAAM,CAACC,OAAO,GAAG,SAASC,KAAKA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACpC,IAAID,CAAC,KAAKC,CAAC,EAAE,OAAO,IAAI;EAExB,IAAID,CAAC,IAAIC,CAAC,IAAI,OAAOD,CAAC,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,QAAQ,EAAE;IAC1D,IAAID,CAAC,CAACE,WAAW,KAAKD,CAAC,CAACC,WAAW,EAAE,OAAO,KAAK;IAEjD,IAAIC,MAAM,EAAEC,CAAC,EAAEC,IAAI;IACnB,IAAIC,KAAK,CAACC,OAAO,CAACP,CAAC,CAAC,EAAE;MACpBG,MAAM,GAAGH,CAAC,CAACG,MAAM;MACjB,IAAIA,MAAM,IAAIF,CAAC,CAACE,MAAM,EAAE,OAAO,KAAK;MACpC,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GACxB,IAAI,CAACL,KAAK,CAACC,CAAC,CAACI,CAAC,CAAC,EAAEH,CAAC,CAACG,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;MACtC,OAAO,IAAI;IACb;IAIA,IAAIJ,CAAC,CAACE,WAAW,KAAKM,MAAM,EAAE,OAAOR,CAAC,CAACS,MAAM,KAAKR,CAAC,CAACQ,MAAM,IAAIT,CAAC,CAACU,KAAK,KAAKT,CAAC,CAACS,KAAK;IACjF,IAAIV,CAAC,CAACW,OAAO,KAAKC,MAAM,CAACC,SAAS,CAACF,OAAO,EAAE,OAAOX,CAAC,CAACW,OAAO,CAAC,CAAC,KAAKV,CAAC,CAACU,OAAO,CAAC,CAAC;IAC9E,IAAIX,CAAC,CAACc,QAAQ,KAAKF,MAAM,CAACC,SAAS,CAACC,QAAQ,EAAE,OAAOd,CAAC,CAACc,QAAQ,CAAC,CAAC,KAAKb,CAAC,CAACa,QAAQ,CAAC,CAAC;IAElFT,IAAI,GAAGO,MAAM,CAACP,IAAI,CAACL,CAAC,CAAC;IACrBG,MAAM,GAAGE,IAAI,CAACF,MAAM;IACpB,IAAIA,MAAM,KAAKS,MAAM,CAACP,IAAI,CAACJ,CAAC,CAAC,CAACE,MAAM,EAAE,OAAO,KAAK;IAElD,KAAKC,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GACxB,IAAI,CAACQ,MAAM,CAACC,SAAS,CAACE,cAAc,CAACC,IAAI,CAACf,CAAC,EAAEI,IAAI,CAACD,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;IAErE,KAAKA,CAAC,GAAGD,MAAM,EAAEC,CAAC,EAAE,KAAK,CAAC,GAAG;MAC3B,IAAIa,GAAG,GAAGZ,IAAI,CAACD,CAAC,CAAC;MAEjB,IAAI,CAACL,KAAK,CAACC,CAAC,CAACiB,GAAG,CAAC,EAAEhB,CAAC,CAACgB,GAAG,CAAC,CAAC,EAAE,OAAO,KAAK;IAC1C;IAEA,OAAO,IAAI;EACb;;EAEA;EACA,OAAOjB,CAAC,KAAGA,CAAC,IAAIC,CAAC,KAAGA,CAAC;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}