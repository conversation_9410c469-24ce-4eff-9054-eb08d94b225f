{"ast": null, "code": "export class NativeDragSource {\n  initializeExposedProperties() {\n    Object.keys(this.config.exposeProperties).forEach(property => {\n      Object.defineProperty(this.item, property, {\n        configurable: true,\n        enumerable: true,\n        get() {\n          // eslint-disable-next-line no-console\n          console.warn(`<PERSON><PERSON><PERSON> doesn't allow reading \"${property}\" until the drop event.`);\n          return null;\n        }\n      });\n    });\n  }\n  loadDataTransfer(dataTransfer) {\n    if (dataTransfer) {\n      const newProperties = {};\n      Object.keys(this.config.exposeProperties).forEach(property => {\n        const propertyFn = this.config.exposeProperties[property];\n        if (propertyFn != null) {\n          newProperties[property] = {\n            value: propertyFn(dataTransfer, this.config.matchesTypes),\n            configurable: true,\n            enumerable: true\n          };\n        }\n      });\n      Object.defineProperties(this.item, newProperties);\n    }\n  }\n  canDrag() {\n    return true;\n  }\n  beginDrag() {\n    return this.item;\n  }\n  isDragging(monitor, handle) {\n    return handle === monitor.getSourceId();\n  }\n  endDrag() {\n    // empty\n  }\n  constructor(config) {\n    this.config = config;\n    this.item = {};\n    this.initializeExposedProperties();\n  }\n}", "map": {"version": 3, "names": ["NativeDragSource", "initializeExposedProperties", "Object", "keys", "config", "exposeProperties", "for<PERSON>ach", "property", "defineProperty", "item", "configurable", "enumerable", "get", "console", "warn", "loadDataTransfer", "dataTransfer", "newProperties", "propertyFn", "value", "matchesTypes", "defineProperties", "canDrag", "beginDrag", "isDragging", "monitor", "handle", "getSourceId", "endDrag", "constructor"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd-html5-backend\\src\\NativeDragSources\\NativeDragSource.ts"], "sourcesContent": ["import type { DragDropMonitor } from 'dnd-core'\n\nimport type { NativeItemConfig } from './nativeTypesConfig.js'\n\nexport class NativeDragSource {\n\tpublic item: any\n\tprivate config: NativeItemConfig\n\n\tpublic constructor(config: NativeItemConfig) {\n\t\tthis.config = config\n\t\tthis.item = {}\n\t\tthis.initializeExposedProperties()\n\t}\n\n\tprivate initializeExposedProperties() {\n\t\tObject.keys(this.config.exposeProperties).forEach((property) => {\n\t\t\tObject.defineProperty(this.item, property, {\n\t\t\t\tconfigurable: true, // This is needed to allow redefining it later\n\t\t\t\tenumerable: true,\n\t\t\t\tget() {\n\t\t\t\t\t// eslint-disable-next-line no-console\n\t\t\t\t\tconsole.warn(\n\t\t\t\t\t\t`Browser doesn't allow reading \"${property}\" until the drop event.`,\n\t\t\t\t\t)\n\t\t\t\t\treturn null\n\t\t\t\t},\n\t\t\t})\n\t\t})\n\t}\n\n\tpublic loadDataTransfer(dataTransfer: DataTransfer | null | undefined): void {\n\t\tif (dataTransfer) {\n\t\t\tconst newProperties: PropertyDescriptorMap = {}\n\t\t\tObject.keys(this.config.exposeProperties).forEach((property) => {\n\t\t\t\tconst propertyFn = this.config.exposeProperties[property]\n\t\t\t\tif (propertyFn != null) {\n\t\t\t\t\tnewProperties[property] = {\n\t\t\t\t\t\tvalue: propertyFn(dataTransfer, this.config.matchesTypes),\n\t\t\t\t\t\tconfigurable: true,\n\t\t\t\t\t\tenumerable: true,\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t})\n\t\t\tObject.defineProperties(this.item, newProperties)\n\t\t}\n\t}\n\n\tpublic canDrag(): boolean {\n\t\treturn true\n\t}\n\n\tpublic beginDrag(): any {\n\t\treturn this.item\n\t}\n\n\tpublic isDragging(monitor: DragDropMonitor, handle: string): boolean {\n\t\treturn handle === monitor.getSourceId()\n\t}\n\n\tpublic endDrag(): void {\n\t\t// empty\n\t}\n}\n"], "mappings": "AAIA,OAAO,MAAMA,gBAAgB;EAU5BC,2BAAmCA,CAAA,EAAG;IACrCC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,MAAM,CAACC,gBAAgB,CAAC,CAACC,OAAO,CAAEC,QAAQ,IAAK;MAC/DL,MAAM,CAACM,cAAc,CAAC,IAAI,CAACC,IAAI,EAAEF,QAAQ,EAAE;QAC1CG,YAAY,EAAE,IAAI;QAClBC,UAAU,EAAE,IAAI;QAChBC,GAAGA,CAAA,EAAG;UACL;UACAC,OAAO,CAACC,IAAI,CACX,kCAAkCP,QAAQ,yBAAyB,CACnE;UACD,OAAO,IAAI;;OAEZ,CAAC;KACF,CAAC;;EAGHQ,gBAAuBA,CAACC,YAA6C,EAAQ;IAC5E,IAAIA,YAAY,EAAE;MACjB,MAAMC,aAAa,GAA0B,EAAE;MAC/Cf,MAAM,CAACC,IAAI,CAAC,IAAI,CAACC,MAAM,CAACC,gBAAgB,CAAC,CAACC,OAAO,CAAEC,QAAQ,IAAK;QAC/D,MAAMW,UAAU,GAAG,IAAI,CAACd,MAAM,CAACC,gBAAgB,CAACE,QAAQ,CAAC;QACzD,IAAIW,UAAU,IAAI,IAAI,EAAE;UACvBD,aAAa,CAACV,QAAQ,CAAC,GAAG;YACzBY,KAAK,EAAED,UAAU,CAACF,YAAY,EAAE,IAAI,CAACZ,MAAM,CAACgB,YAAY,CAAC;YACzDV,YAAY,EAAE,IAAI;YAClBC,UAAU,EAAE;WACZ;;OAEF,CAAC;MACFT,MAAM,CAACmB,gBAAgB,CAAC,IAAI,CAACZ,IAAI,EAAEQ,aAAa,CAAC;;;EAInDK,OAAcA,CAAA,EAAY;IACzB,OAAO,IAAI;;EAGZC,SAAgBA,CAAA,EAAQ;IACvB,OAAO,IAAI,CAACd,IAAI;;EAGjBe,UAAiBA,CAACC,OAAwB,EAAEC,MAAc,EAAW;IACpE,OAAOA,MAAM,KAAKD,OAAO,CAACE,WAAW,EAAE;;EAGxCC,OAAcA,CAAA,EAAS;IACtB;EAAA;EApDDC,YAAmBzB,MAAwB,EAAE;IAC5C,IAAI,CAACA,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACK,IAAI,GAAG,EAAE;IACd,IAAI,CAACR,2BAA2B,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}