{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport { authService } from '../../utils/authService';\nimport { apiService } from '../../utils/apiService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleBasedDashboard = () => {\n  _s();\n  const [userRole, setUserRole] = useState('Member'); // Default role for demo\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n\n  // Mock data for different dashboard components\n  const mockProjects = [{\n    id: 1,\n    name: \"E-commerce Platform Redesign\",\n    description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n    status: \"Active\",\n    priority: \"High\",\n    progress: 75,\n    dueDate: \"Dec 15, 2025\",\n    team: [{\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    }, {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    }, {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    }, {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    }]\n  }, {\n    id: 2,\n    name: \"Mobile App Development\",\n    description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n    status: \"Active\",\n    priority: \"Medium\",\n    progress: 45,\n    dueDate: \"Jan 30, 2026\",\n    team: [{\n      name: \"David Kim\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\"\n    }, {\n      name: \"Lisa Wang\",\n      avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\"\n    }, {\n      name: \"Tom Wilson\",\n      avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\"\n    }]\n  }, {\n    id: 3,\n    name: \"Data Analytics Dashboard\",\n    description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n    status: \"Completed\",\n    priority: \"Low\",\n    progress: 100,\n    dueDate: \"Nov 20, 2025\",\n    team: [{\n      name: \"Rachel Green\",\n      avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\"\n    }, {\n      name: \"James Brown\",\n      avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\"\n    }]\n  }];\n  const mockActivities = [{\n    id: 1,\n    type: \"task_completed\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    description: \"Completed the user interface mockups for the checkout process\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 15 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 2,\n    type: \"comment_added\",\n    user: {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    },\n    description: \"Added feedback on the mobile responsive design implementation\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 45 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 3,\n    type: \"project_created\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    description: \"Created new project for Q1 marketing campaign automation\",\n    project: \"Marketing Automation\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    isPublic: false\n  }, {\n    id: 4,\n    type: \"member_added\",\n    user: {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    },\n    description: \"Joined the mobile app development team as a senior developer\",\n    project: \"Mobile App Development\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    isPublic: true\n  }];\n  const mockTasks = [{\n    id: 1,\n    title: \"Implement payment gateway integration\",\n    status: \"In Progress\",\n    priority: \"High\",\n    dueDate: \"Dec 10, 2025\",\n    assignee: \"Sarah Johnson\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 2,\n    title: \"Design user onboarding flow\",\n    status: \"To Do\",\n    priority: \"Medium\",\n    dueDate: \"Dec 12, 2025\",\n    assignee: \"Mike Chen\",\n    project: \"Mobile App\"\n  }, {\n    id: 3,\n    title: \"Set up automated testing pipeline\",\n    status: \"Review\",\n    priority: \"High\",\n    dueDate: \"Dec 8, 2025\",\n    assignee: \"Emily Davis\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 4,\n    title: \"Create API documentation\",\n    status: \"Done\",\n    priority: \"Low\",\n    dueDate: \"Dec 5, 2025\",\n    assignee: \"Alex Rodriguez\",\n    project: \"Data Analytics\"\n  }, {\n    id: 5,\n    title: \"Optimize database queries\",\n    status: \"Blocked\",\n    priority: \"High\",\n    dueDate: \"Dec 15, 2025\",\n    assignee: \"David Kim\",\n    project: \"E-commerce Platform\"\n  }];\n  const mockTeamMembers = [{\n    id: 1,\n    name: \"Sarah Johnson\",\n    email: \"<EMAIL>\",\n    role: \"Admin\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n    department: \"Design\",\n    currentTask: \"UI/UX Design Review\",\n    tasksCompleted: 24,\n    lastActive: new Date()\n  }, {\n    id: 2,\n    name: \"Mike Chen\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Away\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n    department: \"Development\",\n    currentTask: \"Frontend Implementation\",\n    tasksCompleted: 18,\n    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n  }, {\n    id: 3,\n    name: \"Emily Davis\",\n    email: \"<EMAIL>\",\n    role: \"Owner\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n    department: \"Management\",\n    currentTask: \"Project Planning\",\n    tasksCompleted: 32,\n    lastActive: new Date()\n  }, {\n    id: 4,\n    name: \"Alex Rodriguez\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Busy\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n    department: \"Development\",\n    currentTask: \"API Integration\",\n    tasksCompleted: 15,\n    lastActive: new Date(Date.now() - 30 * 60 * 1000)\n  }, {\n    id: 5,\n    name: \"David Kim\",\n    email: \"<EMAIL>\",\n    role: \"Viewer\",\n    status: \"Offline\",\n    avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n    department: \"QA\",\n    currentTask: null,\n    tasksCompleted: 8,\n    lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n  }];\n  const mockNotifications = [{\n    id: 1,\n    type: \"task_assigned\",\n    title: \"New Task Assigned\",\n    message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n    timestamp: new Date(Date.now() - 30 * 60 * 1000),\n    read: false,\n    priority: \"high\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    actions: [{\n      label: \"View Task\",\n      variant: \"default\"\n    }, {\n      label: \"Accept\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 2,\n    type: \"deadline_reminder\",\n    title: \"Deadline Approaching\",\n    message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    read: false,\n    priority: \"medium\",\n    user: null,\n    actions: [{\n      label: \"View Details\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 3,\n    type: \"comment_mention\",\n    title: \"You were mentioned\",\n    message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    read: true,\n    priority: \"low\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    actions: [{\n      label: \"Reply\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 4,\n    type: \"system_alert\",\n    title: \"System Maintenance\",\n    message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n    read: true,\n    priority: \"medium\",\n    user: null,\n    actions: []\n  }];\n\n  // Role-based KPI data\n  const getKPIData = () => {\n    switch (userRole) {\n      case 'Owner':\n        return [{\n          title: \"Total Revenue\",\n          value: \"$124,500\",\n          change: \"+12.5%\",\n          changeType: \"positive\",\n          icon: \"DollarSign\",\n          color: \"success\"\n        }, {\n          title: \"Active Projects\",\n          value: \"12\",\n          change: \"+3\",\n          changeType: \"positive\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: \"24\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Client Satisfaction\",\n          value: \"94%\",\n          change: \"+2%\",\n          changeType: \"positive\",\n          icon: \"Heart\",\n          color: \"success\"\n        }];\n      case 'Admin':\n        return [{\n          title: \"Active Projects\",\n          value: \"8\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Productivity\",\n          value: \"87%\",\n          change: \"+5%\",\n          changeType: \"positive\",\n          icon: \"TrendingUp\",\n          color: \"success\"\n        }, {\n          title: \"Pending Tasks\",\n          value: \"23\",\n          change: \"-4\",\n          changeType: \"positive\",\n          icon: \"CheckSquare\",\n          color: \"warning\"\n        }, {\n          title: \"Resource Utilization\",\n          value: \"92%\",\n          change: \"+3%\",\n          changeType: \"positive\",\n          icon: \"BarChart3\",\n          color: \"accent\"\n        }];\n      case 'Member':\n        return [{\n          title: \"My Tasks\",\n          value: \"8\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"CheckSquare\",\n          color: \"primary\"\n        }, {\n          title: \"Completed Today\",\n          value: \"3\",\n          change: \"+1\",\n          changeType: \"positive\",\n          icon: \"Check\",\n          color: \"success\"\n        }, {\n          title: \"Hours Logged\",\n          value: \"32h\",\n          change: \"+4h\",\n          changeType: \"positive\",\n          icon: \"Clock\",\n          color: \"accent\"\n        }, {\n          title: \"Projects Involved\",\n          value: \"4\",\n          change: \"0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"warning\"\n        }];\n      case 'Viewer':\n        return [{\n          title: \"Projects Viewed\",\n          value: \"6\",\n          change: \"+1\",\n          changeType: \"positive\",\n          icon: \"Eye\",\n          color: \"primary\"\n        }, {\n          title: \"Reports Generated\",\n          value: \"12\",\n          change: \"+3\",\n          changeType: \"positive\",\n          icon: \"FileText\",\n          color: \"accent\"\n        }, {\n          title: \"Data Exported\",\n          value: \"8\",\n          change: \"+2\",\n          changeType: \"positive\",\n          icon: \"Download\",\n          color: \"success\"\n        }, {\n          title: \"Dashboards Accessed\",\n          value: \"15\",\n          change: \"+5\",\n          changeType: \"positive\",\n          icon: \"BarChart3\",\n          color: \"warning\"\n        }];\n      default:\n        return [];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = mockProjects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) || project.description.toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || project.status.toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n  // Role switcher for demo purposes\n  const handleRoleChange = newRole => {\n    setUserRole(newRole);\n  };\n\n  // Mock user and organization data for the header\n  const currentUser = {\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    avatar: '/assets/images/avatar.jpg',\n    role: 'Project Manager'\n  };\n  const currentOrganization = {\n    name: 'Acme Corporation',\n    domain: 'acme.com',\n    logo: '/assets/images/org-logo.png'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser,\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"glass-effect border-b border-white/20 p-4 mt-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium text-slate-700\",\n            children: [\"Demo Mode - Current Role: \", userRole]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 348,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-2\",\n          children: ['Owner', 'Admin', 'Member', 'Viewer'].map(role => /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleRoleChange(role),\n            className: `px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${userRole === role ? 'bg-white text-slate-800 shadow-md' : 'text-slate-600 hover:bg-white/50 hover:text-slate-800'}`,\n            children: role\n          }, role, false, {\n            fileName: _jsxFileName,\n            lineNumber: 352,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 350,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DashboardHeader, {\n      userRole: userRole,\n      onFilterChange: setFilterValue,\n      onSearchChange: setSearchValue,\n      searchValue: searchValue\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\",\n        children: getKPIData().map((kpi, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in\",\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(KPICard, {\n            title: kpi.title,\n            value: kpi.value,\n            change: kpi.change,\n            changeType: kpi.changeType,\n            icon: kpi.icon,\n            color: kpi.color\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 382,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2 space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-semibold text-slate-800 tracking-tight\",\n                  children: userRole === 'Viewer' ? 'Available Projects' : 'My Projects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 402,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 mt-1\",\n                  children: \"Manage and track your active projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 405,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 401,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/project-overview-analytics\",\n                className: \"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",\n                children: [\"View Analytics\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 413,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 412,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 400,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(ProjectCard, {\n                  project: project,\n                  userRole: userRole\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 21\n                }, this)\n              }, project.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 418,\n              columnNumber: 15\n            }, this), filteredProjects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-slate-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-600 text-lg\",\n                children: \"No projects match your current filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-500 text-sm mt-1\",\n                children: \"Try adjusting your search or filter criteria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(QuickActions, {\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 397,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(ActivityFeed, {\n            activities: mockActivities,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationPanel, {\n            notifications: mockNotifications,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 447,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(TaskSummary, {\n          tasks: mockTasks,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TeamOverview, {\n          teamMembers: mockTeamMembers,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 335,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedDashboard, \"j3xI708uV/T5Z1mhMRd1pPmlpGE=\");\n_c = RoleBasedDashboard;\nexport default RoleBasedDashboard;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "RoleBasedHeader", "DashboardHeader", "KPICard", "ProjectCard", "ActivityFeed", "QuickActions", "TaskSummary", "TeamOverview", "NotificationPanel", "authService", "apiService", "jsxDEV", "_jsxDEV", "RoleBasedDashboard", "_s", "userRole", "setUserRole", "searchValue", "setSearchValue", "filterValue", "setFilterValue", "mockProjects", "id", "name", "description", "status", "priority", "progress", "dueDate", "team", "avatar", "mockActivities", "type", "user", "project", "timestamp", "Date", "now", "isPublic", "mockTasks", "title", "assignee", "mockTeamMembers", "email", "role", "department", "currentTask", "tasksCompleted", "lastActive", "mockNotifications", "message", "read", "actions", "label", "variant", "getKPIData", "value", "change", "changeType", "icon", "color", "filteredProjects", "filter", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "handleRoleChange", "newRole", "currentUser", "currentOrganization", "domain", "logo", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "onClick", "onFilterChange", "onSearchChange", "kpi", "index", "style", "animationDelay", "to", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "length", "activities", "notifications", "tasks", "teamMembers", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/role-based-dashboard/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport { authService } from '../../utils/authService';\nimport { apiService } from '../../utils/apiService';\n\nconst RoleBasedDashboard = () => {\n  const [userRole, setUserRole] = useState('Member'); // Default role for demo\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n\n  // Mock data for different dashboard components\n  const mockProjects = [\n    {\n      id: 1,\n      name: \"E-commerce Platform Redesign\",\n      description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n      status: \"Active\",\n      priority: \"High\",\n      progress: 75,\n      dueDate: \"Dec 15, 2025\",\n      team: [\n        { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n        { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n        { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n        { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" }\n      ]\n    },\n    {\n      id: 2,\n      name: \"Mobile App Development\",\n      description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n      status: \"Active\",\n      priority: \"Medium\",\n      progress: 45,\n      dueDate: \"Jan 30, 2026\",\n      team: [\n        { name: \"David Kim\", avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\" },\n        { name: \"Lisa Wang\", avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\" },\n        { name: \"Tom Wilson\", avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\" }\n      ]\n    },\n    {\n      id: 3,\n      name: \"Data Analytics Dashboard\",\n      description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n      status: \"Completed\",\n      priority: \"Low\",\n      progress: 100,\n      dueDate: \"Nov 20, 2025\",\n      team: [\n        { name: \"Rachel Green\", avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\" },\n        { name: \"James Brown\", avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\" }\n      ]\n    }\n  ];\n\n  const mockActivities = [\n    {\n      id: 1,\n      type: \"task_completed\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      description: \"Completed the user interface mockups for the checkout process\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 15 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 2,\n      type: \"comment_added\",\n      user: { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n      description: \"Added feedback on the mobile responsive design implementation\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 45 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 3,\n      type: \"project_created\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      description: \"Created new project for Q1 marketing campaign automation\",\n      project: \"Marketing Automation\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      isPublic: false\n    },\n    {\n      id: 4,\n      type: \"member_added\",\n      user: { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" },\n      description: \"Joined the mobile app development team as a senior developer\",\n      project: \"Mobile App Development\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      isPublic: true\n    }\n  ];\n\n  const mockTasks = [\n    {\n      id: 1,\n      title: \"Implement payment gateway integration\",\n      status: \"In Progress\",\n      priority: \"High\",\n      dueDate: \"Dec 10, 2025\",\n      assignee: \"Sarah Johnson\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 2,\n      title: \"Design user onboarding flow\",\n      status: \"To Do\",\n      priority: \"Medium\",\n      dueDate: \"Dec 12, 2025\",\n      assignee: \"Mike Chen\",\n      project: \"Mobile App\"\n    },\n    {\n      id: 3,\n      title: \"Set up automated testing pipeline\",\n      status: \"Review\",\n      priority: \"High\",\n      dueDate: \"Dec 8, 2025\",\n      assignee: \"Emily Davis\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 4,\n      title: \"Create API documentation\",\n      status: \"Done\",\n      priority: \"Low\",\n      dueDate: \"Dec 5, 2025\",\n      assignee: \"Alex Rodriguez\",\n      project: \"Data Analytics\"\n    },\n    {\n      id: 5,\n      title: \"Optimize database queries\",\n      status: \"Blocked\",\n      priority: \"High\",\n      dueDate: \"Dec 15, 2025\",\n      assignee: \"David Kim\",\n      project: \"E-commerce Platform\"\n    }\n  ];\n\n  const mockTeamMembers = [\n    {\n      id: 1,\n      name: \"Sarah Johnson\",\n      email: \"<EMAIL>\",\n      role: \"Admin\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n      department: \"Design\",\n      currentTask: \"UI/UX Design Review\",\n      tasksCompleted: 24,\n      lastActive: new Date()\n    },\n    {\n      id: 2,\n      name: \"Mike Chen\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Away\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n      department: \"Development\",\n      currentTask: \"Frontend Implementation\",\n      tasksCompleted: 18,\n      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n    },\n    {\n      id: 3,\n      name: \"Emily Davis\",\n      email: \"<EMAIL>\",\n      role: \"Owner\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n      department: \"Management\",\n      currentTask: \"Project Planning\",\n      tasksCompleted: 32,\n      lastActive: new Date()\n    },\n    {\n      id: 4,\n      name: \"Alex Rodriguez\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Busy\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n      department: \"Development\",\n      currentTask: \"API Integration\",\n      tasksCompleted: 15,\n      lastActive: new Date(Date.now() - 30 * 60 * 1000)\n    },\n    {\n      id: 5,\n      name: \"David Kim\",\n      email: \"<EMAIL>\",\n      role: \"Viewer\",\n      status: \"Offline\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n      department: \"QA\",\n      currentTask: null,\n      tasksCompleted: 8,\n      lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n    }\n  ];\n\n  const mockNotifications = [\n    {\n      id: 1,\n      type: \"task_assigned\",\n      title: \"New Task Assigned\",\n      message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n      timestamp: new Date(Date.now() - 30 * 60 * 1000),\n      read: false,\n      priority: \"high\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      actions: [\n        { label: \"View Task\", variant: \"default\" },\n        { label: \"Accept\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 2,\n      type: \"deadline_reminder\",\n      title: \"Deadline Approaching\",\n      message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      read: false,\n      priority: \"medium\",\n      user: null,\n      actions: [\n        { label: \"View Details\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 3,\n      type: \"comment_mention\",\n      title: \"You were mentioned\",\n      message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      read: true,\n      priority: \"low\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      actions: [\n        { label: \"Reply\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 4,\n      type: \"system_alert\",\n      title: \"System Maintenance\",\n      message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n      read: true,\n      priority: \"medium\",\n      user: null,\n      actions: []\n    }\n  ];\n\n  // Role-based KPI data\n  const getKPIData = () => {\n    switch (userRole) {\n      case 'Owner':\n        return [\n          { title: \"Total Revenue\", value: \"$124,500\", change: \"+12.5%\", changeType: \"positive\", icon: \"DollarSign\", color: \"success\" },\n          { title: \"Active Projects\", value: \"12\", change: \"+3\", changeType: \"positive\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: \"24\", change: \"+2\", changeType: \"positive\", icon: \"Users\", color: \"accent\" },\n          { title: \"Client Satisfaction\", value: \"94%\", change: \"+2%\", changeType: \"positive\", icon: \"Heart\", color: \"success\" }\n        ];\n      case 'Admin':\n        return [\n          { title: \"Active Projects\", value: \"8\", change: \"+2\", changeType: \"positive\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Productivity\", value: \"87%\", change: \"+5%\", changeType: \"positive\", icon: \"TrendingUp\", color: \"success\" },\n          { title: \"Pending Tasks\", value: \"23\", change: \"-4\", changeType: \"positive\", icon: \"CheckSquare\", color: \"warning\" },\n          { title: \"Resource Utilization\", value: \"92%\", change: \"+3%\", changeType: \"positive\", icon: \"BarChart3\", color: \"accent\" }\n        ];\n      case 'Member':\n        return [\n          { title: \"My Tasks\", value: \"8\", change: \"+2\", changeType: \"positive\", icon: \"CheckSquare\", color: \"primary\" },\n          { title: \"Completed Today\", value: \"3\", change: \"+1\", changeType: \"positive\", icon: \"Check\", color: \"success\" },\n          { title: \"Hours Logged\", value: \"32h\", change: \"+4h\", changeType: \"positive\", icon: \"Clock\", color: \"accent\" },\n          { title: \"Projects Involved\", value: \"4\", change: \"0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"warning\" }\n        ];\n      case 'Viewer':\n        return [\n          { title: \"Projects Viewed\", value: \"6\", change: \"+1\", changeType: \"positive\", icon: \"Eye\", color: \"primary\" },\n          { title: \"Reports Generated\", value: \"12\", change: \"+3\", changeType: \"positive\", icon: \"FileText\", color: \"accent\" },\n          { title: \"Data Exported\", value: \"8\", change: \"+2\", changeType: \"positive\", icon: \"Download\", color: \"success\" },\n          { title: \"Dashboards Accessed\", value: \"15\", change: \"+5\", changeType: \"positive\", icon: \"BarChart3\", color: \"warning\" }\n        ];\n      default:\n        return [];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = mockProjects.filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||\n                         project.description.toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || \n                         project.status.toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n  // Role switcher for demo purposes\n  const handleRoleChange = (newRole) => {\n    setUserRole(newRole);\n  };\n\n  // Mock user and organization data for the header\n  const currentUser = {\n    name: 'Sarah Johnson',\n    email: '<EMAIL>',\n    avatar: '/assets/images/avatar.jpg',\n    role: 'Project Manager'\n  };\n\n  const currentOrganization = {\n    name: 'Acme Corporation',\n    domain: 'acme.com',\n    logo: '/assets/images/org-logo.png'\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\">\n      {/* Role-Based Header */}\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Enhanced Demo Role Switcher */}\n      <div className=\"glass-effect border-b border-white/20 p-4 mt-16\">\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n            <span className=\"text-sm font-medium text-slate-700\">Demo Mode - Current Role: {userRole}</span>\n          </div>\n          <div className=\"flex items-center gap-2\">\n            {['Owner', 'Admin', 'Member', 'Viewer'].map((role) => (\n              <button\n                key={role}\n                onClick={() => handleRoleChange(role)}\n                className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${\n                  userRole === role\n                    ? 'bg-white text-slate-800 shadow-md'\n                    : 'text-slate-600 hover:bg-white/50 hover:text-slate-800'\n                }`}\n              >\n                {role}\n              </button>\n            ))}\n          </div>\n        </div>\n      </div>\n\n      {/* Dashboard Header */}\n      <DashboardHeader\n        userRole={userRole}\n        onFilterChange={setFilterValue}\n        onSearchChange={setSearchValue}\n        searchValue={searchValue}\n      />\n\n      {/* Main Dashboard Content */}\n      <div className=\"max-w-7xl mx-auto p-8\">\n        {/* Enhanced KPI Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\">\n          {getKPIData().map((kpi, index) => (\n            <div key={index} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <KPICard\n                title={kpi.title}\n                value={kpi.value}\n                change={kpi.change}\n                changeType={kpi.changeType}\n                icon={kpi.icon}\n                color={kpi.color}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content Grid with improved spacing */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\">\n          {/* Left Column - Projects and Quick Actions */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Projects Section with enhanced header */}\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h2 className=\"text-2xl font-semibold text-slate-800 tracking-tight\">\n                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}\n                  </h2>\n                  <p className=\"text-slate-600 mt-1\">Manage and track your active projects</p>\n                </div>\n                <Link \n                  to=\"/project-overview-analytics\"\n                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n                >\n                  View Analytics\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n                {filteredProjects.map((project, index) => (\n                  <div key={project.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                    <ProjectCard\n                      project={project}\n                      userRole={userRole}\n                    />\n                  </div>\n                ))}\n              </div>\n              \n              {filteredProjects.length === 0 && (\n                <div className=\"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\">\n                  <div className=\"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-8 h-8 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-slate-600 text-lg\">No projects match your current filters</p>\n                  <p className=\"text-slate-500 text-sm mt-1\">Try adjusting your search or filter criteria</p>\n                </div>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <QuickActions userRole={userRole} />\n          </div>\n\n          {/* Right Column - Activity Feed and Notifications */}\n          <div className=\"space-y-8\">\n            <ActivityFeed activities={mockActivities} userRole={userRole} />\n            <NotificationPanel notifications={mockNotifications} userRole={userRole} />\n          </div>\n        </div>\n\n        {/* Bottom Section - Tasks and Team with improved layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          <TaskSummary tasks={mockTasks} userRole={userRole} />\n          <TeamOverview teamMembers={mockTeamMembers} userRole={userRole} />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default RoleBasedDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,SAASC,WAAW,QAAQ,yBAAyB;AACrD,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGnB,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAACoB,WAAW,EAAEC,cAAc,CAAC,GAAGrB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsB,WAAW,EAAEC,cAAc,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;;EAErD;EACA,MAAMwB,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,8BAA8B;IACpCC,WAAW,EAAE,uHAAuH;IACpIC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACvG;MAAEP,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEP,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACrG;MAAEP,IAAI,EAAE,gBAAgB;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAE5G,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE,wGAAwG;IACrHC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEP,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAkE,CAAC,EAChG;MAAEP,IAAI,EAAE,YAAY;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAExG,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,0BAA0B;IAChCC,WAAW,EAAE,sHAAsH;IACnIC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,cAAc;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACtG;MAAEP,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAEzG,CAAC,CACF;EAED,MAAMC,cAAc,GAAG,CACrB;IACET,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,gBAAgB;IACtBC,IAAI,EAAE;MAAEV,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC7GN,WAAW,EAAE,+DAA+D;IAC5EU,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,eAAe;IACrBC,IAAI,EAAE;MAAEV,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC;IACzGN,WAAW,EAAE,+DAA+D;IAC5EU,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,iBAAiB;IACvBC,IAAI,EAAE;MAAEV,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC3GN,WAAW,EAAE,0DAA0D;IACvEU,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEhB,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE;MAAEV,IAAI,EAAE,gBAAgB;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC9GN,WAAW,EAAE,8DAA8D;IAC3EU,OAAO,EAAE,wBAAwB;IACjCC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IACEjB,EAAE,EAAE,CAAC;IACLkB,KAAK,EAAE,uCAAuC;IAC9Cf,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBa,QAAQ,EAAE,eAAe;IACzBP,OAAO,EAAE;EACX,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLkB,KAAK,EAAE,6BAA6B;IACpCf,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,QAAQ;IAClBE,OAAO,EAAE,cAAc;IACvBa,QAAQ,EAAE,WAAW;IACrBP,OAAO,EAAE;EACX,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLkB,KAAK,EAAE,mCAAmC;IAC1Cf,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,aAAa;IACtBa,QAAQ,EAAE,aAAa;IACvBP,OAAO,EAAE;EACX,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLkB,KAAK,EAAE,0BAA0B;IACjCf,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,KAAK;IACfE,OAAO,EAAE,aAAa;IACtBa,QAAQ,EAAE,gBAAgB;IAC1BP,OAAO,EAAE;EACX,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLkB,KAAK,EAAE,2BAA2B;IAClCf,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBa,QAAQ,EAAE,WAAW;IACrBP,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMQ,eAAe,GAAG,CACtB;IACEpB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBoB,KAAK,EAAE,wBAAwB;IAC/BC,IAAI,EAAE,OAAO;IACbnB,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5Ee,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIZ,IAAI,CAAC;EACvB,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBoB,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,QAAQ;IACdnB,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5Ee,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,yBAAyB;IACtCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIZ,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACtD,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBoB,KAAK,EAAE,sBAAsB;IAC7BC,IAAI,EAAE,OAAO;IACbnB,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5Ee,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIZ,IAAI,CAAC;EACvB,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBoB,KAAK,EAAE,yBAAyB;IAChCC,IAAI,EAAE,QAAQ;IACdnB,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5Ee,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,iBAAiB;IAC9BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIZ,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EAClD,CAAC,EACD;IACEf,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBoB,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,QAAQ;IACdnB,MAAM,EAAE,SAAS;IACjBK,MAAM,EAAE,oEAAoE;IAC5Ee,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE,IAAIZ,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACvD,CAAC,CACF;EAED,MAAMY,iBAAiB,GAAG,CACxB;IACE3B,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,eAAe;IACrBQ,KAAK,EAAE,mBAAmB;IAC1BU,OAAO,EAAE,gGAAgG;IACzGf,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDc,IAAI,EAAE,KAAK;IACXzB,QAAQ,EAAE,MAAM;IAChBO,IAAI,EAAE;MAAEV,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC3GsB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAU,CAAC,EAC1C;MAAED,KAAK,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE3C,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,mBAAmB;IACzBQ,KAAK,EAAE,sBAAsB;IAC7BU,OAAO,EAAE,oGAAoG;IAC7Gf,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDc,IAAI,EAAE,KAAK;IACXzB,QAAQ,EAAE,QAAQ;IAClBO,IAAI,EAAE,IAAI;IACVmB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAU,CAAC;EAEjD,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,iBAAiB;IACvBQ,KAAK,EAAE,oBAAoB;IAC3BU,OAAO,EAAE,iFAAiF;IAC1Ff,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDc,IAAI,EAAE,IAAI;IACVzB,QAAQ,EAAE,KAAK;IACfO,IAAI,EAAE;MAAEV,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC7GsB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE1C,CAAC,EACD;IACEhC,EAAE,EAAE,CAAC;IACLU,IAAI,EAAE,cAAc;IACpBQ,KAAK,EAAE,oBAAoB;IAC3BU,OAAO,EAAE,gHAAgH;IACzHf,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDc,IAAI,EAAE,IAAI;IACVzB,QAAQ,EAAE,QAAQ;IAClBO,IAAI,EAAE,IAAI;IACVmB,OAAO,EAAE;EACX,CAAC,CACF;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IACvB,QAAQxC,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,CACL;UAAEyB,KAAK,EAAE,eAAe;UAAEgB,KAAK,EAAE,UAAU;UAAEC,MAAM,EAAE,QAAQ;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC7H;UAAEpB,KAAK,EAAE,iBAAiB;UAAEgB,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACrH;UAAEpB,KAAK,EAAE,cAAc;UAAEgB,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC5G;UAAEpB,KAAK,EAAE,qBAAqB;UAAEgB,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,CACvH;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAEpB,KAAK,EAAE,iBAAiB;UAAEgB,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACpH;UAAEpB,KAAK,EAAE,mBAAmB;UAAEgB,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACzH;UAAEpB,KAAK,EAAE,eAAe;UAAEgB,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAU,CAAC,EACpH;UAAEpB,KAAK,EAAE,sBAAsB;UAAEgB,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAS,CAAC,CAC3H;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEpB,KAAK,EAAE,UAAU;UAAEgB,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,aAAa;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC9G;UAAEpB,KAAK,EAAE,iBAAiB;UAAEgB,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/G;UAAEpB,KAAK,EAAE,cAAc;UAAEgB,KAAK,EAAE,KAAK;UAAEC,MAAM,EAAE,KAAK;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC9G;UAAEpB,KAAK,EAAE,mBAAmB;UAAEgB,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,GAAG;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,CACrH;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEpB,KAAK,EAAE,iBAAiB;UAAEgB,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC7G;UAAEpB,KAAK,EAAE,mBAAmB;UAAEgB,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EACpH;UAAEpB,KAAK,EAAE,eAAe;UAAEgB,KAAK,EAAE,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAChH;UAAEpB,KAAK,EAAE,qBAAqB;UAAEgB,KAAK,EAAE,IAAI;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,UAAU;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAU,CAAC,CACzH;MACH;QACE,OAAO,EAAE;IACb;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGxC,YAAY,CAACyC,MAAM,CAAC5B,OAAO,IAAI;IACtD,MAAM6B,aAAa,GAAG7B,OAAO,CAACX,IAAI,CAACyC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,WAAW,CAAC+C,WAAW,CAAC,CAAC,CAAC,IAC/D9B,OAAO,CAACV,WAAW,CAACwC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAChD,WAAW,CAAC+C,WAAW,CAAC,CAAC,CAAC;IAC1F,MAAME,aAAa,GAAG/C,WAAW,KAAK,KAAK,IACtBe,OAAO,CAACT,MAAM,CAACuC,WAAW,CAAC,CAAC,KAAK7C,WAAW,CAAC6C,WAAW,CAAC,CAAC;IAC/E,OAAOD,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAEF;EACA,MAAMC,gBAAgB,GAAIC,OAAO,IAAK;IACpCpD,WAAW,CAACoD,OAAO,CAAC;EACtB,CAAC;;EAED;EACA,MAAMC,WAAW,GAAG;IAClB9C,IAAI,EAAE,eAAe;IACrBoB,KAAK,EAAE,2BAA2B;IAClCb,MAAM,EAAE,2BAA2B;IACnCc,IAAI,EAAE;EACR,CAAC;EAED,MAAM0B,mBAAmB,GAAG;IAC1B/C,IAAI,EAAE,kBAAkB;IACxBgD,MAAM,EAAE,UAAU;IAClBC,IAAI,EAAE;EACR,CAAC;EAED,oBACE5D,OAAA;IAAK6D,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1F9D,OAAA,CAACZ,eAAe;MACde,QAAQ,EAAEA,QAAQ,CAACiD,WAAW,CAAC,CAAE;MACjCK,WAAW,EAAEA,WAAY;MACzBC,mBAAmB,EAAEA;IAAoB;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,eAGFlE,OAAA;MAAK6D,SAAS,EAAC,iDAAiD;MAAAC,QAAA,eAC9D9D,OAAA;QAAK6D,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClE9D,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtC9D,OAAA;YAAK6D,SAAS,EAAC;UAAiD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACvElE,OAAA;YAAM6D,SAAS,EAAC,oCAAoC;YAAAC,QAAA,GAAC,4BAA0B,EAAC3D,QAAQ;UAAA;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7F,CAAC,eACNlE,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EACrC,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAACK,GAAG,CAAEnC,IAAI,iBAC/ChC,OAAA;YAEEoE,OAAO,EAAEA,CAAA,KAAMb,gBAAgB,CAACvB,IAAI,CAAE;YACtC6B,SAAS,EAAE,wEACT1D,QAAQ,KAAK6B,IAAI,GACb,mCAAmC,GACnC,uDAAuD,EAC1D;YAAA8B,QAAA,EAEF9B;UAAI,GARAA,IAAI;YAAA+B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OASH,CACT;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA,CAACX,eAAe;MACdc,QAAQ,EAAEA,QAAS;MACnBkE,cAAc,EAAE7D,cAAe;MAC/B8D,cAAc,EAAEhE,cAAe;MAC/BD,WAAW,EAAEA;IAAY;MAAA0D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGFlE,OAAA;MAAK6D,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAEpC9D,OAAA;QAAK6D,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxEnB,UAAU,CAAC,CAAC,CAACwB,GAAG,CAAC,CAACI,GAAG,EAAEC,KAAK,kBAC3BxE,OAAA;UAAiB6D,SAAS,EAAC,iBAAiB;UAACY,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAI,CAAE;UAAAV,QAAA,eACxF9D,OAAA,CAACV,OAAO;YACNsC,KAAK,EAAE2C,GAAG,CAAC3C,KAAM;YACjBgB,KAAK,EAAE2B,GAAG,CAAC3B,KAAM;YACjBC,MAAM,EAAE0B,GAAG,CAAC1B,MAAO;YACnBC,UAAU,EAAEyB,GAAG,CAACzB,UAAW;YAC3BC,IAAI,EAAEwB,GAAG,CAACxB,IAAK;YACfC,KAAK,EAAEuB,GAAG,CAACvB;UAAM;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GARMM,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNlE,OAAA;QAAK6D,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1D9D,OAAA;UAAK6D,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtC9D,OAAA;YAAK6D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9D,OAAA;cAAK6D,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChD9D,OAAA;gBAAA8D,QAAA,gBACE9D,OAAA;kBAAI6D,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EACjE3D,QAAQ,KAAK,QAAQ,GAAG,oBAAoB,GAAG;gBAAa;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACLlE,OAAA;kBAAG6D,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACNlE,OAAA,CAACb,IAAI;gBACHwF,EAAE,EAAC,6BAA6B;gBAChCd,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,GAC3K,gBAEC,eAAA9D,OAAA;kBAAK6D,SAAS,EAAC,SAAS;kBAACe,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eAC5E9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAENlE,OAAA;cAAK6D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnDb,gBAAgB,CAACkB,GAAG,CAAC,CAAC7C,OAAO,EAAEkD,KAAK,kBACnCxE,OAAA;gBAAsB6D,SAAS,EAAC,iBAAiB;gBAACY,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAAV,QAAA,eAC7F9D,OAAA,CAACT,WAAW;kBACV+B,OAAO,EAAEA,OAAQ;kBACjBnB,QAAQ,EAAEA;gBAAS;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC,GAJM5C,OAAO,CAACZ,EAAE;gBAAAqD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELjB,gBAAgB,CAACkC,MAAM,KAAK,CAAC,iBAC5BnF,OAAA;cAAK6D,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAChG9D,OAAA;gBAAK6D,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChG9D,OAAA;kBAAK6D,SAAS,EAAC,wBAAwB;kBAACe,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAhB,QAAA,eAC3F9D,OAAA;oBAAM+E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAsH;oBAAAnB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNlE,OAAA;gBAAG6D,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChFlE,OAAA;gBAAG6D,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGNlE,OAAA,CAACP,YAAY;YAACU,QAAQ,EAAEA;UAAS;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjC,CAAC,eAGNlE,OAAA;UAAK6D,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxB9D,OAAA,CAACR,YAAY;YAAC4F,UAAU,EAAEjE,cAAe;YAAChB,QAAQ,EAAEA;UAAS;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChElE,OAAA,CAACJ,iBAAiB;YAACyF,aAAa,EAAEhD,iBAAkB;YAAClC,QAAQ,EAAEA;UAAS;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlE,OAAA;QAAK6D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpD9D,OAAA,CAACN,WAAW;UAAC4F,KAAK,EAAE3D,SAAU;UAACxB,QAAQ,EAAEA;QAAS;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrDlE,OAAA,CAACL,YAAY;UAAC4F,WAAW,EAAEzD,eAAgB;UAAC3B,QAAQ,EAAEA;QAAS;UAAA4D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChE,EAAA,CA9bID,kBAAkB;AAAAuF,EAAA,GAAlBvF,kBAAkB;AAgcxB,eAAeA,kBAAkB;AAAC,IAAAuF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}