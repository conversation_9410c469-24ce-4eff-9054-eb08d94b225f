// src/utils/apiService.js

// API service for connecting to the backend
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// Helper function to get headers with authentication
const getAuthHeaders = (organizationId = null) => {
  const token = localStorage.getItem('accessToken');
  const headers = {
    'Content-Type': 'application/json',
    ...(organizationId && { 'X-Organization-ID': organizationId })
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return headers;
};

// Helper function to handle API responses
const handleResponse = async (response) => {
  const result = await response.json();

  if (!response.ok) {
    throw new Error(result.error?.message || result.message || 'API request failed');
  }

  return result;
};

const apiService = {
  // Organizations
  organizations: {
    getAll: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/v1/organizations`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Failed to fetch organizations:', error);
        throw error;
      }
    },

    create: async (orgData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/v1/organizations`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(orgData),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Failed to create organization:', error);
        throw error;
      }
    },

    getById: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to fetch organization:', error);
        throw error;
      }
    },

    update: async (id, updateData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(updateData),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to update organization:', error);
        throw error;
      }
    },

    delete: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/organizations/${id}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to delete organization:', error);
        throw error;
      }
    },

    getMembers: async (id, params = {}) => {
      try {
        const queryString = new URLSearchParams(params).toString();
        const url = `${API_BASE_URL}/v1/organizations/${id}/members${queryString ? `?${queryString}` : ''}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Failed to fetch organization members:', error);
        throw error;
      }
    },

    inviteMember: async (id, inviteData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/v1/organizations/${id}/invite`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(inviteData),
        });

        const result = await handleResponse(response);
        return result;
      } catch (error) {
        console.error('Failed to invite member:', error);
        throw error;
      }
    }
  },

  // Projects
  projects: {
    getAll: async (organizationId, params = {}) => {
      try {
        const queryParams = organizationId ? { organization_id: organizationId, ...params } : params;
        const queryString = new URLSearchParams(queryParams).toString();
        const url = `${API_BASE_URL}/v1/projects${queryString ? `?${queryString}` : ''}`;

        const response = await fetch(url, {
          method: 'GET',
          headers: getAuthHeaders(),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Failed to fetch projects:', error);
        throw error;
      }
    },

    create: async (organizationId, projectData) => {
      try {
        const dataWithOrg = {
          ...projectData,
          organization_id: organizationId
        };

        const response = await fetch(`${API_BASE_URL}/v1/projects`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(dataWithOrg),
        });

        const result = await handleResponse(response);
        return result.data;
      } catch (error) {
        console.error('Failed to create project:', error);
        throw error;
      }
    },

    getById: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to fetch project:', error);
        throw error;
      }
    },

    update: async (id, updateData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(updateData),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to update project:', error);
        throw error;
      }
    },

    delete: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/projects/${id}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to delete project:', error);
        throw error;
      }
    }
  },

  // Boards
  boards: {
    getByProject: async (projectId) => {
      try {
        const response = await fetch(`${API_BASE_URL}/projects/${projectId}/boards`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to fetch boards:', error);
        throw error;
      }
    },

    getById: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to fetch board:', error);
        throw error;
      }
    },

    create: async (projectId, boardData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/projects/${projectId}/boards`, {
          method: 'POST',
          headers: getAuthHeaders(),
          body: JSON.stringify(boardData),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to create board:', error);
        throw error;
      }
    },

    update: async (id, updateData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(updateData),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to update board:', error);
        throw error;
      }
    },

    delete: async (id) => {
      try {
        const response = await fetch(`${API_BASE_URL}/boards/${id}`, {
          method: 'DELETE',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to delete board:', error);
        throw error;
      }
    }
  },

  // Users
  users: {
    getProfile: async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/users/profile`, {
          method: 'GET',
          headers: getAuthHeaders(),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to fetch user profile:', error);
        throw error;
      }
    },

    updateProfile: async (updateData) => {
      try {
        const response = await fetch(`${API_BASE_URL}/users/profile`, {
          method: 'PUT',
          headers: getAuthHeaders(),
          body: JSON.stringify(updateData),
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to update user profile:', error);
        throw error;
      }
    },

    uploadAvatar: async (file) => {
      try {
        const formData = new FormData();
        formData.append('avatar', file);

        const token = localStorage.getItem('accessToken');
        const response = await fetch(`${API_BASE_URL}/users/avatar`, {
          method: 'POST',
          headers: {
            ...(token && { 'Authorization': `Bearer ${token}` })
          },
          body: formData,
        });
        
        return await handleResponse(response);
      } catch (error) {
        console.error('Failed to upload avatar:', error);
        throw error;
      }
    }
  }
};

export default apiService;
