{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\team-members\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useMemo } from 'react';\nimport Header from '../../components/ui/Header';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport Input from '../../components/ui/Input';\nimport Select from '../../components/ui/Select';\nimport MemberTable from './components/MemberTable';\nimport MemberCard from './components/MemberCard';\nimport InviteMemberModal from './components/InviteMemberModal';\nimport EditRoleModal from './components/EditRoleModal';\nimport MemberActivityModal from './components/MemberActivityModal';\nimport RemoveMemberModal from './components/RemoveMemberModal';\nimport BulkActionsBar from './components/BulkActionsBar';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeamMembers = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [roleFilter, setRoleFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [sortConfig, setSortConfig] = useState({\n    key: 'name',\n    direction: 'asc'\n  });\n  const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'\n  const [selectedMembers, setSelectedMembers] = useState([]);\n\n  // Modal states\n  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);\n  const [isEditRoleModalOpen, setIsEditRoleModalOpen] = useState(false);\n  const [isActivityModalOpen, setIsActivityModalOpen] = useState(false);\n  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);\n  const [selectedMember, setSelectedMember] = useState(null);\n\n  // Mock data for team members\n  const [members, setMembers] = useState([{\n    id: 1,\n    name: \"Sarah Johnson\",\n    email: \"<EMAIL>\",\n    role: \"Owner\",\n    status: \"active\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-01-15')\n  }, {\n    id: 2,\n    name: \"Michael Chen\",\n    email: \"<EMAIL>\",\n    role: \"Admin\",\n    status: \"active\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-02-20')\n  }, {\n    id: 3,\n    name: \"Emily Rodriguez\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"active\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-03-10')\n  }, {\n    id: 4,\n    name: \"David Kim\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"inactive\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-04-05')\n  }, {\n    id: 5,\n    name: \"Lisa Thompson\",\n    email: \"<EMAIL>\",\n    role: \"Viewer\",\n    status: \"active\",\n    avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 8 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-05-12')\n  }, {\n    id: 6,\n    name: \"James Wilson\",\n    email: \"<EMAIL>\",\n    role: \"Admin\",\n    status: \"pending\",\n    avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 12 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-06-18')\n  }, {\n    id: 7,\n    name: \"Anna Martinez\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"active\",\n    avatar: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-07-22')\n  }, {\n    id: 8,\n    name: \"Robert Davis\",\n    email: \"<EMAIL>\",\n    role: \"Viewer\",\n    status: \"active\",\n    avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face\",\n    lastActivity: new Date(Date.now() - 3 * 60 * 60 * 1000),\n    joinedDate: new Date('2023-08-14')\n  }]);\n  const roleOptions = [{\n    value: 'all',\n    label: 'All Roles'\n  }, {\n    value: 'owner',\n    label: 'Owner'\n  }, {\n    value: 'admin',\n    label: 'Admin'\n  }, {\n    value: 'member',\n    label: 'Member'\n  }, {\n    value: 'viewer',\n    label: 'Viewer'\n  }];\n  const statusOptions = [{\n    value: 'all',\n    label: 'All Status'\n  }, {\n    value: 'active',\n    label: 'Active'\n  }, {\n    value: 'inactive',\n    label: 'Inactive'\n  }, {\n    value: 'pending',\n    label: 'Pending'\n  }];\n\n  // Filter and sort members\n  const filteredAndSortedMembers = useMemo(() => {\n    let filtered = members.filter(member => {\n      const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) || member.email.toLowerCase().includes(searchQuery.toLowerCase());\n      const matchesRole = roleFilter === 'all' || member.role.toLowerCase() === roleFilter;\n      const matchesStatus = statusFilter === 'all' || member.status.toLowerCase() === statusFilter;\n      return matchesSearch && matchesRole && matchesStatus;\n    });\n\n    // Sort members\n    filtered.sort((a, b) => {\n      let aValue = a[sortConfig.key];\n      let bValue = b[sortConfig.key];\n      if (sortConfig.key === 'lastActivity') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      }\n      if (aValue < bValue) {\n        return sortConfig.direction === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return sortConfig.direction === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n    return filtered;\n  }, [members, searchQuery, roleFilter, statusFilter, sortConfig]);\n  const handleSort = key => {\n    setSortConfig(prevConfig => ({\n      key,\n      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'\n    }));\n  };\n  const handleSelectMember = memberId => {\n    setSelectedMembers(prev => prev.includes(memberId) ? prev.filter(id => id !== memberId) : [...prev, memberId]);\n  };\n  const handleSelectAll = () => {\n    if (selectedMembers.length === filteredAndSortedMembers.length) {\n      setSelectedMembers([]);\n    } else {\n      setSelectedMembers(filteredAndSortedMembers.map(member => member.id));\n    }\n  };\n  const handleInviteMembers = async inviteData => {\n    console.log('Inviting members:', inviteData);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Add mock invited members to the list\n    const newMembers = inviteData.emails.map((email, index) => ({\n      id: Date.now() + index,\n      name: email.split('@')[0].replace('.', ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n      email,\n      role: inviteData.role,\n      status: 'pending',\n      avatar: `https://images.unsplash.com/photo-150000000${index + 1}?w=150&h=150&fit=crop&crop=face`,\n      lastActivity: new Date(),\n      joinedDate: new Date()\n    }));\n    setMembers(prev => [...prev, ...newMembers]);\n  };\n  const handleEditRole = member => {\n    setSelectedMember(member);\n    setIsEditRoleModalOpen(true);\n  };\n  const handleUpdateRole = async (memberId, newRole) => {\n    console.log('Updating role:', memberId, newRole);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 500));\n    setMembers(prev => prev.map(member => member.id === memberId ? {\n      ...member,\n      role: newRole\n    } : member));\n  };\n  const handleViewActivity = member => {\n    setSelectedMember(member);\n    setIsActivityModalOpen(true);\n  };\n  const handleRemoveMember = member => {\n    setSelectedMember(member);\n    setIsRemoveModalOpen(true);\n  };\n  const handleConfirmRemove = async memberId => {\n    console.log('Removing member:', memberId);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 500));\n    setMembers(prev => prev.filter(member => member.id !== memberId));\n    setSelectedMembers(prev => prev.filter(id => id !== memberId));\n  };\n  const handleBulkRoleChange = async newRole => {\n    console.log('Bulk role change:', selectedMembers, newRole);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    setMembers(prev => prev.map(member => selectedMembers.includes(member.id) ? {\n      ...member,\n      role: newRole\n    } : member));\n    setSelectedMembers([]);\n  };\n  const handleBulkRemove = async () => {\n    console.log('Bulk remove:', selectedMembers);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    setMembers(prev => prev.filter(member => !selectedMembers.includes(member.id)));\n    setSelectedMembers([]);\n  };\n  const handleClearSelection = () => {\n    setSelectedMembers([]);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-background pt-16\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-3xl font-bold text-text-primary\",\n              children: \"Team Members\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-text-secondary mt-2\",\n              children: \"Manage your organization's team members, roles, and permissions\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: () => setIsInviteModalOpen(true),\n            iconName: \"UserPlus\",\n            iconPosition: \"left\",\n            children: \"Invite Members\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 268,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-card border border-border rounded-lg p-6 mb-6 shadow-ambient\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full sm:w-80\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                type: \"search\",\n                placeholder: \"Search members by name or email...\",\n                value: searchQuery,\n                onChange: e => setSearchQuery(e.target.value),\n                className: \"w-full\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 290,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Filter by role\",\n                options: roleOptions,\n                value: roleFilter,\n                onChange: setRoleFilter,\n                className: \"min-w-[140px]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"Filter by status\",\n                options: statusOptions,\n                value: statusFilter,\n                onChange: setStatusFilter,\n                className: \"min-w-[140px]\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 289,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center bg-muted rounded-lg p-1\",\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                variant: viewMode === 'table' ? 'default' : 'ghost',\n                size: \"sm\",\n                onClick: () => setViewMode('table'),\n                iconName: \"Table\",\n                className: \"h-8 w-8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 319,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                variant: viewMode === 'cards' ? 'default' : 'ghost',\n                size: \"sm\",\n                onClick: () => setViewMode('cards'),\n                iconName: \"Grid3X3\",\n                className: \"h-8 w-8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-sm text-text-secondary\",\n              children: [filteredAndSortedMembers.length, \" of \", members.length, \" members\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 317,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 287,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(BulkActionsBar, {\n        selectedCount: selectedMembers.length,\n        onBulkRoleChange: handleBulkRoleChange,\n        onBulkRemove: handleBulkRemove,\n        onClearSelection: handleClearSelection\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 9\n      }, this), viewMode === 'table' ? /*#__PURE__*/_jsxDEV(MemberTable, {\n        members: filteredAndSortedMembers,\n        sortConfig: sortConfig,\n        onSort: handleSort,\n        onEditRole: handleEditRole,\n        onViewActivity: handleViewActivity,\n        onRemoveMember: handleRemoveMember,\n        selectedMembers: selectedMembers,\n        onSelectMember: handleSelectMember,\n        onSelectAll: handleSelectAll\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n        children: filteredAndSortedMembers.map(member => /*#__PURE__*/_jsxDEV(MemberCard, {\n          member: member,\n          onEditRole: handleEditRole,\n          onViewActivity: handleViewActivity,\n          onRemoveMember: handleRemoveMember\n        }, member.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 15\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this), filteredAndSortedMembers.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Users\",\n          size: 48,\n          className: \"text-text-secondary mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-medium text-text-primary mb-2\",\n          children: \"No members found\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 380,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary mb-6\",\n          children: searchQuery || roleFilter !== 'all' || statusFilter !== 'all' ? 'Try adjusting your search or filter criteria' : 'Get started by inviting your first team member'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 13\n        }, this), !searchQuery && roleFilter === 'all' && statusFilter === 'all' && /*#__PURE__*/_jsxDEV(Button, {\n          onClick: () => setIsInviteModalOpen(true),\n          iconName: \"UserPlus\",\n          iconPosition: \"left\",\n          children: \"Invite Members\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 385,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(InviteMemberModal, {\n        isOpen: isInviteModalOpen,\n        onClose: () => setIsInviteModalOpen(false),\n        onInvite: handleInviteMembers\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 397,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(EditRoleModal, {\n        isOpen: isEditRoleModalOpen,\n        onClose: () => setIsEditRoleModalOpen(false),\n        member: selectedMember,\n        onUpdateRole: handleUpdateRole\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(MemberActivityModal, {\n        isOpen: isActivityModalOpen,\n        onClose: () => setIsActivityModalOpen(false),\n        member: selectedMember\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(RemoveMemberModal, {\n        isOpen: isRemoveModalOpen,\n        onClose: () => setIsRemoveModalOpen(false),\n        member: selectedMember,\n        onRemove: handleConfirmRemove\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 416,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 266,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 265,\n    columnNumber: 5\n  }, this);\n};\n_s(TeamMembers, \"lwyvCPuDGKgJ9T1ljU8ajRG3scU=\");\n_c = TeamMembers;\nexport default TeamMembers;\nvar _c;\n$RefreshReg$(_c, \"TeamMembers\");", "map": {"version": 3, "names": ["React", "useState", "useMemo", "Header", "Breadcrumb", "Icon", "<PERSON><PERSON>", "Input", "Select", "MemberTable", "MemberCard", "InviteMemberModal", "EditRoleModal", "MemberActivityModal", "RemoveMemberModal", "BulkActionsBar", "jsxDEV", "_jsxDEV", "TeamMembers", "_s", "searchQuery", "setSearch<PERSON>uery", "<PERSON><PERSON><PERSON>er", "setRoleFilter", "statusFilter", "setStatus<PERSON>ilter", "sortConfig", "setSortConfig", "key", "direction", "viewMode", "setViewMode", "selected<PERSON><PERSON><PERSON>", "setSelectedMembers", "isInviteModalOpen", "setIsInviteModalOpen", "isEditRoleModalOpen", "setIsEditRoleModalOpen", "isActivityModalOpen", "setIsActivityModalOpen", "isRemoveModalOpen", "setIsRemoveModalOpen", "selected<PERSON><PERSON>ber", "setSelectedMember", "members", "setMembers", "id", "name", "email", "role", "status", "avatar", "lastActivity", "Date", "now", "joinedDate", "roleOptions", "value", "label", "statusOptions", "filteredAndSortedMembers", "filtered", "filter", "member", "matchesSearch", "toLowerCase", "includes", "matchesRole", "matchesStatus", "sort", "a", "b", "aValue", "bValue", "handleSort", "prevConfig", "handleSelectMember", "memberId", "prev", "handleSelectAll", "length", "map", "handleInviteMembers", "inviteData", "console", "log", "Promise", "resolve", "setTimeout", "newMembers", "emails", "index", "split", "replace", "l", "toUpperCase", "handleEditRole", "handleUpdateRole", "newRole", "handleViewActivity", "handleRemoveMember", "handleConfirmRemove", "handleBulkRoleChange", "handleBulkRemove", "handleClearSelection", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "iconName", "iconPosition", "type", "placeholder", "onChange", "e", "target", "options", "variant", "size", "selectedCount", "onBulkRoleChange", "onBulkRemove", "onClearSelection", "onSort", "onEditRole", "onViewActivity", "onRemoveMember", "onSelectMember", "onSelectAll", "isOpen", "onClose", "onInvite", "onUpdateRole", "onRemove", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/team-members/index.jsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\nimport Header from '../../components/ui/Header';\nimport Breadcrumb from '../../components/ui/Breadcrumb';\nimport Icon from '../../components/AppIcon';\nimport Button from '../../components/ui/Button';\nimport Input from '../../components/ui/Input';\nimport Select from '../../components/ui/Select';\nimport MemberTable from './components/MemberTable';\nimport MemberCard from './components/MemberCard';\nimport InviteMemberModal from './components/InviteMemberModal';\nimport EditRoleModal from './components/EditRoleModal';\nimport MemberActivityModal from './components/MemberActivityModal';\nimport RemoveMemberModal from './components/RemoveMemberModal';\nimport BulkActionsBar from './components/BulkActionsBar';\n\nconst TeamMembers = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [roleFilter, setRoleFilter] = useState('all');\n  const [statusFilter, setStatusFilter] = useState('all');\n  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });\n  const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'\n  const [selectedMembers, setSelectedMembers] = useState([]);\n  \n  // Modal states\n  const [isInviteModalOpen, setIsInviteModalOpen] = useState(false);\n  const [isEditRoleModalOpen, setIsEditRoleModalOpen] = useState(false);\n  const [isActivityModalOpen, setIsActivityModalOpen] = useState(false);\n  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false);\n  const [selectedMember, setSelectedMember] = useState(null);\n\n  // Mock data for team members\n  const [members, setMembers] = useState([\n    {\n      id: 1,\n      name: \"Sarah Johnson\",\n      email: \"<EMAIL>\",\n      role: \"Owner\",\n      status: \"active\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-01-15')\n    },\n    {\n      id: 2,\n      name: \"Michael Chen\",\n      email: \"<EMAIL>\",\n      role: \"Admin\",\n      status: \"active\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-02-20')\n    },\n    {\n      id: 3,\n      name: \"Emily Rodriguez\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"active\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 6 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-03-10')\n    },\n    {\n      id: 4,\n      name: \"David Kim\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"inactive\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-04-05')\n    },\n    {\n      id: 5,\n      name: \"Lisa Thompson\",\n      email: \"<EMAIL>\",\n      role: \"Viewer\",\n      status: \"active\",\n      avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 8 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-05-12')\n    },\n    {\n      id: 6,\n      name: \"James Wilson\",\n      email: \"<EMAIL>\",\n      role: \"Admin\",\n      status: \"pending\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 12 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-06-18')\n    },\n    {\n      id: 7,\n      name: \"Anna Martinez\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"active\",\n      avatar: \"https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 1 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-07-22')\n    },\n    {\n      id: 8,\n      name: \"Robert Davis\",\n      email: \"<EMAIL>\",\n      role: \"Viewer\",\n      status: \"active\",\n      avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face\",\n      lastActivity: new Date(Date.now() - 3 * 60 * 60 * 1000),\n      joinedDate: new Date('2023-08-14')\n    }\n  ]);\n\n  const roleOptions = [\n    { value: 'all', label: 'All Roles' },\n    { value: 'owner', label: 'Owner' },\n    { value: 'admin', label: 'Admin' },\n    { value: 'member', label: 'Member' },\n    { value: 'viewer', label: 'Viewer' }\n  ];\n\n  const statusOptions = [\n    { value: 'all', label: 'All Status' },\n    { value: 'active', label: 'Active' },\n    { value: 'inactive', label: 'Inactive' },\n    { value: 'pending', label: 'Pending' }\n  ];\n\n  // Filter and sort members\n  const filteredAndSortedMembers = useMemo(() => {\n    let filtered = members.filter(member => {\n      const matchesSearch = member.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                           member.email.toLowerCase().includes(searchQuery.toLowerCase());\n      const matchesRole = roleFilter === 'all' || member.role.toLowerCase() === roleFilter;\n      const matchesStatus = statusFilter === 'all' || member.status.toLowerCase() === statusFilter;\n      \n      return matchesSearch && matchesRole && matchesStatus;\n    });\n\n    // Sort members\n    filtered.sort((a, b) => {\n      let aValue = a[sortConfig.key];\n      let bValue = b[sortConfig.key];\n\n      if (sortConfig.key === 'lastActivity') {\n        aValue = new Date(aValue);\n        bValue = new Date(bValue);\n      }\n\n      if (aValue < bValue) {\n        return sortConfig.direction === 'asc' ? -1 : 1;\n      }\n      if (aValue > bValue) {\n        return sortConfig.direction === 'asc' ? 1 : -1;\n      }\n      return 0;\n    });\n\n    return filtered;\n  }, [members, searchQuery, roleFilter, statusFilter, sortConfig]);\n\n  const handleSort = (key) => {\n    setSortConfig(prevConfig => ({\n      key,\n      direction: prevConfig.key === key && prevConfig.direction === 'asc' ? 'desc' : 'asc'\n    }));\n  };\n\n  const handleSelectMember = (memberId) => {\n    setSelectedMembers(prev => \n      prev.includes(memberId) \n        ? prev.filter(id => id !== memberId)\n        : [...prev, memberId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    if (selectedMembers.length === filteredAndSortedMembers.length) {\n      setSelectedMembers([]);\n    } else {\n      setSelectedMembers(filteredAndSortedMembers.map(member => member.id));\n    }\n  };\n\n  const handleInviteMembers = async (inviteData) => {\n    console.log('Inviting members:', inviteData);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    // Add mock invited members to the list\n    const newMembers = inviteData.emails.map((email, index) => ({\n      id: Date.now() + index,\n      name: email.split('@')[0].replace('.', ' ').replace(/\\b\\w/g, l => l.toUpperCase()),\n      email,\n      role: inviteData.role,\n      status: 'pending',\n      avatar: `https://images.unsplash.com/photo-150000000${index + 1}?w=150&h=150&fit=crop&crop=face`,\n      lastActivity: new Date(),\n      joinedDate: new Date()\n    }));\n    \n    setMembers(prev => [...prev, ...newMembers]);\n  };\n\n  const handleEditRole = (member) => {\n    setSelectedMember(member);\n    setIsEditRoleModalOpen(true);\n  };\n\n  const handleUpdateRole = async (memberId, newRole) => {\n    console.log('Updating role:', memberId, newRole);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    setMembers(prev => prev.map(member => \n      member.id === memberId ? { ...member, role: newRole } : member\n    ));\n  };\n\n  const handleViewActivity = (member) => {\n    setSelectedMember(member);\n    setIsActivityModalOpen(true);\n  };\n\n  const handleRemoveMember = (member) => {\n    setSelectedMember(member);\n    setIsRemoveModalOpen(true);\n  };\n\n  const handleConfirmRemove = async (memberId) => {\n    console.log('Removing member:', memberId);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 500));\n    \n    setMembers(prev => prev.filter(member => member.id !== memberId));\n    setSelectedMembers(prev => prev.filter(id => id !== memberId));\n  };\n\n  const handleBulkRoleChange = async (newRole) => {\n    console.log('Bulk role change:', selectedMembers, newRole);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    setMembers(prev => prev.map(member => \n      selectedMembers.includes(member.id) ? { ...member, role: newRole } : member\n    ));\n    setSelectedMembers([]);\n  };\n\n  const handleBulkRemove = async () => {\n    console.log('Bulk remove:', selectedMembers);\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    \n    setMembers(prev => prev.filter(member => !selectedMembers.includes(member.id)));\n    setSelectedMembers([]);\n  };\n\n  const handleClearSelection = () => {\n    setSelectedMembers([]);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-background pt-16\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold text-text-primary\">Team Members</h1>\n              <p className=\"text-text-secondary mt-2\">\n                Manage your organization's team members, roles, and permissions\n              </p>\n            </div>\n            <Button\n              onClick={() => setIsInviteModalOpen(true)}\n              iconName=\"UserPlus\"\n              iconPosition=\"left\"\n            >\n              Invite Members\n            </Button>\n          </div>\n        </div>\n\n        {/* Filters and Search */}\n        <div className=\"bg-card border border-border rounded-lg p-6 mb-6 shadow-ambient\">\n          <div className=\"flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0\">\n            <div className=\"flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4\">\n              <div className=\"w-full sm:w-80\">\n                <Input\n                  type=\"search\"\n                  placeholder=\"Search members by name or email...\"\n                  value={searchQuery}\n                  onChange={(e) => setSearchQuery(e.target.value)}\n                  className=\"w-full\"\n                />\n              </div>\n              <div className=\"flex space-x-3\">\n                <Select\n                  placeholder=\"Filter by role\"\n                  options={roleOptions}\n                  value={roleFilter}\n                  onChange={setRoleFilter}\n                  className=\"min-w-[140px]\"\n                />\n                <Select\n                  placeholder=\"Filter by status\"\n                  options={statusOptions}\n                  value={statusFilter}\n                  onChange={setStatusFilter}\n                  className=\"min-w-[140px]\"\n                />\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"flex items-center bg-muted rounded-lg p-1\">\n                <Button\n                  variant={viewMode === 'table' ? 'default' : 'ghost'}\n                  size=\"sm\"\n                  onClick={() => setViewMode('table')}\n                  iconName=\"Table\"\n                  className=\"h-8 w-8\"\n                />\n                <Button\n                  variant={viewMode === 'cards' ? 'default' : 'ghost'}\n                  size=\"sm\"\n                  onClick={() => setViewMode('cards')}\n                  iconName=\"Grid3X3\"\n                  className=\"h-8 w-8\"\n                />\n              </div>\n              <div className=\"text-sm text-text-secondary\">\n                {filteredAndSortedMembers.length} of {members.length} members\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Bulk Actions */}\n        <BulkActionsBar\n          selectedCount={selectedMembers.length}\n          onBulkRoleChange={handleBulkRoleChange}\n          onBulkRemove={handleBulkRemove}\n          onClearSelection={handleClearSelection}\n        />\n\n        {/* Members List */}\n        {viewMode === 'table' ? (\n          <MemberTable\n            members={filteredAndSortedMembers}\n            sortConfig={sortConfig}\n            onSort={handleSort}\n            onEditRole={handleEditRole}\n            onViewActivity={handleViewActivity}\n            onRemoveMember={handleRemoveMember}\n            selectedMembers={selectedMembers}\n            onSelectMember={handleSelectMember}\n            onSelectAll={handleSelectAll}\n          />\n        ) : (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n            {filteredAndSortedMembers.map((member) => (\n              <MemberCard\n                key={member.id}\n                member={member}\n                onEditRole={handleEditRole}\n                onViewActivity={handleViewActivity}\n                onRemoveMember={handleRemoveMember}\n              />\n            ))}\n          </div>\n        )}\n\n        {/* Empty State */}\n        {filteredAndSortedMembers.length === 0 && (\n          <div className=\"text-center py-12\">\n            <Icon name=\"Users\" size={48} className=\"text-text-secondary mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-text-primary mb-2\">No members found</h3>\n            <p className=\"text-text-secondary mb-6\">\n              {searchQuery || roleFilter !== 'all' || statusFilter !== 'all' ?'Try adjusting your search or filter criteria' :'Get started by inviting your first team member'}\n            </p>\n            {(!searchQuery && roleFilter === 'all' && statusFilter === 'all') && (\n              <Button\n                onClick={() => setIsInviteModalOpen(true)}\n                iconName=\"UserPlus\"\n                iconPosition=\"left\"\n              >\n                Invite Members\n              </Button>\n            )}\n          </div>\n        )}\n\n        {/* Modals */}\n        <InviteMemberModal\n          isOpen={isInviteModalOpen}\n          onClose={() => setIsInviteModalOpen(false)}\n          onInvite={handleInviteMembers}\n        />\n\n        <EditRoleModal\n          isOpen={isEditRoleModalOpen}\n          onClose={() => setIsEditRoleModalOpen(false)}\n          member={selectedMember}\n          onUpdateRole={handleUpdateRole}\n        />\n\n        <MemberActivityModal\n          isOpen={isActivityModalOpen}\n          onClose={() => setIsActivityModalOpen(false)}\n          member={selectedMember}\n        />\n\n        <RemoveMemberModal\n          isOpen={isRemoveModalOpen}\n          onClose={() => setIsRemoveModalOpen(false)}\n          member={selectedMember}\n          onRemove={handleConfirmRemove}\n        />\n      </div>\n    </div>\n  );\n};\n\nexport default TeamMembers;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,OAAO,QAAQ,OAAO;AAChD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,UAAU,MAAM,gCAAgC;AACvD,OAAOC,IAAI,MAAM,0BAA0B;AAC3C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,KAAK,MAAM,2BAA2B;AAC7C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,mBAAmB,MAAM,kCAAkC;AAClE,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,cAAc,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACqB,UAAU,EAAEC,aAAa,CAAC,GAAGtB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACuB,YAAY,EAAEC,eAAe,CAAC,GAAGxB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAC;IAAE2B,GAAG,EAAE,MAAM;IAAEC,SAAS,EAAE;EAAM,CAAC,CAAC;EAC/E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;EACnD,MAAM,CAAC+B,eAAe,EAAEC,kBAAkB,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;;EAE1D;EACA,MAAM,CAACiC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACqC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EACrE,MAAM,CAACuC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACyC,cAAc,EAAEC,iBAAiB,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;;EAE1D;EACA,MAAM,CAAC2C,OAAO,EAAEC,UAAU,CAAC,GAAG5C,QAAQ,CAAC,CACrC;IACE6C,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,2BAA2B;IAClCC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,6FAA6F;IACrGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACvDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,0BAA0B;IACjCC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,6FAA6F;IACrGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACvDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,iBAAiB;IACvBC,KAAK,EAAE,6BAA6B;IACpCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,6FAA6F;IACrGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACvDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBC,KAAK,EAAE,uBAAuB;IAC9BC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,UAAU;IAClBC,MAAM,EAAE,6FAA6F;IACrGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACxDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,2BAA2B;IAClCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,0FAA0F;IAClGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACvDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,0BAA0B;IACjCC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,6FAA6F;IACrGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACxDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,2BAA2B;IAClCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,6FAA6F;IACrGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACvDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,0BAA0B;IACjCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,QAAQ;IAChBC,MAAM,EAAE,6FAA6F;IACrGC,YAAY,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACvDC,UAAU,EAAE,IAAIF,IAAI,CAAC,YAAY;EACnC,CAAC,CACF,CAAC;EAEF,MAAMG,WAAW,GAAG,CAClB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAY,CAAC,EACpC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAQ,CAAC,EAClC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,CACrC;EAED,MAAMC,aAAa,GAAG,CACpB;IAAEF,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAa,CAAC,EACrC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAS,CAAC,EACpC;IAAED,KAAK,EAAE,UAAU;IAAEC,KAAK,EAAE;EAAW,CAAC,EACxC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAU,CAAC,CACvC;;EAED;EACA,MAAME,wBAAwB,GAAG1D,OAAO,CAAC,MAAM;IAC7C,IAAI2D,QAAQ,GAAGjB,OAAO,CAACkB,MAAM,CAACC,MAAM,IAAI;MACtC,MAAMC,aAAa,GAAGD,MAAM,CAAChB,IAAI,CAACkB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,WAAW,CAAC6C,WAAW,CAAC,CAAC,CAAC,IAC9DF,MAAM,CAACf,KAAK,CAACiB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC9C,WAAW,CAAC6C,WAAW,CAAC,CAAC,CAAC;MACnF,MAAME,WAAW,GAAG7C,UAAU,KAAK,KAAK,IAAIyC,MAAM,CAACd,IAAI,CAACgB,WAAW,CAAC,CAAC,KAAK3C,UAAU;MACpF,MAAM8C,aAAa,GAAG5C,YAAY,KAAK,KAAK,IAAIuC,MAAM,CAACb,MAAM,CAACe,WAAW,CAAC,CAAC,KAAKzC,YAAY;MAE5F,OAAOwC,aAAa,IAAIG,WAAW,IAAIC,aAAa;IACtD,CAAC,CAAC;;IAEF;IACAP,QAAQ,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACtB,IAAIC,MAAM,GAAGF,CAAC,CAAC5C,UAAU,CAACE,GAAG,CAAC;MAC9B,IAAI6C,MAAM,GAAGF,CAAC,CAAC7C,UAAU,CAACE,GAAG,CAAC;MAE9B,IAAIF,UAAU,CAACE,GAAG,KAAK,cAAc,EAAE;QACrC4C,MAAM,GAAG,IAAInB,IAAI,CAACmB,MAAM,CAAC;QACzBC,MAAM,GAAG,IAAIpB,IAAI,CAACoB,MAAM,CAAC;MAC3B;MAEA,IAAID,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO/C,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;MAChD;MACA,IAAI2C,MAAM,GAAGC,MAAM,EAAE;QACnB,OAAO/C,UAAU,CAACG,SAAS,KAAK,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;MAChD;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,OAAOgC,QAAQ;EACjB,CAAC,EAAE,CAACjB,OAAO,EAAExB,WAAW,EAAEE,UAAU,EAAEE,YAAY,EAAEE,UAAU,CAAC,CAAC;EAEhE,MAAMgD,UAAU,GAAI9C,GAAG,IAAK;IAC1BD,aAAa,CAACgD,UAAU,KAAK;MAC3B/C,GAAG;MACHC,SAAS,EAAE8C,UAAU,CAAC/C,GAAG,KAAKA,GAAG,IAAI+C,UAAU,CAAC9C,SAAS,KAAK,KAAK,GAAG,MAAM,GAAG;IACjF,CAAC,CAAC,CAAC;EACL,CAAC;EAED,MAAM+C,kBAAkB,GAAIC,QAAQ,IAAK;IACvC5C,kBAAkB,CAAC6C,IAAI,IACrBA,IAAI,CAACZ,QAAQ,CAACW,QAAQ,CAAC,GACnBC,IAAI,CAAChB,MAAM,CAAChB,EAAE,IAAIA,EAAE,KAAK+B,QAAQ,CAAC,GAClC,CAAC,GAAGC,IAAI,EAAED,QAAQ,CACxB,CAAC;EACH,CAAC;EAED,MAAME,eAAe,GAAGA,CAAA,KAAM;IAC5B,IAAI/C,eAAe,CAACgD,MAAM,KAAKpB,wBAAwB,CAACoB,MAAM,EAAE;MAC9D/C,kBAAkB,CAAC,EAAE,CAAC;IACxB,CAAC,MAAM;MACLA,kBAAkB,CAAC2B,wBAAwB,CAACqB,GAAG,CAAClB,MAAM,IAAIA,MAAM,CAACjB,EAAE,CAAC,CAAC;IACvE;EACF,CAAC;EAED,MAAMoC,mBAAmB,GAAG,MAAOC,UAAU,IAAK;IAChDC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEF,UAAU,CAAC;IAC5C;IACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,UAAU,GAAGN,UAAU,CAACO,MAAM,CAACT,GAAG,CAAC,CAACjC,KAAK,EAAE2C,KAAK,MAAM;MAC1D7C,EAAE,EAAEO,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGqC,KAAK;MACtB5C,IAAI,EAAEC,KAAK,CAAC4C,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,OAAO,EAAEC,CAAC,IAAIA,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;MAClF/C,KAAK;MACLC,IAAI,EAAEkC,UAAU,CAAClC,IAAI;MACrBC,MAAM,EAAE,SAAS;MACjBC,MAAM,EAAE,8CAA8CwC,KAAK,GAAG,CAAC,iCAAiC;MAChGvC,YAAY,EAAE,IAAIC,IAAI,CAAC,CAAC;MACxBE,UAAU,EAAE,IAAIF,IAAI,CAAC;IACvB,CAAC,CAAC,CAAC;IAEHR,UAAU,CAACiC,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE,GAAGW,UAAU,CAAC,CAAC;EAC9C,CAAC;EAED,MAAMO,cAAc,GAAIjC,MAAM,IAAK;IACjCpB,iBAAiB,CAACoB,MAAM,CAAC;IACzB1B,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM4D,gBAAgB,GAAG,MAAAA,CAAOpB,QAAQ,EAAEqB,OAAO,KAAK;IACpDd,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAER,QAAQ,EAAEqB,OAAO,CAAC;IAChD;IACA,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD1C,UAAU,CAACiC,IAAI,IAAIA,IAAI,CAACG,GAAG,CAAClB,MAAM,IAChCA,MAAM,CAACjB,EAAE,KAAK+B,QAAQ,GAAG;MAAE,GAAGd,MAAM;MAAEd,IAAI,EAAEiD;IAAQ,CAAC,GAAGnC,MAC1D,CAAC,CAAC;EACJ,CAAC;EAED,MAAMoC,kBAAkB,GAAIpC,MAAM,IAAK;IACrCpB,iBAAiB,CAACoB,MAAM,CAAC;IACzBxB,sBAAsB,CAAC,IAAI,CAAC;EAC9B,CAAC;EAED,MAAM6D,kBAAkB,GAAIrC,MAAM,IAAK;IACrCpB,iBAAiB,CAACoB,MAAM,CAAC;IACzBtB,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM4D,mBAAmB,GAAG,MAAOxB,QAAQ,IAAK;IAC9CO,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAER,QAAQ,CAAC;IACzC;IACA,MAAM,IAAIS,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD1C,UAAU,CAACiC,IAAI,IAAIA,IAAI,CAAChB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACjB,EAAE,KAAK+B,QAAQ,CAAC,CAAC;IACjE5C,kBAAkB,CAAC6C,IAAI,IAAIA,IAAI,CAAChB,MAAM,CAAChB,EAAE,IAAIA,EAAE,KAAK+B,QAAQ,CAAC,CAAC;EAChE,CAAC;EAED,MAAMyB,oBAAoB,GAAG,MAAOJ,OAAO,IAAK;IAC9Cd,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAErD,eAAe,EAAEkE,OAAO,CAAC;IAC1D;IACA,MAAM,IAAIZ,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD1C,UAAU,CAACiC,IAAI,IAAIA,IAAI,CAACG,GAAG,CAAClB,MAAM,IAChC/B,eAAe,CAACkC,QAAQ,CAACH,MAAM,CAACjB,EAAE,CAAC,GAAG;MAAE,GAAGiB,MAAM;MAAEd,IAAI,EAAEiD;IAAQ,CAAC,GAAGnC,MACvE,CAAC,CAAC;IACF9B,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMsE,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnCnB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAErD,eAAe,CAAC;IAC5C;IACA,MAAM,IAAIsD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;IAEvD1C,UAAU,CAACiC,IAAI,IAAIA,IAAI,CAAChB,MAAM,CAACC,MAAM,IAAI,CAAC/B,eAAe,CAACkC,QAAQ,CAACH,MAAM,CAACjB,EAAE,CAAC,CAAC,CAAC;IAC/Eb,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,MAAMuE,oBAAoB,GAAGA,CAAA,KAAM;IACjCvE,kBAAkB,CAAC,EAAE,CAAC;EACxB,CAAC;EAED,oBACEhB,OAAA;IAAKwF,SAAS,EAAC,kCAAkC;IAAAC,QAAA,eAC/CzF,OAAA;MAAKwF,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE1DzF,OAAA;QAAKwF,SAAS,EAAC,MAAM;QAAAC,QAAA,eACnBzF,OAAA;UAAKwF,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDzF,OAAA;YAAAyF,QAAA,gBACEzF,OAAA;cAAIwF,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAAY;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACtE7F,OAAA;cAAGwF,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAC;YAExC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACN7F,OAAA,CAACX,MAAM;YACLyG,OAAO,EAAEA,CAAA,KAAM5E,oBAAoB,CAAC,IAAI,CAAE;YAC1C6E,QAAQ,EAAC,UAAU;YACnBC,YAAY,EAAC,MAAM;YAAAP,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7F,OAAA;QAAKwF,SAAS,EAAC,iEAAiE;QAAAC,QAAA,eAC9EzF,OAAA;UAAKwF,SAAS,EAAC,qFAAqF;UAAAC,QAAA,gBAClGzF,OAAA;YAAKwF,SAAS,EAAC,+EAA+E;YAAAC,QAAA,gBAC5FzF,OAAA;cAAKwF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,eAC7BzF,OAAA,CAACV,KAAK;gBACJ2G,IAAI,EAAC,QAAQ;gBACbC,WAAW,EAAC,oCAAoC;gBAChD1D,KAAK,EAAErC,WAAY;gBACnBgG,QAAQ,EAAGC,CAAC,IAAKhG,cAAc,CAACgG,CAAC,CAACC,MAAM,CAAC7D,KAAK,CAAE;gBAChDgD,SAAS,EAAC;cAAQ;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BzF,OAAA,CAACT,MAAM;gBACL2G,WAAW,EAAC,gBAAgB;gBAC5BI,OAAO,EAAE/D,WAAY;gBACrBC,KAAK,EAAEnC,UAAW;gBAClB8F,QAAQ,EAAE7F,aAAc;gBACxBkF,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC,eACF7F,OAAA,CAACT,MAAM;gBACL2G,WAAW,EAAC,kBAAkB;gBAC9BI,OAAO,EAAE5D,aAAc;gBACvBF,KAAK,EAAEjC,YAAa;gBACpB4F,QAAQ,EAAE3F,eAAgB;gBAC1BgF,SAAS,EAAC;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEN7F,OAAA;YAAKwF,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CzF,OAAA;cAAKwF,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDzF,OAAA,CAACX,MAAM;gBACLkH,OAAO,EAAE1F,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,OAAQ;gBACpD2F,IAAI,EAAC,IAAI;gBACTV,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,OAAO,CAAE;gBACpCiF,QAAQ,EAAC,OAAO;gBAChBP,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACF7F,OAAA,CAACX,MAAM;gBACLkH,OAAO,EAAE1F,QAAQ,KAAK,OAAO,GAAG,SAAS,GAAG,OAAQ;gBACpD2F,IAAI,EAAC,IAAI;gBACTV,OAAO,EAAEA,CAAA,KAAMhF,WAAW,CAAC,OAAO,CAAE;gBACpCiF,QAAQ,EAAC,SAAS;gBAClBP,SAAS,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACN7F,OAAA;cAAKwF,SAAS,EAAC,6BAA6B;cAAAC,QAAA,GACzC9C,wBAAwB,CAACoB,MAAM,EAAC,MAAI,EAACpC,OAAO,CAACoC,MAAM,EAAC,UACvD;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN7F,OAAA,CAACF,cAAc;QACb2G,aAAa,EAAE1F,eAAe,CAACgD,MAAO;QACtC2C,gBAAgB,EAAErB,oBAAqB;QACvCsB,YAAY,EAAErB,gBAAiB;QAC/BsB,gBAAgB,EAAErB;MAAqB;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC,CAAC,EAGDhF,QAAQ,KAAK,OAAO,gBACnBb,OAAA,CAACR,WAAW;QACVmC,OAAO,EAAEgB,wBAAyB;QAClClC,UAAU,EAAEA,UAAW;QACvBoG,MAAM,EAAEpD,UAAW;QACnBqD,UAAU,EAAE/B,cAAe;QAC3BgC,cAAc,EAAE7B,kBAAmB;QACnC8B,cAAc,EAAE7B,kBAAmB;QACnCpE,eAAe,EAAEA,eAAgB;QACjCkG,cAAc,EAAEtD,kBAAmB;QACnCuD,WAAW,EAAEpD;MAAgB;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,gBAEF7F,OAAA;QAAKwF,SAAS,EAAC,qEAAqE;QAAAC,QAAA,EACjF9C,wBAAwB,CAACqB,GAAG,CAAElB,MAAM,iBACnC9C,OAAA,CAACP,UAAU;UAETqD,MAAM,EAAEA,MAAO;UACfgE,UAAU,EAAE/B,cAAe;UAC3BgC,cAAc,EAAE7B,kBAAmB;UACnC8B,cAAc,EAAE7B;QAAmB,GAJ9BrC,MAAM,CAACjB,EAAE;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAKf,CACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CACN,EAGAlD,wBAAwB,CAACoB,MAAM,KAAK,CAAC,iBACpC/D,OAAA;QAAKwF,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzF,OAAA,CAACZ,IAAI;UAAC0C,IAAI,EAAC,OAAO;UAAC0E,IAAI,EAAE,EAAG;UAAChB,SAAS,EAAC;QAAkC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC5E7F,OAAA;UAAIwF,SAAS,EAAC,4CAA4C;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAChF7F,OAAA;UAAGwF,SAAS,EAAC,0BAA0B;UAAAC,QAAA,EACpCtF,WAAW,IAAIE,UAAU,KAAK,KAAK,IAAIE,YAAY,KAAK,KAAK,GAAE,8CAA8C,GAAE;QAAgD;UAAAmF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/J,CAAC,EACF,CAAC1F,WAAW,IAAIE,UAAU,KAAK,KAAK,IAAIE,YAAY,KAAK,KAAK,iBAC9DP,OAAA,CAACX,MAAM;UACLyG,OAAO,EAAEA,CAAA,KAAM5E,oBAAoB,CAAC,IAAI,CAAE;UAC1C6E,QAAQ,EAAC,UAAU;UACnBC,YAAY,EAAC,MAAM;UAAAP,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN,eAGD7F,OAAA,CAACN,iBAAiB;QAChByH,MAAM,EAAElG,iBAAkB;QAC1BmG,OAAO,EAAEA,CAAA,KAAMlG,oBAAoB,CAAC,KAAK,CAAE;QAC3CmG,QAAQ,EAAEpD;MAAoB;QAAAyB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,eAEF7F,OAAA,CAACL,aAAa;QACZwH,MAAM,EAAEhG,mBAAoB;QAC5BiG,OAAO,EAAEA,CAAA,KAAMhG,sBAAsB,CAAC,KAAK,CAAE;QAC7C0B,MAAM,EAAErB,cAAe;QACvB6F,YAAY,EAAEtC;MAAiB;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC,eAEF7F,OAAA,CAACJ,mBAAmB;QAClBuH,MAAM,EAAE9F,mBAAoB;QAC5B+F,OAAO,EAAEA,CAAA,KAAM9F,sBAAsB,CAAC,KAAK,CAAE;QAC7CwB,MAAM,EAAErB;MAAe;QAAAiE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxB,CAAC,eAEF7F,OAAA,CAACH,iBAAiB;QAChBsH,MAAM,EAAE5F,iBAAkB;QAC1B6F,OAAO,EAAEA,CAAA,KAAM5F,oBAAoB,CAAC,KAAK,CAAE;QAC3CsB,MAAM,EAAErB,cAAe;QACvB8F,QAAQ,EAAEnC;MAAoB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3F,EAAA,CAzZID,WAAW;AAAAuH,EAAA,GAAXvH,WAAW;AA2ZjB,eAAeA,WAAW;AAAC,IAAAuH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}