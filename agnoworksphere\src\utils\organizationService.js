// src/utils/organizationService.js

// Placeholder data structure for demo/testing
const mockOrganizations = [
  { id: 1, name: 'Acme Corp', createdAt: new Date().toISOString() },
  { id: 2, name: 'Globex Inc', createdAt: new Date().toISOString() },
];

// Simulate API delay
const delay = (ms) => new Promise((res) => setTimeout(res, ms));

export const getOrganizations = async () => {
  await delay(500);
  return { data: mockOrganizations, error: null };
};

export const getOrganizationById = async (id) => {
  await delay(500);
  const org = mockOrganizations.find((org) => org.id === id);
  return { data: org || null, error: org ? null : 'Organization not found' };
};

export const createOrganization = async (orgData) => {
  await delay(500);
  const newOrg = {
    id: mockOrganizations.length + 1,
    ...orgData,
    createdAt: new Date().toISOString(),
  };
  mockOrganizations.push(newOrg);
  return { data: newOrg, error: null };
};

// Default export for backward compatibility
const organizationService = {
  getOrganizations,
  getOrganizationById,
  createOrganization
};

export default organizationService;
