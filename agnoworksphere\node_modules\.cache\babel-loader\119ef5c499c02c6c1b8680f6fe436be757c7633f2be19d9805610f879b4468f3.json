{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\register\\\\components\\\\RegistrationForm.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport { Checkbox } from '../../../components/ui/Checkbox';\nimport Icon from '../../../components/AppIcon';\nimport authService from '../../../utils/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst RegistrationForm = () => {\n  _s();\n  const navigate = useNavigate();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    organizationName: '',\n    organizationDomain: '',\n    role: 'owner',\n    agreeToTerms: false,\n    agreeToPrivacy: false\n  });\n  const [errors, setErrors] = useState({});\n  const [passwordStrength, setPasswordStrength] = useState(0);\n  const validateEmail = email => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n  const validatePassword = password => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    return strength;\n  };\n  const validateDomain = domain => {\n    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/;\n    return domainRegex.test(domain);\n  };\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Clear specific field error\n    if (errors[field]) {\n      setErrors(prev => ({\n        ...prev,\n        [field]: ''\n      }));\n    }\n\n    // Real-time password strength validation\n    if (field === 'password') {\n      setPasswordStrength(validatePassword(value));\n    }\n  };\n  const validateStep1 = () => {\n    const newErrors = {};\n    if (!formData.fullName.trim()) {\n      newErrors.fullName = 'Full name is required';\n    }\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email address is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters long';\n    }\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const validateStep2 = () => {\n    const newErrors = {};\n    if (!formData.organizationName.trim()) {\n      newErrors.organizationName = 'Organization name is required';\n    }\n    if (!formData.organizationDomain.trim()) {\n      newErrors.organizationDomain = 'Organization domain is required';\n    } else if (!validateDomain(formData.organizationDomain)) {\n      newErrors.organizationDomain = 'Please enter a valid domain (e.g., company.com)';\n    }\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the Terms of Service';\n    }\n    if (!formData.agreeToPrivacy) {\n      newErrors.agreeToPrivacy = 'You must agree to the Privacy Policy';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleNextStep = () => {\n    if (validateStep1()) {\n      setCurrentStep(2);\n    }\n  };\n  const handlePreviousStep = () => {\n    setCurrentStep(1);\n  };\n  const handleSubmit = async e => {\n    e.preventDefault();\n    if (!validateStep2()) {\n      return;\n    }\n    setIsLoading(true);\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      // Mock successful registration\n      console.log('Registration successful:', formData);\n\n      // Navigate to login with success message\n      navigate('/login', {\n        state: {\n          message: 'Account created successfully! Please check your email for verification instructions.',\n          type: 'success'\n        }\n      });\n    } catch (error) {\n      setErrors({\n        submit: 'Registration failed. Please try again.'\n      });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const getPasswordStrengthText = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1:\n        return 'Weak';\n      case 2:\n      case 3:\n        return 'Medium';\n      case 4:\n      case 5:\n        return 'Strong';\n      default:\n        return '';\n    }\n  };\n  const getPasswordStrengthColor = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1:\n        return 'bg-destructive';\n      case 2:\n      case 3:\n        return 'bg-warning';\n      case 4:\n      case 5:\n        return 'bg-success';\n      default:\n        return 'bg-muted';\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full max-w-md mx-auto\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-card rounded-lg shadow-enterprise border border-border p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-2xl font-semibold text-text-primary mb-2\",\n          children: \"Create Your Account\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-text-secondary\",\n          children: currentStep === 1 ? 'Enter your personal information to get started' : 'Set up your organization workspace'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-center mb-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center justify-center w-8 h-8 rounded-full border-2 ${currentStep >= 1 ? 'bg-primary border-primary text-primary-foreground' : 'border-border text-text-secondary'}`,\n            children: currentStep > 1 ? /*#__PURE__*/_jsxDEV(Icon, {\n              name: \"Check\",\n              size: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 34\n            }, this) : '1'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-12 h-0.5 ${currentStep > 1 ? 'bg-primary' : 'bg-border'}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center justify-center w-8 h-8 rounded-full border-2 ${currentStep >= 2 ? 'bg-primary border-primary text-primary-foreground' : 'border-border text-text-secondary'}`,\n            children: \"2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [currentStep === 1 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Full Name\",\n            type: \"text\",\n            placeholder: \"Enter your full name\",\n            value: formData.fullName,\n            onChange: e => handleInputChange('fullName', e.target.value),\n            error: errors.fullName,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Email Address\",\n            type: \"email\",\n            placeholder: \"Enter your email address\",\n            value: formData.email,\n            onChange: e => handleInputChange('email', e.target.value),\n            error: errors.email,\n            description: \"We'll use this email for account verification and notifications\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-2\",\n            children: [/*#__PURE__*/_jsxDEV(Input, {\n              label: \"Password\",\n              type: \"password\",\n              placeholder: \"Create a strong password\",\n              value: formData.password,\n              onChange: e => handleInputChange('password', e.target.value),\n              error: errors.password,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 17\n            }, this), formData.password && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"space-y-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between text-sm\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-text-secondary\",\n                  children: \"Password strength:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `font-medium ${passwordStrength <= 1 ? 'text-destructive' : passwordStrength <= 3 ? 'text-warning' : 'text-success'}`,\n                  children: getPasswordStrengthText()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full bg-muted rounded-full h-2\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`,\n                  style: {\n                    width: `${passwordStrength / 5 * 100}%`\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 264,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 263,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Confirm Password\",\n            type: \"password\",\n            placeholder: \"Confirm your password\",\n            value: formData.confirmPassword,\n            onChange: e => handleInputChange('confirmPassword', e.target.value),\n            error: errors.confirmPassword,\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"button\",\n            onClick: handleNextStep,\n            className: \"w-full\",\n            iconName: \"ArrowRight\",\n            iconPosition: \"right\",\n            children: \"Continue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 283,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true), currentStep === 2 && /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(Input, {\n            label: \"Organization Name\",\n            type: \"text\",\n            placeholder: \"Enter your organization name\",\n            value: formData.organizationName,\n            onChange: e => handleInputChange('organizationName', e.target.value),\n            error: errors.organizationName,\n            description: \"This will be the name of your workspace\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            label: \"Organization Domain\",\n            type: \"text\",\n            placeholder: \"company.com\",\n            value: formData.organizationDomain,\n            onChange: e => handleInputChange('organizationDomain', e.target.value),\n            error: errors.organizationDomain,\n            description: \"Used for email invitations and domain restrictions\",\n            required: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n              label: \"I agree to the Terms of Service\",\n              checked: formData.agreeToTerms,\n              onChange: e => handleInputChange('agreeToTerms', e.target.checked),\n              error: errors.agreeToTerms,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n              label: \"I agree to the Privacy Policy\",\n              checked: formData.agreeToPrivacy,\n              onChange: e => handleInputChange('agreeToPrivacy', e.target.checked),\n              error: errors.agreeToPrivacy,\n              required: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 330,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this), errors.submit && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 bg-destructive/10 border border-destructive/20 rounded-md\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-destructive\",\n              children: errors.submit\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 341,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 340,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex space-x-3\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"button\",\n              variant: \"outline\",\n              onClick: handlePreviousStep,\n              className: \"flex-1\",\n              iconName: \"ArrowLeft\",\n              iconPosition: \"left\",\n              children: \"Back\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"submit\",\n              loading: isLoading,\n              className: \"flex-1\",\n              iconName: \"UserPlus\",\n              iconPosition: \"left\",\n              children: \"Create Account\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-8 pt-6 border-t border-border text-center\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-text-secondary\",\n          children: [\"Already have an account?\", ' ', /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/login\",\n            className: \"font-medium text-primary hover:text-primary/80 transition-colors\",\n            children: \"Sign in here\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 376,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 374,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 text-center\",\n      children: /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-xs text-text-secondary\",\n        children: [\"By creating an account, you'll be able to invite team members and manage projects.\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this), \"Need to join an existing organization? Contact your administrator for an invitation.\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n_s(RegistrationForm, \"b/Cy1R4jGx6JpDOjetrLO9DZsZs=\", false, function () {\n  return [useNavigate];\n});\n_c = RegistrationForm;\nexport default RegistrationForm;\nvar _c;\n$RefreshReg$(_c, \"RegistrationForm\");", "map": {"version": 3, "names": ["React", "useState", "Link", "useNavigate", "<PERSON><PERSON>", "Input", "Checkbox", "Icon", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "RegistrationForm", "_s", "navigate", "currentStep", "setCurrentStep", "isLoading", "setIsLoading", "formData", "setFormData", "fullName", "email", "password", "confirmPassword", "organizationName", "organizationDomain", "role", "agreeToTerms", "agreeToPrivacy", "errors", "setErrors", "passwordStrength", "setPasswordStrength", "validateEmail", "emailRegex", "test", "validatePassword", "strength", "length", "validateDomain", "domain", "domainRegex", "handleInputChange", "field", "value", "prev", "validateStep1", "newErrors", "trim", "Object", "keys", "validateStep2", "handleNextStep", "handlePreviousStep", "handleSubmit", "e", "preventDefault", "Promise", "resolve", "setTimeout", "console", "log", "state", "message", "type", "error", "submit", "getPasswordStrengthText", "getPasswordStrengthColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "size", "onSubmit", "label", "placeholder", "onChange", "target", "required", "description", "style", "width", "onClick", "iconName", "iconPosition", "checked", "variant", "loading", "to", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/register/components/RegistrationForm.jsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Link, useNavigate } from 'react-router-dom';\nimport Button from '../../../components/ui/Button';\nimport Input from '../../../components/ui/Input';\nimport { Checkbox } from '../../../components/ui/Checkbox';\nimport Icon from '../../../components/AppIcon';\nimport authService from '../../../utils/authService';\n\nconst RegistrationForm = () => {\n  const navigate = useNavigate();\n  const [currentStep, setCurrentStep] = useState(1);\n  const [isLoading, setIsLoading] = useState(false);\n  const [formData, setFormData] = useState({\n    fullName: '',\n    email: '',\n    password: '',\n    confirmPassword: '',\n    organizationName: '',\n    organizationDomain: '',\n    role: 'owner',\n    agreeToTerms: false,\n    agreeToPrivacy: false\n  });\n  const [errors, setErrors] = useState({});\n  const [passwordStrength, setPasswordStrength] = useState(0);\n\n  const validateEmail = (email) => {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n  };\n\n  const validatePassword = (password) => {\n    let strength = 0;\n    if (password.length >= 8) strength++;\n    if (/[A-Z]/.test(password)) strength++;\n    if (/[a-z]/.test(password)) strength++;\n    if (/[0-9]/.test(password)) strength++;\n    if (/[^A-Za-z0-9]/.test(password)) strength++;\n    return strength;\n  };\n\n  const validateDomain = (domain) => {\n    const domainRegex = /^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9](?:\\.[a-zA-Z]{2,})+$/;\n    return domainRegex.test(domain);\n  };\n\n  const handleInputChange = (field, value) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    \n    // Clear specific field error\n    if (errors[field]) {\n      setErrors(prev => ({ ...prev, [field]: '' }));\n    }\n\n    // Real-time password strength validation\n    if (field === 'password') {\n      setPasswordStrength(validatePassword(value));\n    }\n  };\n\n  const validateStep1 = () => {\n    const newErrors = {};\n\n    if (!formData.fullName.trim()) {\n      newErrors.fullName = 'Full name is required';\n    }\n\n    if (!formData.email.trim()) {\n      newErrors.email = 'Email address is required';\n    } else if (!validateEmail(formData.email)) {\n      newErrors.email = 'Please enter a valid email address';\n    }\n\n    if (!formData.password) {\n      newErrors.password = 'Password is required';\n    } else if (formData.password.length < 8) {\n      newErrors.password = 'Password must be at least 8 characters long';\n    }\n\n    if (!formData.confirmPassword) {\n      newErrors.confirmPassword = 'Please confirm your password';\n    } else if (formData.password !== formData.confirmPassword) {\n      newErrors.confirmPassword = 'Passwords do not match';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const validateStep2 = () => {\n    const newErrors = {};\n\n    if (!formData.organizationName.trim()) {\n      newErrors.organizationName = 'Organization name is required';\n    }\n\n    if (!formData.organizationDomain.trim()) {\n      newErrors.organizationDomain = 'Organization domain is required';\n    } else if (!validateDomain(formData.organizationDomain)) {\n      newErrors.organizationDomain = 'Please enter a valid domain (e.g., company.com)';\n    }\n\n    if (!formData.agreeToTerms) {\n      newErrors.agreeToTerms = 'You must agree to the Terms of Service';\n    }\n\n    if (!formData.agreeToPrivacy) {\n      newErrors.agreeToPrivacy = 'You must agree to the Privacy Policy';\n    }\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleNextStep = () => {\n    if (validateStep1()) {\n      setCurrentStep(2);\n    }\n  };\n\n  const handlePreviousStep = () => {\n    setCurrentStep(1);\n  };\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n    \n    if (!validateStep2()) {\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // Simulate API call\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Mock successful registration\n      console.log('Registration successful:', formData);\n      \n      // Navigate to login with success message\n      navigate('/login', { \n        state: { \n          message: 'Account created successfully! Please check your email for verification instructions.',\n          type: 'success'\n        }\n      });\n    } catch (error) {\n      setErrors({ submit: 'Registration failed. Please try again.' });\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getPasswordStrengthText = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1: return 'Weak';\n      case 2:\n      case 3: return 'Medium';\n      case 4:\n      case 5: return 'Strong';\n      default: return '';\n    }\n  };\n\n  const getPasswordStrengthColor = () => {\n    switch (passwordStrength) {\n      case 0:\n      case 1: return 'bg-destructive';\n      case 2:\n      case 3: return 'bg-warning';\n      case 4:\n      case 5: return 'bg-success';\n      default: return 'bg-muted';\n    }\n  };\n\n  return (\n    <div className=\"w-full max-w-md mx-auto\">\n      <div className=\"bg-card rounded-lg shadow-enterprise border border-border p-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-2xl font-semibold text-text-primary mb-2\">\n            Create Your Account\n          </h1>\n          <p className=\"text-text-secondary\">\n            {currentStep === 1 \n              ? 'Enter your personal information to get started'\n              : 'Set up your organization workspace'\n            }\n          </p>\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"flex items-center justify-center mb-8\">\n          <div className=\"flex items-center space-x-4\">\n            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n              currentStep >= 1 \n                ? 'bg-primary border-primary text-primary-foreground' \n                : 'border-border text-text-secondary'\n            }`}>\n              {currentStep > 1 ? <Icon name=\"Check\" size={16} /> : '1'}\n            </div>\n            <div className={`w-12 h-0.5 ${currentStep > 1 ? 'bg-primary' : 'bg-border'}`} />\n            <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${\n              currentStep >= 2 \n                ? 'bg-primary border-primary text-primary-foreground' \n                : 'border-border text-text-secondary'\n            }`}>\n              2\n            </div>\n          </div>\n        </div>\n\n        <form onSubmit={handleSubmit} className=\"space-y-6\">\n          {currentStep === 1 && (\n            <>\n              {/* Step 1: Personal Information */}\n              <Input\n                label=\"Full Name\"\n                type=\"text\"\n                placeholder=\"Enter your full name\"\n                value={formData.fullName}\n                onChange={(e) => handleInputChange('fullName', e.target.value)}\n                error={errors.fullName}\n                required\n              />\n\n              <Input\n                label=\"Email Address\"\n                type=\"email\"\n                placeholder=\"Enter your email address\"\n                value={formData.email}\n                onChange={(e) => handleInputChange('email', e.target.value)}\n                error={errors.email}\n                description=\"We'll use this email for account verification and notifications\"\n                required\n              />\n\n              <div className=\"space-y-2\">\n                <Input\n                  label=\"Password\"\n                  type=\"password\"\n                  placeholder=\"Create a strong password\"\n                  value={formData.password}\n                  onChange={(e) => handleInputChange('password', e.target.value)}\n                  error={errors.password}\n                  required\n                />\n                \n                {formData.password && (\n                  <div className=\"space-y-2\">\n                    <div className=\"flex items-center justify-between text-sm\">\n                      <span className=\"text-text-secondary\">Password strength:</span>\n                      <span className={`font-medium ${\n                        passwordStrength <= 1 ? 'text-destructive' :\n                        passwordStrength <= 3 ? 'text-warning' : 'text-success'\n                      }`}>\n                        {getPasswordStrengthText()}\n                      </span>\n                    </div>\n                    <div className=\"w-full bg-muted rounded-full h-2\">\n                      <div \n                        className={`h-2 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}\n                        style={{ width: `${(passwordStrength / 5) * 100}%` }}\n                      />\n                    </div>\n                  </div>\n                )}\n              </div>\n\n              <Input\n                label=\"Confirm Password\"\n                type=\"password\"\n                placeholder=\"Confirm your password\"\n                value={formData.confirmPassword}\n                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}\n                error={errors.confirmPassword}\n                required\n              />\n\n              <Button\n                type=\"button\"\n                onClick={handleNextStep}\n                className=\"w-full\"\n                iconName=\"ArrowRight\"\n                iconPosition=\"right\"\n              >\n                Continue\n              </Button>\n            </>\n          )}\n\n          {currentStep === 2 && (\n            <>\n              {/* Step 2: Organization Setup */}\n              <Input\n                label=\"Organization Name\"\n                type=\"text\"\n                placeholder=\"Enter your organization name\"\n                value={formData.organizationName}\n                onChange={(e) => handleInputChange('organizationName', e.target.value)}\n                error={errors.organizationName}\n                description=\"This will be the name of your workspace\"\n                required\n              />\n\n              <Input\n                label=\"Organization Domain\"\n                type=\"text\"\n                placeholder=\"company.com\"\n                value={formData.organizationDomain}\n                onChange={(e) => handleInputChange('organizationDomain', e.target.value)}\n                error={errors.organizationDomain}\n                description=\"Used for email invitations and domain restrictions\"\n                required\n              />\n\n              {/* Terms and Privacy */}\n              <div className=\"space-y-4\">\n                <Checkbox\n                  label=\"I agree to the Terms of Service\"\n                  checked={formData.agreeToTerms}\n                  onChange={(e) => handleInputChange('agreeToTerms', e.target.checked)}\n                  error={errors.agreeToTerms}\n                  required\n                />\n\n                <Checkbox\n                  label=\"I agree to the Privacy Policy\"\n                  checked={formData.agreeToPrivacy}\n                  onChange={(e) => handleInputChange('agreeToPrivacy', e.target.checked)}\n                  error={errors.agreeToPrivacy}\n                  required\n                />\n              </div>\n\n              {errors.submit && (\n                <div className=\"p-3 bg-destructive/10 border border-destructive/20 rounded-md\">\n                  <p className=\"text-sm text-destructive\">{errors.submit}</p>\n                </div>\n              )}\n\n              {/* Action Buttons */}\n              <div className=\"flex space-x-3\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={handlePreviousStep}\n                  className=\"flex-1\"\n                  iconName=\"ArrowLeft\"\n                  iconPosition=\"left\"\n                >\n                  Back\n                </Button>\n                \n                <Button\n                  type=\"submit\"\n                  loading={isLoading}\n                  className=\"flex-1\"\n                  iconName=\"UserPlus\"\n                  iconPosition=\"left\"\n                >\n                  Create Account\n                </Button>\n              </div>\n            </>\n          )}\n        </form>\n\n        {/* Footer */}\n        <div className=\"mt-8 pt-6 border-t border-border text-center\">\n          <p className=\"text-sm text-text-secondary\">\n            Already have an account?{' '}\n            <Link \n              to=\"/login\" \n              className=\"font-medium text-primary hover:text-primary/80 transition-colors\"\n            >\n              Sign in here\n            </Link>\n          </p>\n        </div>\n      </div>\n\n      {/* Additional Information */}\n      <div className=\"mt-6 text-center\">\n        <p className=\"text-xs text-text-secondary\">\n          By creating an account, you'll be able to invite team members and manage projects.\n          <br />\n          Need to join an existing organization? Contact your administrator for an invitation.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default RegistrationForm;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,KAAK,MAAM,8BAA8B;AAChD,SAASC,QAAQ,QAAQ,iCAAiC;AAC1D,OAAOC,IAAI,MAAM,6BAA6B;AAC9C,OAAOC,WAAW,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErD,MAAMC,gBAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACa,WAAW,EAAEC,cAAc,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACiB,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAAC;IACvCqB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE,EAAE;IACTC,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE,EAAE;IACnBC,gBAAgB,EAAE,EAAE;IACpBC,kBAAkB,EAAE,EAAE;IACtBC,IAAI,EAAE,OAAO;IACbC,YAAY,EAAE,KAAK;IACnBC,cAAc,EAAE;EAClB,CAAC,CAAC;EACF,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAG/B,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxC,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EAE3D,MAAMkC,aAAa,GAAIZ,KAAK,IAAK;IAC/B,MAAMa,UAAU,GAAG,4BAA4B;IAC/C,OAAOA,UAAU,CAACC,IAAI,CAACd,KAAK,CAAC;EAC/B,CAAC;EAED,MAAMe,gBAAgB,GAAId,QAAQ,IAAK;IACrC,IAAIe,QAAQ,GAAG,CAAC;IAChB,IAAIf,QAAQ,CAACgB,MAAM,IAAI,CAAC,EAAED,QAAQ,EAAE;IACpC,IAAI,OAAO,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IACtC,IAAI,OAAO,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IACtC,IAAI,cAAc,CAACF,IAAI,CAACb,QAAQ,CAAC,EAAEe,QAAQ,EAAE;IAC7C,OAAOA,QAAQ;EACjB,CAAC;EAED,MAAME,cAAc,GAAIC,MAAM,IAAK;IACjC,MAAMC,WAAW,GAAG,+DAA+D;IACnF,OAAOA,WAAW,CAACN,IAAI,CAACK,MAAM,CAAC;EACjC,CAAC;EAED,MAAME,iBAAiB,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IAC1CzB,WAAW,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACF,KAAK,GAAGC;IAAM,CAAC,CAAC,CAAC;;IAElD;IACA,IAAIf,MAAM,CAACc,KAAK,CAAC,EAAE;MACjBb,SAAS,CAACe,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACF,KAAK,GAAG;MAAG,CAAC,CAAC,CAAC;IAC/C;;IAEA;IACA,IAAIA,KAAK,KAAK,UAAU,EAAE;MACxBX,mBAAmB,CAACI,gBAAgB,CAACQ,KAAK,CAAC,CAAC;IAC9C;EACF,CAAC;EAED,MAAME,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC7B,QAAQ,CAACE,QAAQ,CAAC4B,IAAI,CAAC,CAAC,EAAE;MAC7BD,SAAS,CAAC3B,QAAQ,GAAG,uBAAuB;IAC9C;IAEA,IAAI,CAACF,QAAQ,CAACG,KAAK,CAAC2B,IAAI,CAAC,CAAC,EAAE;MAC1BD,SAAS,CAAC1B,KAAK,GAAG,2BAA2B;IAC/C,CAAC,MAAM,IAAI,CAACY,aAAa,CAACf,QAAQ,CAACG,KAAK,CAAC,EAAE;MACzC0B,SAAS,CAAC1B,KAAK,GAAG,oCAAoC;IACxD;IAEA,IAAI,CAACH,QAAQ,CAACI,QAAQ,EAAE;MACtByB,SAAS,CAACzB,QAAQ,GAAG,sBAAsB;IAC7C,CAAC,MAAM,IAAIJ,QAAQ,CAACI,QAAQ,CAACgB,MAAM,GAAG,CAAC,EAAE;MACvCS,SAAS,CAACzB,QAAQ,GAAG,6CAA6C;IACpE;IAEA,IAAI,CAACJ,QAAQ,CAACK,eAAe,EAAE;MAC7BwB,SAAS,CAACxB,eAAe,GAAG,8BAA8B;IAC5D,CAAC,MAAM,IAAIL,QAAQ,CAACI,QAAQ,KAAKJ,QAAQ,CAACK,eAAe,EAAE;MACzDwB,SAAS,CAACxB,eAAe,GAAG,wBAAwB;IACtD;IAEAO,SAAS,CAACiB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACT,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMa,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMJ,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAAC7B,QAAQ,CAACM,gBAAgB,CAACwB,IAAI,CAAC,CAAC,EAAE;MACrCD,SAAS,CAACvB,gBAAgB,GAAG,+BAA+B;IAC9D;IAEA,IAAI,CAACN,QAAQ,CAACO,kBAAkB,CAACuB,IAAI,CAAC,CAAC,EAAE;MACvCD,SAAS,CAACtB,kBAAkB,GAAG,iCAAiC;IAClE,CAAC,MAAM,IAAI,CAACc,cAAc,CAACrB,QAAQ,CAACO,kBAAkB,CAAC,EAAE;MACvDsB,SAAS,CAACtB,kBAAkB,GAAG,iDAAiD;IAClF;IAEA,IAAI,CAACP,QAAQ,CAACS,YAAY,EAAE;MAC1BoB,SAAS,CAACpB,YAAY,GAAG,wCAAwC;IACnE;IAEA,IAAI,CAACT,QAAQ,CAACU,cAAc,EAAE;MAC5BmB,SAAS,CAACnB,cAAc,GAAG,sCAAsC;IACnE;IAEAE,SAAS,CAACiB,SAAS,CAAC;IACpB,OAAOE,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC,CAACT,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMc,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAIN,aAAa,CAAC,CAAC,EAAE;MACnB/B,cAAc,CAAC,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtC,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMuC,YAAY,GAAG,MAAOC,CAAC,IAAK;IAChCA,CAAC,CAACC,cAAc,CAAC,CAAC;IAElB,IAAI,CAACL,aAAa,CAAC,CAAC,EAAE;MACpB;IACF;IAEAlC,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,MAAM,IAAIwC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACAE,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE3C,QAAQ,CAAC;;MAEjD;MACAL,QAAQ,CAAC,QAAQ,EAAE;QACjBiD,KAAK,EAAE;UACLC,OAAO,EAAE,sFAAsF;UAC/FC,IAAI,EAAE;QACR;MACF,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdnC,SAAS,CAAC;QAAEoC,MAAM,EAAE;MAAyC,CAAC,CAAC;IACjE,CAAC,SAAS;MACRjD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMkD,uBAAuB,GAAGA,CAAA,KAAM;IACpC,QAAQpC,gBAAgB;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,MAAM;MACrB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,QAAQ;MACvB;QAAS,OAAO,EAAE;IACpB;EACF,CAAC;EAED,MAAMqC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,QAAQrC,gBAAgB;MACtB,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,gBAAgB;MAC/B,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,YAAY;MAC3B,KAAK,CAAC;MACN,KAAK,CAAC;QAAE,OAAO,YAAY;MAC3B;QAAS,OAAO,UAAU;IAC5B;EACF,CAAC;EAED,oBACEvB,OAAA;IAAK6D,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBACtC9D,OAAA;MAAK6D,SAAS,EAAC,+DAA+D;MAAAC,QAAA,gBAE5E9D,OAAA;QAAK6D,SAAS,EAAC,kBAAkB;QAAAC,QAAA,gBAC/B9D,OAAA;UAAI6D,SAAS,EAAC,+CAA+C;UAAAC,QAAA,EAAC;QAE9D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLlE,OAAA;UAAG6D,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAC/BxD,WAAW,KAAK,CAAC,GACd,gDAAgD,GAChD;QAAoC;UAAAyD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEvC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNlE,OAAA;QAAK6D,SAAS,EAAC,uCAAuC;QAAAC,QAAA,eACpD9D,OAAA;UAAK6D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1C9D,OAAA;YAAK6D,SAAS,EAAE,kEACdvD,WAAW,IAAI,CAAC,GACZ,mDAAmD,GACnD,mCAAmC,EACtC;YAAAwD,QAAA,EACAxD,WAAW,GAAG,CAAC,gBAAGN,OAAA,CAACH,IAAI;cAACsE,IAAI,EAAC,OAAO;cAACC,IAAI,EAAE;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,GAAG;UAAG;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAE,cAAcvD,WAAW,GAAG,CAAC,GAAG,YAAY,GAAG,WAAW;UAAG;YAAAyD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChFlE,OAAA;YAAK6D,SAAS,EAAE,kEACdvD,WAAW,IAAI,CAAC,GACZ,mDAAmD,GACnD,mCAAmC,EACtC;YAAAwD,QAAA,EAAC;UAEJ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENlE,OAAA;QAAMqE,QAAQ,EAAEvB,YAAa;QAACe,SAAS,EAAC,WAAW;QAAAC,QAAA,GAChDxD,WAAW,KAAK,CAAC,iBAChBN,OAAA,CAAAE,SAAA;UAAA4D,QAAA,gBAEE9D,OAAA,CAACL,KAAK;YACJ2E,KAAK,EAAC,WAAW;YACjBd,IAAI,EAAC,MAAM;YACXe,WAAW,EAAC,sBAAsB;YAClCnC,KAAK,EAAE1B,QAAQ,CAACE,QAAS;YACzB4D,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,UAAU,EAAEa,CAAC,CAAC0B,MAAM,CAACrC,KAAK,CAAE;YAC/DqB,KAAK,EAAEpC,MAAM,CAACT,QAAS;YACvB8D,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFlE,OAAA,CAACL,KAAK;YACJ2E,KAAK,EAAC,eAAe;YACrBd,IAAI,EAAC,OAAO;YACZe,WAAW,EAAC,0BAA0B;YACtCnC,KAAK,EAAE1B,QAAQ,CAACG,KAAM;YACtB2D,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,OAAO,EAAEa,CAAC,CAAC0B,MAAM,CAACrC,KAAK,CAAE;YAC5DqB,KAAK,EAAEpC,MAAM,CAACR,KAAM;YACpB8D,WAAW,EAAC,iEAAiE;YAC7ED,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFlE,OAAA;YAAK6D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9D,OAAA,CAACL,KAAK;cACJ2E,KAAK,EAAC,UAAU;cAChBd,IAAI,EAAC,UAAU;cACfe,WAAW,EAAC,0BAA0B;cACtCnC,KAAK,EAAE1B,QAAQ,CAACI,QAAS;cACzB0D,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,UAAU,EAAEa,CAAC,CAAC0B,MAAM,CAACrC,KAAK,CAAE;cAC/DqB,KAAK,EAAEpC,MAAM,CAACP,QAAS;cACvB4D,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,EAEDxD,QAAQ,CAACI,QAAQ,iBAChBd,OAAA;cAAK6D,SAAS,EAAC,WAAW;cAAAC,QAAA,gBACxB9D,OAAA;gBAAK6D,SAAS,EAAC,2CAA2C;gBAAAC,QAAA,gBACxD9D,OAAA;kBAAM6D,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAkB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC/DlE,OAAA;kBAAM6D,SAAS,EAAE,eACftC,gBAAgB,IAAI,CAAC,GAAG,kBAAkB,GAC1CA,gBAAgB,IAAI,CAAC,GAAG,cAAc,GAAG,cAAc,EACtD;kBAAAuC,QAAA,EACAH,uBAAuB,CAAC;gBAAC;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNlE,OAAA;gBAAK6D,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,eAC/C9D,OAAA;kBACE6D,SAAS,EAAE,gDAAgDD,wBAAwB,CAAC,CAAC,EAAG;kBACxFgB,KAAK,EAAE;oBAAEC,KAAK,EAAE,GAAItD,gBAAgB,GAAG,CAAC,GAAI,GAAG;kBAAI;gBAAE;kBAAAwC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAENlE,OAAA,CAACL,KAAK;YACJ2E,KAAK,EAAC,kBAAkB;YACxBd,IAAI,EAAC,UAAU;YACfe,WAAW,EAAC,uBAAuB;YACnCnC,KAAK,EAAE1B,QAAQ,CAACK,eAAgB;YAChCyD,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,iBAAiB,EAAEa,CAAC,CAAC0B,MAAM,CAACrC,KAAK,CAAE;YACtEqB,KAAK,EAAEpC,MAAM,CAACN,eAAgB;YAC9B2D,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFlE,OAAA,CAACN,MAAM;YACL8D,IAAI,EAAC,QAAQ;YACbsB,OAAO,EAAElC,cAAe;YACxBiB,SAAS,EAAC,QAAQ;YAClBkB,QAAQ,EAAC,YAAY;YACrBC,YAAY,EAAC,OAAO;YAAAlB,QAAA,EACrB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA,eACT,CACH,EAEA5D,WAAW,KAAK,CAAC,iBAChBN,OAAA,CAAAE,SAAA;UAAA4D,QAAA,gBAEE9D,OAAA,CAACL,KAAK;YACJ2E,KAAK,EAAC,mBAAmB;YACzBd,IAAI,EAAC,MAAM;YACXe,WAAW,EAAC,8BAA8B;YAC1CnC,KAAK,EAAE1B,QAAQ,CAACM,gBAAiB;YACjCwD,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,kBAAkB,EAAEa,CAAC,CAAC0B,MAAM,CAACrC,KAAK,CAAE;YACvEqB,KAAK,EAAEpC,MAAM,CAACL,gBAAiB;YAC/B2D,WAAW,EAAC,yCAAyC;YACrDD,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAEFlE,OAAA,CAACL,KAAK;YACJ2E,KAAK,EAAC,qBAAqB;YAC3Bd,IAAI,EAAC,MAAM;YACXe,WAAW,EAAC,aAAa;YACzBnC,KAAK,EAAE1B,QAAQ,CAACO,kBAAmB;YACnCuD,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,oBAAoB,EAAEa,CAAC,CAAC0B,MAAM,CAACrC,KAAK,CAAE;YACzEqB,KAAK,EAAEpC,MAAM,CAACJ,kBAAmB;YACjC0D,WAAW,EAAC,oDAAoD;YAChED,QAAQ;UAAA;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eAGFlE,OAAA;YAAK6D,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxB9D,OAAA,CAACJ,QAAQ;cACP0E,KAAK,EAAC,iCAAiC;cACvCW,OAAO,EAAEvE,QAAQ,CAACS,YAAa;cAC/BqD,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,cAAc,EAAEa,CAAC,CAAC0B,MAAM,CAACQ,OAAO,CAAE;cACrExB,KAAK,EAAEpC,MAAM,CAACF,YAAa;cAC3BuD,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eAEFlE,OAAA,CAACJ,QAAQ;cACP0E,KAAK,EAAC,+BAA+B;cACrCW,OAAO,EAAEvE,QAAQ,CAACU,cAAe;cACjCoD,QAAQ,EAAGzB,CAAC,IAAKb,iBAAiB,CAAC,gBAAgB,EAAEa,CAAC,CAAC0B,MAAM,CAACQ,OAAO,CAAE;cACvExB,KAAK,EAAEpC,MAAM,CAACD,cAAe;cAC7BsD,QAAQ;YAAA;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAEL7C,MAAM,CAACqC,MAAM,iBACZ1D,OAAA;YAAK6D,SAAS,EAAC,+DAA+D;YAAAC,QAAA,eAC5E9D,OAAA;cAAG6D,SAAS,EAAC,0BAA0B;cAAAC,QAAA,EAAEzC,MAAM,CAACqC;YAAM;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CACN,eAGDlE,OAAA;YAAK6D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7B9D,OAAA,CAACN,MAAM;cACL8D,IAAI,EAAC,QAAQ;cACb0B,OAAO,EAAC,SAAS;cACjBJ,OAAO,EAAEjC,kBAAmB;cAC5BgB,SAAS,EAAC,QAAQ;cAClBkB,QAAQ,EAAC,WAAW;cACpBC,YAAY,EAAC,MAAM;cAAAlB,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETlE,OAAA,CAACN,MAAM;cACL8D,IAAI,EAAC,QAAQ;cACb2B,OAAO,EAAE3E,SAAU;cACnBqD,SAAS,EAAC,QAAQ;cAClBkB,QAAQ,EAAC,UAAU;cACnBC,YAAY,EAAC,MAAM;cAAAlB,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA,eACN,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAGPlE,OAAA;QAAK6D,SAAS,EAAC,8CAA8C;QAAAC,QAAA,eAC3D9D,OAAA;UAAG6D,SAAS,EAAC,6BAA6B;UAAAC,QAAA,GAAC,0BACjB,EAAC,GAAG,eAC5B9D,OAAA,CAACR,IAAI;YACH4F,EAAE,EAAC,QAAQ;YACXvB,SAAS,EAAC,kEAAkE;YAAAC,QAAA,EAC7E;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B9D,OAAA;QAAG6D,SAAS,EAAC,6BAA6B;QAAAC,QAAA,GAAC,oFAEzC,eAAA9D,OAAA;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,wFAER;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAnYID,gBAAgB;EAAA,QACHV,WAAW;AAAA;AAAA4F,EAAA,GADxBlF,gBAAgB;AAqYtB,eAAeA,gBAAgB;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}