{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\index.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\nimport CreateProjectModal from '../../components/modals/CreateProjectModal';\nimport InviteMemberModal from '../../components/modals/InviteMemberModal';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RoleBasedDashboard = () => {\n  _s();\n  var _organizations$0$orga, _currentUser$email, _organizations$0$orga2;\n  const location = useLocation();\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n  const [showCreateProject, setShowCreateProject] = useState(false);\n  const [showInviteMember, setShowInviteMember] = useState(false);\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        var _location$state, _location$state2;\n        setLoading(true);\n\n        // Check for welcome message from navigation state\n        if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message && ((_location$state2 = location.state) === null || _location$state2 === void 0 ? void 0 : _location$state2.type) === 'success') {\n          setWelcomeMessage(location.state.message);\n          setShowWelcome(true);\n          // Clear the state to prevent showing on refresh\n          window.history.replaceState({}, document.title);\n        }\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n        }\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    loadDashboardData();\n  }, [location.state]);\n\n  // Project creation handler\n  const handleCreateProject = async projectData => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n      const newProject = await apiService.projects.create(organizationId, projectData);\n\n      // Refresh projects list\n      const projectsResult = await apiService.projects.getAll(organizationId);\n      setProjects(projectsResult || []);\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n      console.log('Project created successfully:', newProject);\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n\n  // Quick action handlers\n  const handleOpenCreateProject = () => {\n    setShowCreateProject(true);\n  };\n  const handleCloseCreateProject = () => {\n    setShowCreateProject(false);\n  };\n  const handleManageUsers = () => {\n    // Navigate to team members page\n    window.location.href = '/team-members';\n  };\n  const handleInviteMembers = () => {\n    // Navigate to team members page with invite mode\n    window.location.href = '/team-members?action=invite';\n  };\n\n  // Show loading state\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600\",\n          children: \"Loading dashboard...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 132,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 129,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Show error state\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-8 h-8 text-red-600\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-red-600 text-lg mb-2\",\n          children: \"Failed to load dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-slate-600 text-sm\",\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => window.location.reload(),\n          className: \"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this);\n  }\n\n  // Mock data for different dashboard components (fallback)\n  const mockProjects = [{\n    id: 1,\n    name: \"E-commerce Platform Redesign\",\n    description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n    status: \"Active\",\n    priority: \"High\",\n    progress: 75,\n    dueDate: \"Dec 15, 2025\",\n    team: [{\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    }, {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    }, {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    }, {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    }]\n  }, {\n    id: 2,\n    name: \"Mobile App Development\",\n    description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n    status: \"Active\",\n    priority: \"Medium\",\n    progress: 45,\n    dueDate: \"Jan 30, 2026\",\n    team: [{\n      name: \"David Kim\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\"\n    }, {\n      name: \"Lisa Wang\",\n      avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\"\n    }, {\n      name: \"Tom Wilson\",\n      avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\"\n    }]\n  }, {\n    id: 3,\n    name: \"Data Analytics Dashboard\",\n    description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n    status: \"Completed\",\n    priority: \"Low\",\n    progress: 100,\n    dueDate: \"Nov 20, 2025\",\n    team: [{\n      name: \"Rachel Green\",\n      avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\"\n    }, {\n      name: \"James Brown\",\n      avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\"\n    }]\n  }];\n  const mockActivities = [{\n    id: 1,\n    type: \"task_completed\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    description: \"Completed the user interface mockups for the checkout process\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 15 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 2,\n    type: \"comment_added\",\n    user: {\n      name: \"Mike Chen\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\"\n    },\n    description: \"Added feedback on the mobile responsive design implementation\",\n    project: \"E-commerce Platform\",\n    timestamp: new Date(Date.now() - 45 * 60 * 1000),\n    isPublic: true\n  }, {\n    id: 3,\n    type: \"project_created\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    description: \"Created new project for Q1 marketing campaign automation\",\n    project: \"Marketing Automation\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    isPublic: false\n  }, {\n    id: 4,\n    type: \"member_added\",\n    user: {\n      name: \"Alex Rodriguez\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\"\n    },\n    description: \"Joined the mobile app development team as a senior developer\",\n    project: \"Mobile App Development\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    isPublic: true\n  }];\n  const mockTasks = [{\n    id: 1,\n    title: \"Implement payment gateway integration\",\n    status: \"In Progress\",\n    priority: \"High\",\n    dueDate: \"Dec 10, 2025\",\n    assignee: \"Sarah Johnson\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 2,\n    title: \"Design user onboarding flow\",\n    status: \"To Do\",\n    priority: \"Medium\",\n    dueDate: \"Dec 12, 2025\",\n    assignee: \"Mike Chen\",\n    project: \"Mobile App\"\n  }, {\n    id: 3,\n    title: \"Set up automated testing pipeline\",\n    status: \"Review\",\n    priority: \"High\",\n    dueDate: \"Dec 8, 2025\",\n    assignee: \"Emily Davis\",\n    project: \"E-commerce Platform\"\n  }, {\n    id: 4,\n    title: \"Create API documentation\",\n    status: \"Done\",\n    priority: \"Low\",\n    dueDate: \"Dec 5, 2025\",\n    assignee: \"Alex Rodriguez\",\n    project: \"Data Analytics\"\n  }, {\n    id: 5,\n    title: \"Optimize database queries\",\n    status: \"Blocked\",\n    priority: \"High\",\n    dueDate: \"Dec 15, 2025\",\n    assignee: \"David Kim\",\n    project: \"E-commerce Platform\"\n  }];\n  const mockTeamMembers = [{\n    id: 1,\n    name: \"Sarah Johnson\",\n    email: \"<EMAIL>\",\n    role: \"Admin\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n    department: \"Design\",\n    currentTask: \"UI/UX Design Review\",\n    tasksCompleted: 24,\n    lastActive: new Date()\n  }, {\n    id: 2,\n    name: \"Mike Chen\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Away\",\n    avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n    department: \"Development\",\n    currentTask: \"Frontend Implementation\",\n    tasksCompleted: 18,\n    lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n  }, {\n    id: 3,\n    name: \"Emily Davis\",\n    email: \"<EMAIL>\",\n    role: \"Owner\",\n    status: \"Online\",\n    avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n    department: \"Management\",\n    currentTask: \"Project Planning\",\n    tasksCompleted: 32,\n    lastActive: new Date()\n  }, {\n    id: 4,\n    name: \"Alex Rodriguez\",\n    email: \"<EMAIL>\",\n    role: \"Member\",\n    status: \"Busy\",\n    avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n    department: \"Development\",\n    currentTask: \"API Integration\",\n    tasksCompleted: 15,\n    lastActive: new Date(Date.now() - 30 * 60 * 1000)\n  }, {\n    id: 5,\n    name: \"David Kim\",\n    email: \"<EMAIL>\",\n    role: \"Viewer\",\n    status: \"Offline\",\n    avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n    department: \"QA\",\n    currentTask: null,\n    tasksCompleted: 8,\n    lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n  }];\n  const mockNotifications = [{\n    id: 1,\n    type: \"task_assigned\",\n    title: \"New Task Assigned\",\n    message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n    timestamp: new Date(Date.now() - 30 * 60 * 1000),\n    read: false,\n    priority: \"high\",\n    user: {\n      name: \"Emily Davis\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\"\n    },\n    actions: [{\n      label: \"View Task\",\n      variant: \"default\"\n    }, {\n      label: \"Accept\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 2,\n    type: \"deadline_reminder\",\n    title: \"Deadline Approaching\",\n    message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n    read: false,\n    priority: \"medium\",\n    user: null,\n    actions: [{\n      label: \"View Details\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 3,\n    type: \"comment_mention\",\n    title: \"You were mentioned\",\n    message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n    read: true,\n    priority: \"low\",\n    user: {\n      name: \"Sarah Johnson\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\"\n    },\n    actions: [{\n      label: \"Reply\",\n      variant: \"outline\"\n    }]\n  }, {\n    id: 4,\n    type: \"system_alert\",\n    title: \"System Maintenance\",\n    message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n    read: true,\n    priority: \"medium\",\n    user: null,\n    actions: []\n  }];\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    var _realData$total_organ, _realData$total_proje, _realData$total_membe, _realData$recent_acti, _realData$recent_acti2, _realData$total_proje2, _realData$total_membe2, _realData$total_organ2, _realData$recent_acti3, _realData$recent_acti4, _realData$total_proje3, _realData$total_organ3, _realData$total_membe3, _realData$recent_acti5, _realData$recent_acti6, _realData$total_proje4, _realData$total_organ4, _realData$total_membe4, _realData$recent_acti7, _realData$recent_acti8;\n    const realData = dashboardData || {};\n    switch (userRole) {\n      case 'owner':\n        return [{\n          title: \"Organizations\",\n          value: ((_realData$total_organ = realData.total_organizations) === null || _realData$total_organ === void 0 ? void 0 : _realData$total_organ.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Active Projects\",\n          value: ((_realData$total_proje = realData.total_projects) === null || _realData$total_proje === void 0 ? void 0 : _realData$total_proje.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe = realData.total_members) === null || _realData$total_membe === void 0 ? void 0 : _realData$total_membe.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti = realData.recent_activity) === null || _realData$recent_acti === void 0 ? void 0 : (_realData$recent_acti2 = _realData$recent_acti.length) === null || _realData$recent_acti2 === void 0 ? void 0 : _realData$recent_acti2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'admin':\n        return [{\n          title: \"Active Projects\",\n          value: ((_realData$total_proje2 = realData.total_projects) === null || _realData$total_proje2 === void 0 ? void 0 : _realData$total_proje2.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe2 = realData.total_members) === null || _realData$total_membe2 === void 0 ? void 0 : _realData$total_membe2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ2 = realData.total_organizations) === null || _realData$total_organ2 === void 0 ? void 0 : _realData$total_organ2.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti3 = realData.recent_activity) === null || _realData$recent_acti3 === void 0 ? void 0 : (_realData$recent_acti4 = _realData$recent_acti3.length) === null || _realData$recent_acti4 === void 0 ? void 0 : _realData$recent_acti4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'member':\n        return [{\n          title: \"My Projects\",\n          value: ((_realData$total_proje3 = realData.total_projects) === null || _realData$total_proje3 === void 0 ? void 0 : _realData$total_proje3.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ3 = realData.total_organizations) === null || _realData$total_organ3 === void 0 ? void 0 : _realData$total_organ3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe3 = realData.total_members) === null || _realData$total_membe3 === void 0 ? void 0 : _realData$total_membe3.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Recent Activity\",\n          value: ((_realData$recent_acti5 = realData.recent_activity) === null || _realData$recent_acti5 === void 0 ? void 0 : (_realData$recent_acti6 = _realData$recent_acti5.length) === null || _realData$recent_acti6 === void 0 ? void 0 : _realData$recent_acti6.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n      case 'viewer':\n        return [{\n          title: \"Projects Viewed\",\n          value: ((_realData$total_proje4 = realData.total_projects) === null || _realData$total_proje4 === void 0 ? void 0 : _realData$total_proje4.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Eye\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: ((_realData$total_organ4 = realData.total_organizations) === null || _realData$total_organ4 === void 0 ? void 0 : _realData$total_organ4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"accent\"\n        }, {\n          title: \"Team Members\",\n          value: ((_realData$total_membe4 = realData.total_members) === null || _realData$total_membe4 === void 0 ? void 0 : _realData$total_membe4.toString()) || \"1\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"success\"\n        }, {\n          title: \"Activity Items\",\n          value: ((_realData$recent_acti7 = realData.recent_activity) === null || _realData$recent_acti7 === void 0 ? void 0 : (_realData$recent_acti8 = _realData$recent_acti7.length) === null || _realData$recent_acti8 === void 0 ? void 0 : _realData$recent_acti8.toString()) || \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"BarChart3\",\n          color: \"warning\"\n        }];\n      default:\n        return [{\n          title: \"Projects\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"FolderOpen\",\n          color: \"primary\"\n        }, {\n          title: \"Organizations\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Building\",\n          color: \"success\"\n        }, {\n          title: \"Members\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Users\",\n          color: \"accent\"\n        }, {\n          title: \"Activity\",\n          value: \"0\",\n          change: \"+0\",\n          changeType: \"neutral\",\n          icon: \"Activity\",\n          color: \"warning\"\n        }];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = (projects.length > 0 ? projects : mockProjects).filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) || (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' || (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n  // Get current organization data\n  const currentOrganization = organizations.length > 0 ? {\n    name: ((_organizations$0$orga = organizations[0].organization) === null || _organizations$0$orga === void 0 ? void 0 : _organizations$0$orga.name) || 'Your Organization',\n    domain: (currentUser === null || currentUser === void 0 ? void 0 : (_currentUser$email = currentUser.email) === null || _currentUser$email === void 0 ? void 0 : _currentUser$email.split('@')[1]) || 'company.com',\n    logo: ((_organizations$0$orga2 = organizations[0].organization) === null || _organizations$0$orga2 === void 0 ? void 0 : _organizations$0$orga2.logo_url) || '/assets/images/org-logo.png'\n  } : {\n    name: 'Your Organization',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\",\n    children: [/*#__PURE__*/_jsxDEV(RoleBasedHeader, {\n      userRole: userRole.toLowerCase(),\n      currentUser: currentUser ? {\n        name: `${currentUser.firstName} ${currentUser.lastName}`,\n        email: currentUser.email,\n        avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n        role: userRole\n      } : {\n        name: 'Loading...',\n        email: '',\n        avatar: '/assets/images/avatar.jpg',\n        role: userRole\n      },\n      currentOrganization: currentOrganization\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), showWelcome && welcomeMessage && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gradient-to-r from-green-50 to-blue-50 border-l-4 border-green-400 p-6 mt-16 mx-4 rounded-lg shadow-sm\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-start\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6 text-green-400\",\n            fill: \"none\",\n            viewBox: \"0 0 24 24\",\n            stroke: \"currentColor\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 501,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 500,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3 flex-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-medium text-green-800\",\n            children: \"Welcome to Agno WorkSphere!\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-2 text-sm text-green-700\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: welcomeMessage\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 507,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setShowWelcome(false),\n              className: \"bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-md text-sm font-medium transition-colors\",\n              children: \"Got it, thanks!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 510,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 509,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 497,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `glass-effect border-b border-white/20 p-4 ${showWelcome ? 'mt-4' : 'mt-16'}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-2 h-2 bg-green-500 rounded-full animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-slate-700\",\n              children: currentUser ? `Welcome, ${currentUser.firstName}!` : 'Loading...'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center gap-2 text-sm text-slate-600\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium capitalize\",\n              children: userRole\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 15\n            }, this), currentOrganization && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-slate-500\",\n              children: [\"\\u2022 \", currentOrganization.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 525,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center gap-3 text-sm text-slate-600\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Organizations: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_organizations) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 545,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Projects: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_projects) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"\\u2022\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: [\"Members: \", (dashboardData === null || dashboardData === void 0 ? void 0 : dashboardData.total_members) || 0]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 548,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 524,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 523,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DashboardHeader, {\n      userRole: userRole,\n      onFilterChange: setFilterValue,\n      onSearchChange: setSearchValue,\n      searchValue: searchValue\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 554,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto p-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\",\n        children: getKPIData().map((kpi, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-fade-in\",\n          style: {\n            animationDelay: `${index * 0.1}s`\n          },\n          children: /*#__PURE__*/_jsxDEV(KPICard, {\n            title: kpi.title,\n            value: kpi.value,\n            change: kpi.change,\n            changeType: kpi.changeType,\n            icon: kpi.icon,\n            color: kpi.color\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 567,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 566,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 564,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"lg:col-span-2 space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                  className: \"text-2xl font-semibold text-slate-800 tracking-tight\",\n                  children: userRole === 'Viewer' ? 'Available Projects' : 'My Projects'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 587,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-slate-600 mt-1\",\n                  children: \"Manage and track your active projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 586,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Link, {\n                to: \"/project-overview-analytics\",\n                className: \"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\",\n                children: [\"View Analytics\", /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 5l7 7-7 7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 598,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 597,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 592,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 585,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-2 gap-6\",\n              children: filteredProjects.map((project, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"animate-fade-in\",\n                style: {\n                  animationDelay: `${index * 0.1}s`\n                },\n                children: /*#__PURE__*/_jsxDEV(ProjectCard, {\n                  project: project,\n                  userRole: userRole\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 606,\n                  columnNumber: 21\n                }, this)\n              }, project.id, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), filteredProjects.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-8 h-8 text-slate-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 618,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 617,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 616,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-600 text-lg\",\n                children: \"No projects match your current filters\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 621,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-slate-500 text-sm mt-1\",\n                children: \"Try adjusting your search or filter criteria\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 622,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 615,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(QuickActions, {\n            userRole: userRole,\n            onCreateProject: handleOpenCreateProject,\n            onManageUsers: handleManageUsers,\n            onInviteMembers: handleInviteMembers\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 582,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"space-y-8\",\n          children: [/*#__PURE__*/_jsxDEV(ActivityFeed, {\n            activities: mockActivities,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(NotificationPanel, {\n            notifications: mockNotifications,\n            userRole: userRole\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 639,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 580,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8\",\n        children: [/*#__PURE__*/_jsxDEV(TaskSummary, {\n          tasks: mockTasks,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TeamOverview, {\n          teamMembers: mockTeamMembers,\n          userRole: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 644,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 562,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CreateProjectModal, {\n      isOpen: showCreateProject,\n      onClose: handleCloseCreateProject,\n      onCreateProject: handleCreateProject,\n      organizationId: authService.getOrganizationId(),\n      organizationName: (currentOrganization === null || currentOrganization === void 0 ? void 0 : currentOrganization.name) || 'Your Organization'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 651,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 477,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleBasedDashboard, \"qC6X1Pe2LLKdJUS0RMrv9u0b+cM=\", false, function () {\n  return [useLocation];\n});\n_c = RoleBasedDashboard;\nexport default RoleBasedDashboard;\nvar _c;\n$RefreshReg$(_c, \"RoleBasedDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "useLocation", "RoleBasedHeader", "DashboardHeader", "KPICard", "ProjectCard", "ActivityFeed", "QuickActions", "TaskSummary", "TeamOverview", "NotificationPanel", "authService", "apiService", "CreateProjectModal", "InviteMemberModal", "jsxDEV", "_jsxDEV", "RoleBasedDashboard", "_s", "_organizations$0$orga", "_currentUser$email", "_organizations$0$orga2", "location", "userRole", "setUserRole", "searchValue", "setSearchValue", "filterValue", "setFilterValue", "dashboardData", "setDashboardData", "projects", "setProjects", "currentUser", "setCurrentUser", "organizations", "setOrganizations", "loading", "setLoading", "error", "setError", "showWelcome", "setShowWelcome", "welcomeMessage", "setWelcomeMessage", "showCreateProject", "setShowCreateProject", "showInviteMember", "setShowInviteMember", "loadDashboardData", "_location$state", "_location$state2", "state", "message", "type", "window", "history", "replaceState", "document", "title", "userResult", "getCurrentUser", "data", "user", "role", "getUserRole", "statsResult", "getDashboardStats", "organizationId", "getOrganizationId", "projectsResult", "getAll", "err", "console", "handleCreateProject", "projectData", "Error", "newProject", "create", "log", "handleOpenCreateProject", "handleCloseCreateProject", "handleManageUsers", "href", "handleInviteMembers", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "onClick", "reload", "mockProjects", "id", "name", "description", "status", "priority", "progress", "dueDate", "team", "avatar", "mockActivities", "project", "timestamp", "Date", "now", "isPublic", "mockTasks", "assignee", "mockTeamMembers", "email", "department", "currentTask", "tasksCompleted", "lastActive", "mockNotifications", "read", "actions", "label", "variant", "getKPIData", "_realData$total_organ", "_realData$total_proje", "_realData$total_membe", "_realData$recent_acti", "_realData$recent_acti2", "_realData$total_proje2", "_realData$total_membe2", "_realData$total_organ2", "_realData$recent_acti3", "_realData$recent_acti4", "_realData$total_proje3", "_realData$total_organ3", "_realData$total_membe3", "_realData$recent_acti5", "_realData$recent_acti6", "_realData$total_proje4", "_realData$total_organ4", "_realData$total_membe4", "_realData$recent_acti7", "_realData$recent_acti8", "realData", "value", "total_organizations", "toString", "change", "changeType", "icon", "color", "total_projects", "total_members", "recent_activity", "length", "filteredProjects", "filter", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "currentOrganization", "organization", "domain", "split", "logo", "logo_url", "firstName", "lastName", "onFilterChange", "onSearchChange", "map", "kpi", "index", "style", "animationDelay", "to", "onCreateProject", "onManageUsers", "onInviteMembers", "activities", "notifications", "tasks", "teamMembers", "isOpen", "onClose", "organizationName", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/role-based-dashboard/index.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link, useLocation } from 'react-router-dom';\nimport RoleBasedHeader from '../../components/ui/RoleBasedHeader';\nimport DashboardHeader from './components/DashboardHeader';\nimport KPICard from './components/KPICard';\nimport ProjectCard from './components/ProjectCard';\nimport ActivityFeed from './components/ActivityFeed';\nimport QuickActions from './components/QuickActions';\nimport TaskSummary from './components/TaskSummary';\nimport TeamOverview from './components/TeamOverview';\nimport NotificationPanel from './components/NotificationPanel';\nimport authService from '../../utils/authService';\nimport apiService from '../../utils/apiService';\nimport CreateProjectModal from '../../components/modals/CreateProjectModal';\nimport InviteMemberModal from '../../components/modals/InviteMemberModal';\n\nconst RoleBasedDashboard = () => {\n  const location = useLocation();\n  const [userRole, setUserRole] = useState('member'); // Default role\n  const [searchValue, setSearchValue] = useState('');\n  const [filterValue, setFilterValue] = useState('all');\n  const [dashboardData, setDashboardData] = useState(null);\n  const [projects, setProjects] = useState([]);\n  const [currentUser, setCurrentUser] = useState(null);\n  const [organizations, setOrganizations] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [showWelcome, setShowWelcome] = useState(false);\n  const [welcomeMessage, setWelcomeMessage] = useState('');\n  const [showCreateProject, setShowCreateProject] = useState(false);\n  const [showInviteMember, setShowInviteMember] = useState(false);\n\n  // Load dashboard data from backend\n  useEffect(() => {\n    const loadDashboardData = async () => {\n      try {\n        setLoading(true);\n\n        // Check for welcome message from navigation state\n        if (location.state?.message && location.state?.type === 'success') {\n          setWelcomeMessage(location.state.message);\n          setShowWelcome(true);\n          // Clear the state to prevent showing on refresh\n          window.history.replaceState({}, document.title);\n        }\n\n        // Get current user and role\n        const userResult = await authService.getCurrentUser();\n        if (userResult.data.user) {\n          setCurrentUser(userResult.data.user);\n          setUserRole(userResult.data.user.role || authService.getUserRole());\n          setOrganizations(userResult.data.organizations || []);\n        }\n\n        // Get dashboard stats\n        const statsResult = await authService.getDashboardStats();\n        if (statsResult.data) {\n          setDashboardData(statsResult.data);\n        }\n\n        // Get projects\n        const organizationId = authService.getOrganizationId();\n        if (organizationId) {\n          const projectsResult = await apiService.projects.getAll(organizationId);\n          setProjects(projectsResult || []);\n        }\n\n        setError(null);\n      } catch (err) {\n        console.error('Failed to load dashboard data:', err);\n        setError(err.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    loadDashboardData();\n  }, [location.state]);\n\n  // Project creation handler\n  const handleCreateProject = async (projectData) => {\n    try {\n      const organizationId = authService.getOrganizationId();\n      if (!organizationId) {\n        throw new Error('No organization found');\n      }\n\n      const newProject = await apiService.projects.create(organizationId, projectData);\n\n      // Refresh projects list\n      const projectsResult = await apiService.projects.getAll(organizationId);\n      setProjects(projectsResult || []);\n\n      // Refresh dashboard stats\n      const statsResult = await authService.getDashboardStats();\n      if (statsResult.data) {\n        setDashboardData(statsResult.data);\n      }\n\n      console.log('Project created successfully:', newProject);\n    } catch (error) {\n      console.error('Failed to create project:', error);\n      throw error;\n    }\n  };\n\n  // Quick action handlers\n  const handleOpenCreateProject = () => {\n    setShowCreateProject(true);\n  };\n\n  const handleCloseCreateProject = () => {\n    setShowCreateProject(false);\n  };\n\n  const handleManageUsers = () => {\n    // Navigate to team members page\n    window.location.href = '/team-members';\n  };\n\n  const handleInviteMembers = () => {\n    // Navigate to team members page with invite mode\n    window.location.href = '/team-members?action=invite';\n  };\n\n  // Show loading state\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-slate-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    );\n  }\n\n  // Show error state\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n          </div>\n          <p className=\"text-red-600 text-lg mb-2\">Failed to load dashboard</p>\n          <p className=\"text-slate-600 text-sm\">{error}</p>\n          <button\n            onClick={() => window.location.reload()}\n            className=\"mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // Mock data for different dashboard components (fallback)\n  const mockProjects = [\n    {\n      id: 1,\n      name: \"E-commerce Platform Redesign\",\n      description: \"Complete overhaul of the existing e-commerce platform with modern UI/UX design and improved performance optimization.\",\n      status: \"Active\",\n      priority: \"High\",\n      progress: 75,\n      dueDate: \"Dec 15, 2025\",\n      team: [\n        { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n        { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n        { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n        { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" }\n      ]\n    },\n    {\n      id: 2,\n      name: \"Mobile App Development\",\n      description: \"Native iOS and Android application development for customer engagement and loyalty program management.\",\n      status: \"Active\",\n      priority: \"Medium\",\n      progress: 45,\n      dueDate: \"Jan 30, 2026\",\n      team: [\n        { name: \"David Kim\", avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\" },\n        { name: \"Lisa Wang\", avatar: \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150\" },\n        { name: \"Tom Wilson\", avatar: \"https://images.unsplash.com/photo-1519244703995-f4e0f30006d5?w=150\" }\n      ]\n    },\n    {\n      id: 3,\n      name: \"Data Analytics Dashboard\",\n      description: \"Business intelligence dashboard for real-time analytics and reporting with advanced data visualization capabilities.\",\n      status: \"Completed\",\n      priority: \"Low\",\n      progress: 100,\n      dueDate: \"Nov 20, 2025\",\n      team: [\n        { name: \"Rachel Green\", avatar: \"https://images.unsplash.com/photo-1517841905240-472988babdf9?w=150\" },\n        { name: \"James Brown\", avatar: \"https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150\" }\n      ]\n    }\n  ];\n\n  const mockActivities = [\n    {\n      id: 1,\n      type: \"task_completed\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      description: \"Completed the user interface mockups for the checkout process\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 15 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 2,\n      type: \"comment_added\",\n      user: { name: \"Mike Chen\", avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\" },\n      description: \"Added feedback on the mobile responsive design implementation\",\n      project: \"E-commerce Platform\",\n      timestamp: new Date(Date.now() - 45 * 60 * 1000),\n      isPublic: true\n    },\n    {\n      id: 3,\n      type: \"project_created\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      description: \"Created new project for Q1 marketing campaign automation\",\n      project: \"Marketing Automation\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      isPublic: false\n    },\n    {\n      id: 4,\n      type: \"member_added\",\n      user: { name: \"Alex Rodriguez\", avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\" },\n      description: \"Joined the mobile app development team as a senior developer\",\n      project: \"Mobile App Development\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      isPublic: true\n    }\n  ];\n\n  const mockTasks = [\n    {\n      id: 1,\n      title: \"Implement payment gateway integration\",\n      status: \"In Progress\",\n      priority: \"High\",\n      dueDate: \"Dec 10, 2025\",\n      assignee: \"Sarah Johnson\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 2,\n      title: \"Design user onboarding flow\",\n      status: \"To Do\",\n      priority: \"Medium\",\n      dueDate: \"Dec 12, 2025\",\n      assignee: \"Mike Chen\",\n      project: \"Mobile App\"\n    },\n    {\n      id: 3,\n      title: \"Set up automated testing pipeline\",\n      status: \"Review\",\n      priority: \"High\",\n      dueDate: \"Dec 8, 2025\",\n      assignee: \"Emily Davis\",\n      project: \"E-commerce Platform\"\n    },\n    {\n      id: 4,\n      title: \"Create API documentation\",\n      status: \"Done\",\n      priority: \"Low\",\n      dueDate: \"Dec 5, 2025\",\n      assignee: \"Alex Rodriguez\",\n      project: \"Data Analytics\"\n    },\n    {\n      id: 5,\n      title: \"Optimize database queries\",\n      status: \"Blocked\",\n      priority: \"High\",\n      dueDate: \"Dec 15, 2025\",\n      assignee: \"David Kim\",\n      project: \"E-commerce Platform\"\n    }\n  ];\n\n  const mockTeamMembers = [\n    {\n      id: 1,\n      name: \"Sarah Johnson\",\n      email: \"<EMAIL>\",\n      role: \"Admin\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\",\n      department: \"Design\",\n      currentTask: \"UI/UX Design Review\",\n      tasksCompleted: 24,\n      lastActive: new Date()\n    },\n    {\n      id: 2,\n      name: \"Mike Chen\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Away\",\n      avatar: \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150\",\n      department: \"Development\",\n      currentTask: \"Frontend Implementation\",\n      tasksCompleted: 18,\n      lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000)\n    },\n    {\n      id: 3,\n      name: \"Emily Davis\",\n      email: \"<EMAIL>\",\n      role: \"Owner\",\n      status: \"Online\",\n      avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\",\n      department: \"Management\",\n      currentTask: \"Project Planning\",\n      tasksCompleted: 32,\n      lastActive: new Date()\n    },\n    {\n      id: 4,\n      name: \"Alex Rodriguez\",\n      email: \"<EMAIL>\",\n      role: \"Member\",\n      status: \"Busy\",\n      avatar: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150\",\n      department: \"Development\",\n      currentTask: \"API Integration\",\n      tasksCompleted: 15,\n      lastActive: new Date(Date.now() - 30 * 60 * 1000)\n    },\n    {\n      id: 5,\n      name: \"David Kim\",\n      email: \"<EMAIL>\",\n      role: \"Viewer\",\n      status: \"Offline\",\n      avatar: \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150\",\n      department: \"QA\",\n      currentTask: null,\n      tasksCompleted: 8,\n      lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000)\n    }\n  ];\n\n  const mockNotifications = [\n    {\n      id: 1,\n      type: \"task_assigned\",\n      title: \"New Task Assigned\",\n      message: \"You have been assigned to work on the payment gateway integration for the e-commerce platform.\",\n      timestamp: new Date(Date.now() - 30 * 60 * 1000),\n      read: false,\n      priority: \"high\",\n      user: { name: \"Emily Davis\", avatar: \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150\" },\n      actions: [\n        { label: \"View Task\", variant: \"default\" },\n        { label: \"Accept\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 2,\n      type: \"deadline_reminder\",\n      title: \"Deadline Approaching\",\n      message: \"The UI/UX design review is due in 2 days. Please ensure all deliverables are ready for submission.\",\n      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),\n      read: false,\n      priority: \"medium\",\n      user: null,\n      actions: [\n        { label: \"View Details\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 3,\n      type: \"comment_mention\",\n      title: \"You were mentioned\",\n      message: \"Sarah Johnson mentioned you in a comment on the mobile app development project.\",\n      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),\n      read: true,\n      priority: \"low\",\n      user: { name: \"Sarah Johnson\", avatar: \"https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150\" },\n      actions: [\n        { label: \"Reply\", variant: \"outline\" }\n      ]\n    },\n    {\n      id: 4,\n      type: \"system_alert\",\n      title: \"System Maintenance\",\n      message: \"Scheduled maintenance will occur tonight from 11 PM to 1 AM EST. Some features may be temporarily unavailable.\",\n      timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),\n      read: true,\n      priority: \"medium\",\n      user: null,\n      actions: []\n    }\n  ];\n\n  // Role-based KPI data with real data integration\n  const getKPIData = () => {\n    const realData = dashboardData || {};\n\n    switch (userRole) {\n      case 'owner':\n        return [\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'admin':\n        return [\n          { title: \"Active Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'member':\n        return [\n          { title: \"My Projects\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Recent Activity\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n      case 'viewer':\n        return [\n          { title: \"Projects Viewed\", value: realData.total_projects?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Eye\", color: \"primary\" },\n          { title: \"Organizations\", value: realData.total_organizations?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"accent\" },\n          { title: \"Team Members\", value: realData.total_members?.toString() || \"1\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"success\" },\n          { title: \"Activity Items\", value: realData.recent_activity?.length?.toString() || \"0\", change: \"+0\", changeType: \"neutral\", icon: \"BarChart3\", color: \"warning\" }\n        ];\n      default:\n        return [\n          { title: \"Projects\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"FolderOpen\", color: \"primary\" },\n          { title: \"Organizations\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Building\", color: \"success\" },\n          { title: \"Members\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Users\", color: \"accent\" },\n          { title: \"Activity\", value: \"0\", change: \"+0\", changeType: \"neutral\", icon: \"Activity\", color: \"warning\" }\n        ];\n    }\n  };\n\n  // Filter projects based on search and filter values\n  const filteredProjects = (projects.length > 0 ? projects : mockProjects).filter(project => {\n    const matchesSearch = project.name.toLowerCase().includes(searchValue.toLowerCase()) ||\n                         (project.description || '').toLowerCase().includes(searchValue.toLowerCase());\n    const matchesFilter = filterValue === 'all' ||\n                         (project.status || 'active').toLowerCase() === filterValue.toLowerCase();\n    return matchesSearch && matchesFilter;\n  });\n\n\n\n  // Get current organization data\n  const currentOrganization = organizations.length > 0 ? {\n    name: organizations[0].organization?.name || 'Your Organization',\n    domain: currentUser?.email?.split('@')[1] || 'company.com',\n    logo: organizations[0].organization?.logo_url || '/assets/images/org-logo.png'\n  } : {\n    name: 'Your Organization',\n    domain: 'company.com',\n    logo: '/assets/images/org-logo.png'\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20\">\n      {/* Role-Based Header */}\n      <RoleBasedHeader\n        userRole={userRole.toLowerCase()}\n        currentUser={currentUser ? {\n          name: `${currentUser.firstName} ${currentUser.lastName}`,\n          email: currentUser.email,\n          avatar: currentUser.avatar || '/assets/images/avatar.jpg',\n          role: userRole\n        } : {\n          name: 'Loading...',\n          email: '',\n          avatar: '/assets/images/avatar.jpg',\n          role: userRole\n        }}\n        currentOrganization={currentOrganization}\n      />\n\n      {/* Welcome Message for New Users */}\n      {showWelcome && welcomeMessage && (\n        <div className=\"bg-gradient-to-r from-green-50 to-blue-50 border-l-4 border-green-400 p-6 mt-16 mx-4 rounded-lg shadow-sm\">\n          <div className=\"max-w-7xl mx-auto flex items-start\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-6 w-6 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z\" />\n              </svg>\n            </div>\n            <div className=\"ml-3 flex-1\">\n              <h3 className=\"text-lg font-medium text-green-800\">Welcome to Agno WorkSphere!</h3>\n              <div className=\"mt-2 text-sm text-green-700\">\n                <p>{welcomeMessage}</p>\n              </div>\n              <div className=\"mt-4\">\n                <button\n                  onClick={() => setShowWelcome(false)}\n                  className=\"bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  Got it, thanks!\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* User Info Bar */}\n      <div className={`glass-effect border-b border-white/20 p-4 ${showWelcome ? 'mt-4' : 'mt-16'}`}>\n        <div className=\"max-w-7xl mx-auto flex items-center justify-between\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"flex items-center gap-3\">\n              <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm font-medium text-slate-700\">\n                {currentUser ? `Welcome, ${currentUser.firstName}!` : 'Loading...'}\n              </span>\n            </div>\n            <div className=\"flex items-center gap-2 text-sm text-slate-600\">\n              <span className=\"px-3 py-1 bg-blue-100 text-blue-800 rounded-full font-medium capitalize\">\n                {userRole}\n              </span>\n              {currentOrganization && (\n                <span className=\"text-slate-500\">\n                  • {currentOrganization.name}\n                </span>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center gap-3 text-sm text-slate-600\">\n            <span>Organizations: {dashboardData?.total_organizations || 0}</span>\n            <span>•</span>\n            <span>Projects: {dashboardData?.total_projects || 0}</span>\n            <span>•</span>\n            <span>Members: {dashboardData?.total_members || 0}</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Dashboard Header */}\n      <DashboardHeader\n        userRole={userRole}\n        onFilterChange={setFilterValue}\n        onSearchChange={setSearchValue}\n        searchValue={searchValue}\n      />\n\n      {/* Main Dashboard Content */}\n      <div className=\"max-w-7xl mx-auto p-8\">\n        {/* Enhanced KPI Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10\">\n          {getKPIData().map((kpi, index) => (\n            <div key={index} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n              <KPICard\n                title={kpi.title}\n                value={kpi.value}\n                change={kpi.change}\n                changeType={kpi.changeType}\n                icon={kpi.icon}\n                color={kpi.color}\n              />\n            </div>\n          ))}\n        </div>\n\n        {/* Main Content Grid with improved spacing */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 mb-10\">\n          {/* Left Column - Projects and Quick Actions */}\n          <div className=\"lg:col-span-2 space-y-8\">\n            {/* Projects Section with enhanced header */}\n            <div className=\"space-y-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <h2 className=\"text-2xl font-semibold text-slate-800 tracking-tight\">\n                    {userRole === 'Viewer' ? 'Available Projects' : 'My Projects'}\n                  </h2>\n                  <p className=\"text-slate-600 mt-1\">Manage and track your active projects</p>\n                </div>\n                <Link \n                  to=\"/project-overview-analytics\"\n                  className=\"flex items-center gap-2 px-4 py-2 text-sm text-blue-600 hover:text-blue-700 font-medium bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200\"\n                >\n                  View Analytics\n                  <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                  </svg>\n                </Link>\n              </div>\n              \n              <div className=\"grid grid-cols-1 xl:grid-cols-2 gap-6\">\n                {filteredProjects.map((project, index) => (\n                  <div key={project.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                    <ProjectCard\n                      project={project}\n                      userRole={userRole}\n                    />\n                  </div>\n                ))}\n              </div>\n              \n              {filteredProjects.length === 0 && (\n                <div className=\"text-center py-16 bg-white/60 backdrop-blur-sm rounded-2xl border border-white/20\">\n                  <div className=\"w-16 h-16 bg-slate-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <svg className=\"w-8 h-8 text-slate-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-slate-600 text-lg\">No projects match your current filters</p>\n                  <p className=\"text-slate-500 text-sm mt-1\">Try adjusting your search or filter criteria</p>\n                </div>\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <QuickActions\n              userRole={userRole}\n              onCreateProject={handleOpenCreateProject}\n              onManageUsers={handleManageUsers}\n              onInviteMembers={handleInviteMembers}\n            />\n          </div>\n\n          {/* Right Column - Activity Feed and Notifications */}\n          <div className=\"space-y-8\">\n            <ActivityFeed activities={mockActivities} userRole={userRole} />\n            <NotificationPanel notifications={mockNotifications} userRole={userRole} />\n          </div>\n        </div>\n\n        {/* Bottom Section - Tasks and Team with improved layout */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          <TaskSummary tasks={mockTasks} userRole={userRole} />\n          <TeamOverview teamMembers={mockTeamMembers} userRole={userRole} />\n        </div>\n      </div>\n\n      {/* Create Project Modal */}\n      <CreateProjectModal\n        isOpen={showCreateProject}\n        onClose={handleCloseCreateProject}\n        onCreateProject={handleCreateProject}\n        organizationId={authService.getOrganizationId()}\n        organizationName={currentOrganization?.name || 'Your Organization'}\n      />\n    </div>\n  );\n};\n\nexport default RoleBasedDashboard;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,WAAW,QAAQ,kBAAkB;AACpD,OAAOC,eAAe,MAAM,qCAAqC;AACjE,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAOC,iBAAiB,MAAM,gCAAgC;AAC9D,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,kBAAkB,MAAM,4CAA4C;AAC3E,OAAOC,iBAAiB,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1E,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,kBAAA,EAAAC,sBAAA;EAC/B,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,QAAQ,EAAEC,WAAW,CAAC,GAAG1B,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;EACpD,MAAM,CAAC2B,WAAW,EAAEC,cAAc,CAAC,GAAG5B,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC6B,WAAW,EAAEC,cAAc,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC+B,aAAa,EAAEC,gBAAgB,CAAC,GAAGhC,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACiC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACmC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAC,IAAI,CAAC;EACpD,MAAM,CAACqC,aAAa,EAAEC,gBAAgB,CAAC,GAAGtC,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACuC,OAAO,EAAEC,UAAU,CAAC,GAAGxC,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAAC6C,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC+C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;;EAE/D;EACAC,SAAS,CAAC,MAAM;IACd,MAAMkD,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QAAA,IAAAC,eAAA,EAAAC,gBAAA;QACFb,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,IAAI,CAAAY,eAAA,GAAA5B,QAAQ,CAAC8B,KAAK,cAAAF,eAAA,eAAdA,eAAA,CAAgBG,OAAO,IAAI,EAAAF,gBAAA,GAAA7B,QAAQ,CAAC8B,KAAK,cAAAD,gBAAA,uBAAdA,gBAAA,CAAgBG,IAAI,MAAK,SAAS,EAAE;UACjEV,iBAAiB,CAACtB,QAAQ,CAAC8B,KAAK,CAACC,OAAO,CAAC;UACzCX,cAAc,CAAC,IAAI,CAAC;UACpB;UACAa,MAAM,CAACC,OAAO,CAACC,YAAY,CAAC,CAAC,CAAC,EAAEC,QAAQ,CAACC,KAAK,CAAC;QACjD;;QAEA;QACA,MAAMC,UAAU,GAAG,MAAMjD,WAAW,CAACkD,cAAc,CAAC,CAAC;QACrD,IAAID,UAAU,CAACE,IAAI,CAACC,IAAI,EAAE;UACxB7B,cAAc,CAAC0B,UAAU,CAACE,IAAI,CAACC,IAAI,CAAC;UACpCvC,WAAW,CAACoC,UAAU,CAACE,IAAI,CAACC,IAAI,CAACC,IAAI,IAAIrD,WAAW,CAACsD,WAAW,CAAC,CAAC,CAAC;UACnE7B,gBAAgB,CAACwB,UAAU,CAACE,IAAI,CAAC3B,aAAa,IAAI,EAAE,CAAC;QACvD;;QAEA;QACA,MAAM+B,WAAW,GAAG,MAAMvD,WAAW,CAACwD,iBAAiB,CAAC,CAAC;QACzD,IAAID,WAAW,CAACJ,IAAI,EAAE;UACpBhC,gBAAgB,CAACoC,WAAW,CAACJ,IAAI,CAAC;QACpC;;QAEA;QACA,MAAMM,cAAc,GAAGzD,WAAW,CAAC0D,iBAAiB,CAAC,CAAC;QACtD,IAAID,cAAc,EAAE;UAClB,MAAME,cAAc,GAAG,MAAM1D,UAAU,CAACmB,QAAQ,CAACwC,MAAM,CAACH,cAAc,CAAC;UACvEpC,WAAW,CAACsC,cAAc,IAAI,EAAE,CAAC;QACnC;QAEA9B,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,CAAC,OAAOgC,GAAG,EAAE;QACZC,OAAO,CAAClC,KAAK,CAAC,gCAAgC,EAAEiC,GAAG,CAAC;QACpDhC,QAAQ,CAACgC,GAAG,CAACnB,OAAO,CAAC;MACvB,CAAC,SAAS;QACRf,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDW,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAAC3B,QAAQ,CAAC8B,KAAK,CAAC,CAAC;;EAEpB;EACA,MAAMsB,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD,IAAI;MACF,MAAMP,cAAc,GAAGzD,WAAW,CAAC0D,iBAAiB,CAAC,CAAC;MACtD,IAAI,CAACD,cAAc,EAAE;QACnB,MAAM,IAAIQ,KAAK,CAAC,uBAAuB,CAAC;MAC1C;MAEA,MAAMC,UAAU,GAAG,MAAMjE,UAAU,CAACmB,QAAQ,CAAC+C,MAAM,CAACV,cAAc,EAAEO,WAAW,CAAC;;MAEhF;MACA,MAAML,cAAc,GAAG,MAAM1D,UAAU,CAACmB,QAAQ,CAACwC,MAAM,CAACH,cAAc,CAAC;MACvEpC,WAAW,CAACsC,cAAc,IAAI,EAAE,CAAC;;MAEjC;MACA,MAAMJ,WAAW,GAAG,MAAMvD,WAAW,CAACwD,iBAAiB,CAAC,CAAC;MACzD,IAAID,WAAW,CAACJ,IAAI,EAAE;QACpBhC,gBAAgB,CAACoC,WAAW,CAACJ,IAAI,CAAC;MACpC;MAEAW,OAAO,CAACM,GAAG,CAAC,+BAA+B,EAAEF,UAAU,CAAC;IAC1D,CAAC,CAAC,OAAOtC,KAAK,EAAE;MACdkC,OAAO,CAAClC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMyC,uBAAuB,GAAGA,CAAA,KAAM;IACpClC,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMmC,wBAAwB,GAAGA,CAAA,KAAM;IACrCnC,oBAAoB,CAAC,KAAK,CAAC;EAC7B,CAAC;EAED,MAAMoC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA3B,MAAM,CAACjC,QAAQ,CAAC6D,IAAI,GAAG,eAAe;EACxC,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC;IACA7B,MAAM,CAACjC,QAAQ,CAAC6D,IAAI,GAAG,6BAA6B;EACtD,CAAC;;EAED;EACA,IAAI9C,OAAO,EAAE;IACX,oBACErB,OAAA;MAAKqE,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3HtE,OAAA;QAAKqE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtE,OAAA;UAAKqE,SAAS,EAAC;QAA6E;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnG1E,OAAA;UAAGqE,SAAS,EAAC,gBAAgB;UAAAC,QAAA,EAAC;QAAoB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,IAAInD,KAAK,EAAE;IACT,oBACEvB,OAAA;MAAKqE,SAAS,EAAC,8GAA8G;MAAAC,QAAA,eAC3HtE,OAAA;QAAKqE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BtE,OAAA;UAAKqE,SAAS,EAAC,iFAAiF;UAAAC,QAAA,eAC9FtE,OAAA;YAAKqE,SAAS,EAAC,sBAAsB;YAACM,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAP,QAAA,eACzFtE,OAAA;cAAM8E,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAmD;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1E,OAAA;UAAGqE,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACrE1E,OAAA;UAAGqE,SAAS,EAAC,wBAAwB;UAAAC,QAAA,EAAE/C;QAAK;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjD1E,OAAA;UACEkF,OAAO,EAAEA,CAAA,KAAM3C,MAAM,CAACjC,QAAQ,CAAC6E,MAAM,CAAC,CAAE;UACxCd,SAAS,EAAC,sFAAsF;UAAAC,QAAA,EACjG;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;;EAEA;EACA,MAAMU,YAAY,GAAG,CACnB;IACEC,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,8BAA8B;IACpCC,WAAW,EAAE,uHAAuH;IACpIC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACvG;MAAEP,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEP,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACrG;MAAEP,IAAI,EAAE,gBAAgB;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAE5G,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,wBAAwB;IAC9BC,WAAW,EAAE,wGAAwG;IACrHC,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,QAAQ;IAClBC,QAAQ,EAAE,EAAE;IACZC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACnG;MAAEP,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAkE,CAAC,EAChG;MAAEP,IAAI,EAAE,YAAY;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAExG,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,0BAA0B;IAChCC,WAAW,EAAE,sHAAsH;IACnIC,MAAM,EAAE,WAAW;IACnBC,QAAQ,EAAE,KAAK;IACfC,QAAQ,EAAE,GAAG;IACbC,OAAO,EAAE,cAAc;IACvBC,IAAI,EAAE,CACJ;MAAEN,IAAI,EAAE,cAAc;MAAEO,MAAM,EAAE;IAAqE,CAAC,EACtG;MAAEP,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;EAEzG,CAAC,CACF;EAED,MAAMC,cAAc,GAAG,CACrB;IACET,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,gBAAgB;IACtBS,IAAI,EAAE;MAAEuC,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC7GN,WAAW,EAAE,+DAA+D;IAC5EQ,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,eAAe;IACrBS,IAAI,EAAE;MAAEuC,IAAI,EAAE,WAAW;MAAEO,MAAM,EAAE;IAAqE,CAAC;IACzGN,WAAW,EAAE,+DAA+D;IAC5EQ,OAAO,EAAE,qBAAqB;IAC9BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,iBAAiB;IACvBS,IAAI,EAAE;MAAEuC,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC3GN,WAAW,EAAE,0DAA0D;IACvEQ,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEd,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,cAAc;IACpBS,IAAI,EAAE;MAAEuC,IAAI,EAAE,gBAAgB;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC9GN,WAAW,EAAE,8DAA8D;IAC3EQ,OAAO,EAAE,wBAAwB;IACjCC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,MAAMC,SAAS,GAAG,CAChB;IACEf,EAAE,EAAE,CAAC;IACL1C,KAAK,EAAE,uCAAuC;IAC9C6C,MAAM,EAAE,aAAa;IACrBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBU,QAAQ,EAAE,eAAe;IACzBN,OAAO,EAAE;EACX,CAAC,EACD;IACEV,EAAE,EAAE,CAAC;IACL1C,KAAK,EAAE,6BAA6B;IACpC6C,MAAM,EAAE,OAAO;IACfC,QAAQ,EAAE,QAAQ;IAClBE,OAAO,EAAE,cAAc;IACvBU,QAAQ,EAAE,WAAW;IACrBN,OAAO,EAAE;EACX,CAAC,EACD;IACEV,EAAE,EAAE,CAAC;IACL1C,KAAK,EAAE,mCAAmC;IAC1C6C,MAAM,EAAE,QAAQ;IAChBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,aAAa;IACtBU,QAAQ,EAAE,aAAa;IACvBN,OAAO,EAAE;EACX,CAAC,EACD;IACEV,EAAE,EAAE,CAAC;IACL1C,KAAK,EAAE,0BAA0B;IACjC6C,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,KAAK;IACfE,OAAO,EAAE,aAAa;IACtBU,QAAQ,EAAE,gBAAgB;IAC1BN,OAAO,EAAE;EACX,CAAC,EACD;IACEV,EAAE,EAAE,CAAC;IACL1C,KAAK,EAAE,2BAA2B;IAClC6C,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,MAAM;IAChBE,OAAO,EAAE,cAAc;IACvBU,QAAQ,EAAE,WAAW;IACrBN,OAAO,EAAE;EACX,CAAC,CACF;EAED,MAAMO,eAAe,GAAG,CACtB;IACEjB,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,eAAe;IACrBiB,KAAK,EAAE,wBAAwB;IAC/BvD,IAAI,EAAE,OAAO;IACbwC,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5EW,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,qBAAqB;IAClCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIV,IAAI,CAAC;EACvB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBiB,KAAK,EAAE,oBAAoB;IAC3BvD,IAAI,EAAE,QAAQ;IACdwC,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5EW,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,yBAAyB;IACtCC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACtD,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,aAAa;IACnBiB,KAAK,EAAE,sBAAsB;IAC7BvD,IAAI,EAAE,OAAO;IACbwC,MAAM,EAAE,QAAQ;IAChBK,MAAM,EAAE,oEAAoE;IAC5EW,UAAU,EAAE,YAAY;IACxBC,WAAW,EAAE,kBAAkB;IAC/BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIV,IAAI,CAAC;EACvB,CAAC,EACD;IACEZ,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,gBAAgB;IACtBiB,KAAK,EAAE,yBAAyB;IAChCvD,IAAI,EAAE,QAAQ;IACdwC,MAAM,EAAE,MAAM;IACdK,MAAM,EAAE,oEAAoE;IAC5EW,UAAU,EAAE,aAAa;IACzBC,WAAW,EAAE,iBAAiB;IAC9BC,cAAc,EAAE,EAAE;IAClBC,UAAU,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EAClD,CAAC,EACD;IACEb,EAAE,EAAE,CAAC;IACLC,IAAI,EAAE,WAAW;IACjBiB,KAAK,EAAE,oBAAoB;IAC3BvD,IAAI,EAAE,QAAQ;IACdwC,MAAM,EAAE,SAAS;IACjBK,MAAM,EAAE,oEAAoE;IAC5EW,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE,IAAI;IACjBC,cAAc,EAAE,CAAC;IACjBC,UAAU,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;EACvD,CAAC,CACF;EAED,MAAMU,iBAAiB,GAAG,CACxB;IACEvB,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,eAAe;IACrBK,KAAK,EAAE,mBAAmB;IAC1BN,OAAO,EAAE,gGAAgG;IACzG2D,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAChDW,IAAI,EAAE,KAAK;IACXpB,QAAQ,EAAE,MAAM;IAChB1C,IAAI,EAAE;MAAEuC,IAAI,EAAE,aAAa;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC3GiB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,WAAW;MAAEC,OAAO,EAAE;IAAU,CAAC,EAC1C;MAAED,KAAK,EAAE,QAAQ;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE3C,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,mBAAmB;IACzBK,KAAK,EAAE,sBAAsB;IAC7BN,OAAO,EAAE,oGAAoG;IAC7G2D,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDW,IAAI,EAAE,KAAK;IACXpB,QAAQ,EAAE,QAAQ;IAClB1C,IAAI,EAAE,IAAI;IACV+D,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,cAAc;MAAEC,OAAO,EAAE;IAAU,CAAC;EAEjD,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,iBAAiB;IACvBK,KAAK,EAAE,oBAAoB;IAC3BN,OAAO,EAAE,iFAAiF;IAC1F2D,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDW,IAAI,EAAE,IAAI;IACVpB,QAAQ,EAAE,KAAK;IACf1C,IAAI,EAAE;MAAEuC,IAAI,EAAE,eAAe;MAAEO,MAAM,EAAE;IAAqE,CAAC;IAC7GiB,OAAO,EAAE,CACP;MAAEC,KAAK,EAAE,OAAO;MAAEC,OAAO,EAAE;IAAU,CAAC;EAE1C,CAAC,EACD;IACE3B,EAAE,EAAE,CAAC;IACL/C,IAAI,EAAE,cAAc;IACpBK,KAAK,EAAE,oBAAoB;IAC3BN,OAAO,EAAE,gHAAgH;IACzH2D,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IACpDW,IAAI,EAAE,IAAI;IACVpB,QAAQ,EAAE,QAAQ;IAClB1C,IAAI,EAAE,IAAI;IACV+D,OAAO,EAAE;EACX,CAAC,CACF;;EAED;EACA,MAAMG,UAAU,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACvB,MAAMC,QAAQ,GAAGzH,aAAa,IAAI,CAAC,CAAC;IAEpC,QAAQN,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,CACL;UAAEoC,KAAK,EAAE,eAAe;UAAE4F,KAAK,EAAE,EAAArB,qBAAA,GAAAoB,QAAQ,CAACE,mBAAmB,cAAAtB,qBAAA,uBAA5BA,qBAAA,CAA8BuB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAElG,KAAK,EAAE,iBAAiB;UAAE4F,KAAK,EAAE,EAAApB,qBAAA,GAAAmB,QAAQ,CAACQ,cAAc,cAAA3B,qBAAA,uBAAvBA,qBAAA,CAAyBsB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAElG,KAAK,EAAE,cAAc;UAAE4F,KAAK,EAAE,EAAAnB,qBAAA,GAAAkB,QAAQ,CAACS,aAAa,cAAA3B,qBAAA,uBAAtBA,qBAAA,CAAwBqB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAElG,KAAK,EAAE,iBAAiB;UAAE4F,KAAK,EAAE,EAAAlB,qBAAA,GAAAiB,QAAQ,CAACU,eAAe,cAAA3B,qBAAA,wBAAAC,sBAAA,GAAxBD,qBAAA,CAA0B4B,MAAM,cAAA3B,sBAAA,uBAAhCA,sBAAA,CAAkCmB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,OAAO;QACV,OAAO,CACL;UAAElG,KAAK,EAAE,iBAAiB;UAAE4F,KAAK,EAAE,EAAAhB,sBAAA,GAAAe,QAAQ,CAACQ,cAAc,cAAAvB,sBAAA,uBAAvBA,sBAAA,CAAyBkB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC1J;UAAElG,KAAK,EAAE,cAAc;UAAE4F,KAAK,EAAE,EAAAf,sBAAA,GAAAc,QAAQ,CAACS,aAAa,cAAAvB,sBAAA,uBAAtBA,sBAAA,CAAwBiB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAElG,KAAK,EAAE,eAAe;UAAE4F,KAAK,EAAE,EAAAd,sBAAA,GAAAa,QAAQ,CAACE,mBAAmB,cAAAf,sBAAA,uBAA5BA,sBAAA,CAA8BgB,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAElG,KAAK,EAAE,iBAAiB;UAAE4F,KAAK,EAAE,EAAAb,sBAAA,GAAAY,QAAQ,CAACU,eAAe,cAAAtB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BuB,MAAM,cAAAtB,sBAAA,uBAAhCA,sBAAA,CAAkCc,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAElG,KAAK,EAAE,aAAa;UAAE4F,KAAK,EAAE,EAAAX,sBAAA,GAAAU,QAAQ,CAACQ,cAAc,cAAAlB,sBAAA,uBAAvBA,sBAAA,CAAyBa,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EACtJ;UAAElG,KAAK,EAAE,eAAe;UAAE4F,KAAK,EAAE,EAAAV,sBAAA,GAAAS,QAAQ,CAACE,mBAAmB,cAAAX,sBAAA,uBAA5BA,sBAAA,CAA8BY,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC3J;UAAElG,KAAK,EAAE,cAAc;UAAE4F,KAAK,EAAE,EAAAT,sBAAA,GAAAQ,QAAQ,CAACS,aAAa,cAAAjB,sBAAA,uBAAtBA,sBAAA,CAAwBW,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EAChJ;UAAElG,KAAK,EAAE,iBAAiB;UAAE4F,KAAK,EAAE,EAAAR,sBAAA,GAAAO,QAAQ,CAACU,eAAe,cAAAjB,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0BkB,MAAM,cAAAjB,sBAAA,uBAAhCA,sBAAA,CAAkCS,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH,KAAK,QAAQ;QACX,OAAO,CACL;UAAElG,KAAK,EAAE,iBAAiB;UAAE4F,KAAK,EAAE,EAAAN,sBAAA,GAAAK,QAAQ,CAACQ,cAAc,cAAAb,sBAAA,uBAAvBA,sBAAA,CAAyBQ,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,KAAK;UAAEC,KAAK,EAAE;QAAU,CAAC,EACnJ;UAAElG,KAAK,EAAE,eAAe;UAAE4F,KAAK,EAAE,EAAAL,sBAAA,GAAAI,QAAQ,CAACE,mBAAmB,cAAAN,sBAAA,uBAA5BA,sBAAA,CAA8BO,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAS,CAAC,EAC1J;UAAElG,KAAK,EAAE,cAAc;UAAE4F,KAAK,EAAE,EAAAJ,sBAAA,GAAAG,QAAQ,CAACS,aAAa,cAAAZ,sBAAA,uBAAtBA,sBAAA,CAAwBM,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAU,CAAC,EACjJ;UAAElG,KAAK,EAAE,gBAAgB;UAAE4F,KAAK,EAAE,EAAAH,sBAAA,GAAAE,QAAQ,CAACU,eAAe,cAAAZ,sBAAA,wBAAAC,sBAAA,GAAxBD,sBAAA,CAA0Ba,MAAM,cAAAZ,sBAAA,uBAAhCA,sBAAA,CAAkCI,QAAQ,CAAC,CAAC,KAAI,GAAG;UAAEC,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,WAAW;UAAEC,KAAK,EAAE;QAAU,CAAC,CAClK;MACH;QACE,OAAO,CACL;UAAElG,KAAK,EAAE,UAAU;UAAE4F,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,YAAY;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC5G;UAAElG,KAAK,EAAE,eAAe;UAAE4F,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,EAC/G;UAAElG,KAAK,EAAE,SAAS;UAAE4F,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,OAAO;UAAEC,KAAK,EAAE;QAAS,CAAC,EACrG;UAAElG,KAAK,EAAE,UAAU;UAAE4F,KAAK,EAAE,GAAG;UAAEG,MAAM,EAAE,IAAI;UAAEC,UAAU,EAAE,SAAS;UAAEC,IAAI,EAAE,UAAU;UAAEC,KAAK,EAAE;QAAU,CAAC,CAC3G;IACL;EACF,CAAC;;EAED;EACA,MAAMK,gBAAgB,GAAG,CAACnI,QAAQ,CAACkI,MAAM,GAAG,CAAC,GAAGlI,QAAQ,GAAGqE,YAAY,EAAE+D,MAAM,CAACpD,OAAO,IAAI;IACzF,MAAMqD,aAAa,GAAGrD,OAAO,CAACT,IAAI,CAAC+D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7I,WAAW,CAAC4I,WAAW,CAAC,CAAC,CAAC,IAC/D,CAACtD,OAAO,CAACR,WAAW,IAAI,EAAE,EAAE8D,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC7I,WAAW,CAAC4I,WAAW,CAAC,CAAC,CAAC;IAClG,MAAME,aAAa,GAAG5I,WAAW,KAAK,KAAK,IACtB,CAACoF,OAAO,CAACP,MAAM,IAAI,QAAQ,EAAE6D,WAAW,CAAC,CAAC,KAAK1I,WAAW,CAAC0I,WAAW,CAAC,CAAC;IAC7F,OAAOD,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;;EAIF;EACA,MAAMC,mBAAmB,GAAGrI,aAAa,CAAC8H,MAAM,GAAG,CAAC,GAAG;IACrD3D,IAAI,EAAE,EAAAnF,qBAAA,GAAAgB,aAAa,CAAC,CAAC,CAAC,CAACsI,YAAY,cAAAtJ,qBAAA,uBAA7BA,qBAAA,CAA+BmF,IAAI,KAAI,mBAAmB;IAChEoE,MAAM,EAAE,CAAAzI,WAAW,aAAXA,WAAW,wBAAAb,kBAAA,GAAXa,WAAW,CAAEsF,KAAK,cAAAnG,kBAAA,uBAAlBA,kBAAA,CAAoBuJ,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,KAAI,aAAa;IAC1DC,IAAI,EAAE,EAAAvJ,sBAAA,GAAAc,aAAa,CAAC,CAAC,CAAC,CAACsI,YAAY,cAAApJ,sBAAA,uBAA7BA,sBAAA,CAA+BwJ,QAAQ,KAAI;EACnD,CAAC,GAAG;IACFvE,IAAI,EAAE,mBAAmB;IACzBoE,MAAM,EAAE,aAAa;IACrBE,IAAI,EAAE;EACR,CAAC;EAED,oBACE5J,OAAA;IAAKqE,SAAS,EAAC,6EAA6E;IAAAC,QAAA,gBAE1FtE,OAAA,CAACd,eAAe;MACdqB,QAAQ,EAAEA,QAAQ,CAAC8I,WAAW,CAAC,CAAE;MACjCpI,WAAW,EAAEA,WAAW,GAAG;QACzBqE,IAAI,EAAE,GAAGrE,WAAW,CAAC6I,SAAS,IAAI7I,WAAW,CAAC8I,QAAQ,EAAE;QACxDxD,KAAK,EAAEtF,WAAW,CAACsF,KAAK;QACxBV,MAAM,EAAE5E,WAAW,CAAC4E,MAAM,IAAI,2BAA2B;QACzD7C,IAAI,EAAEzC;MACR,CAAC,GAAG;QACF+E,IAAI,EAAE,YAAY;QAClBiB,KAAK,EAAE,EAAE;QACTV,MAAM,EAAE,2BAA2B;QACnC7C,IAAI,EAAEzC;MACR,CAAE;MACFiJ,mBAAmB,EAAEA;IAAoB;MAAAjF,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1C,CAAC,EAGDjD,WAAW,IAAIE,cAAc,iBAC5B3B,OAAA;MAAKqE,SAAS,EAAC,2GAA2G;MAAAC,QAAA,eACxHtE,OAAA;QAAKqE,SAAS,EAAC,oCAAoC;QAAAC,QAAA,gBACjDtE,OAAA;UAAKqE,SAAS,EAAC,eAAe;UAAAC,QAAA,eAC5BtE,OAAA;YAAKqE,SAAS,EAAC,wBAAwB;YAACM,IAAI,EAAC,MAAM;YAACE,OAAO,EAAC,WAAW;YAACD,MAAM,EAAC,cAAc;YAAAN,QAAA,eAC3FtE,OAAA;cAAM8E,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA+C;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1E,OAAA;UAAKqE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BtE,OAAA;YAAIqE,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAA2B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnF1E,OAAA;YAAKqE,SAAS,EAAC,6BAA6B;YAAAC,QAAA,eAC1CtE,OAAA;cAAAsE,QAAA,EAAI3C;YAAc;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACN1E,OAAA;YAAKqE,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBtE,OAAA;cACEkF,OAAO,EAAEA,CAAA,KAAMxD,cAAc,CAAC,KAAK,CAAE;cACrC2C,SAAS,EAAC,2GAA2G;cAAAC,QAAA,EACtH;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAGD1E,OAAA;MAAKqE,SAAS,EAAE,6CAA6C5C,WAAW,GAAG,MAAM,GAAG,OAAO,EAAG;MAAA6C,QAAA,eAC5FtE,OAAA;QAAKqE,SAAS,EAAC,qDAAqD;QAAAC,QAAA,gBAClEtE,OAAA;UAAKqE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBACtCtE,OAAA;YAAKqE,SAAS,EAAC,yBAAyB;YAAAC,QAAA,gBACtCtE,OAAA;cAAKqE,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvE1E,OAAA;cAAMqE,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EACjDrD,WAAW,GAAG,YAAYA,WAAW,CAAC6I,SAAS,GAAG,GAAG;YAAY;cAAAvF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eACN1E,OAAA;YAAKqE,SAAS,EAAC,gDAAgD;YAAAC,QAAA,gBAC7DtE,OAAA;cAAMqE,SAAS,EAAC,yEAAyE;cAAAC,QAAA,EACtF/D;YAAQ;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,EACN8E,mBAAmB,iBAClBxJ,OAAA;cAAMqE,SAAS,EAAC,gBAAgB;cAAAC,QAAA,GAAC,SAC7B,EAACkF,mBAAmB,CAAClE,IAAI;YAAA;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1E,OAAA;UAAKqE,SAAS,EAAC,gDAAgD;UAAAC,QAAA,gBAC7DtE,OAAA;YAAAsE,QAAA,GAAM,iBAAe,EAAC,CAAAzD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE2H,mBAAmB,KAAI,CAAC;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eACrE1E,OAAA;YAAAsE,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACd1E,OAAA;YAAAsE,QAAA,GAAM,YAAU,EAAC,CAAAzD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEiI,cAAc,KAAI,CAAC;UAAA;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAC3D1E,OAAA;YAAAsE,QAAA,EAAM;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACd1E,OAAA;YAAAsE,QAAA,GAAM,WAAS,EAAC,CAAAzD,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEkI,aAAa,KAAI,CAAC;UAAA;YAAAxE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1E,OAAA,CAACb,eAAe;MACdoB,QAAQ,EAAEA,QAAS;MACnByJ,cAAc,EAAEpJ,cAAe;MAC/BqJ,cAAc,EAAEvJ,cAAe;MAC/BD,WAAW,EAAEA;IAAY;MAAA8D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGF1E,OAAA;MAAKqE,SAAS,EAAC,uBAAuB;MAAAC,QAAA,gBAEpCtE,OAAA;QAAKqE,SAAS,EAAC,4DAA4D;QAAAC,QAAA,EACxE2C,UAAU,CAAC,CAAC,CAACiD,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,kBAC3BpK,OAAA;UAAiBqE,SAAS,EAAC,iBAAiB;UAACgG,KAAK,EAAE;YAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;UAAI,CAAE;UAAA9F,QAAA,eACxFtE,OAAA,CAACZ,OAAO;YACNuD,KAAK,EAAEwH,GAAG,CAACxH,KAAM;YACjB4F,KAAK,EAAE4B,GAAG,CAAC5B,KAAM;YACjBG,MAAM,EAAEyB,GAAG,CAACzB,MAAO;YACnBC,UAAU,EAAEwB,GAAG,CAACxB,UAAW;YAC3BC,IAAI,EAAEuB,GAAG,CAACvB,IAAK;YACfC,KAAK,EAAEsB,GAAG,CAACtB;UAAM;YAAAtE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC,GARM0F,KAAK;UAAA7F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1E,OAAA;QAAKqE,SAAS,EAAC,6CAA6C;QAAAC,QAAA,gBAE1DtE,OAAA;UAAKqE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,gBAEtCtE,OAAA;YAAKqE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBtE,OAAA;cAAKqE,SAAS,EAAC,mCAAmC;cAAAC,QAAA,gBAChDtE,OAAA;gBAAAsE,QAAA,gBACEtE,OAAA;kBAAIqE,SAAS,EAAC,sDAAsD;kBAAAC,QAAA,EACjE/D,QAAQ,KAAK,QAAQ,GAAG,oBAAoB,GAAG;gBAAa;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACL1E,OAAA;kBAAGqE,SAAS,EAAC,qBAAqB;kBAAAC,QAAA,EAAC;gBAAqC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACN1E,OAAA,CAAChB,IAAI;gBACHuL,EAAE,EAAC,6BAA6B;gBAChClG,SAAS,EAAC,gKAAgK;gBAAAC,QAAA,GAC3K,gBAEC,eAAAtE,OAAA;kBAAKqE,SAAS,EAAC,SAAS;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC5EtE,OAAA;oBAAM8E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAc;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAEN1E,OAAA;cAAKqE,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD4E,gBAAgB,CAACgB,GAAG,CAAC,CAACnE,OAAO,EAAEqE,KAAK,kBACnCpK,OAAA;gBAAsBqE,SAAS,EAAC,iBAAiB;gBAACgG,KAAK,EAAE;kBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;gBAAI,CAAE;gBAAA9F,QAAA,eAC7FtE,OAAA,CAACX,WAAW;kBACV0G,OAAO,EAAEA,OAAQ;kBACjBxF,QAAQ,EAAEA;gBAAS;kBAAAgE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACpB;cAAC,GAJMqB,OAAO,CAACV,EAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAKf,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,EAELwE,gBAAgB,CAACD,MAAM,KAAK,CAAC,iBAC5BjJ,OAAA;cAAKqE,SAAS,EAAC,mFAAmF;cAAAC,QAAA,gBAChGtE,OAAA;gBAAKqE,SAAS,EAAC,mFAAmF;gBAAAC,QAAA,eAChGtE,OAAA;kBAAKqE,SAAS,EAAC,wBAAwB;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAC3FtE,OAAA;oBAAM8E,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAsH;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3L;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1E,OAAA;gBAAGqE,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAsC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eAChF1E,OAAA;gBAAGqE,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAAC;cAA4C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxF,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN1E,OAAA,CAACT,YAAY;YACXgB,QAAQ,EAAEA,QAAS;YACnBiK,eAAe,EAAExG,uBAAwB;YACzCyG,aAAa,EAAEvG,iBAAkB;YACjCwG,eAAe,EAAEtG;UAAoB;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN1E,OAAA;UAAKqE,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACxBtE,OAAA,CAACV,YAAY;YAACqL,UAAU,EAAE7E,cAAe;YAACvF,QAAQ,EAAEA;UAAS;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAChE1E,OAAA,CAACN,iBAAiB;YAACkL,aAAa,EAAEhE,iBAAkB;YAACrG,QAAQ,EAAEA;UAAS;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1E,OAAA;QAAKqE,SAAS,EAAC,uCAAuC;QAAAC,QAAA,gBACpDtE,OAAA,CAACR,WAAW;UAACqL,KAAK,EAAEzE,SAAU;UAAC7F,QAAQ,EAAEA;QAAS;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrD1E,OAAA,CAACP,YAAY;UAACqL,WAAW,EAAExE,eAAgB;UAAC/F,QAAQ,EAAEA;QAAS;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN1E,OAAA,CAACH,kBAAkB;MACjBkL,MAAM,EAAElJ,iBAAkB;MAC1BmJ,OAAO,EAAE/G,wBAAyB;MAClCuG,eAAe,EAAE9G,mBAAoB;MACrCN,cAAc,EAAEzD,WAAW,CAAC0D,iBAAiB,CAAC,CAAE;MAChD4H,gBAAgB,EAAE,CAAAzB,mBAAmB,aAAnBA,mBAAmB,uBAAnBA,mBAAmB,CAAElE,IAAI,KAAI;IAAoB;MAAAf,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACxE,EAAA,CAnoBID,kBAAkB;EAAA,QACLhB,WAAW;AAAA;AAAAiM,EAAA,GADxBjL,kBAAkB;AAqoBxB,eAAeA,kBAAkB;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}