{"ast": null, "code": "import { useCollectedProps } from '../useCollectedProps.js';\nimport { useOptionalFactory } from '../useOptionalFactory.js';\nimport { useConnectDropTarget } from './connectors.js';\nimport { useDropTargetConnector } from './useDropTargetConnector.js';\nimport { useDropTargetMonitor } from './useDropTargetMonitor.js';\nimport { useRegisteredDropTarget } from './useRegisteredDropTarget.js';\n/**\n * useDropTarget Hook\n * @param spec The drop target specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrop(specArg, deps) {\n  const spec = useOptionalFactory(specArg, deps);\n  const monitor = useDropTargetMonitor();\n  const connector = useDropTargetConnector(spec.options);\n  useRegisteredDropTarget(spec, monitor, connector);\n  return [useCollectedProps(spec.collect, monitor, connector), useConnectDropTarget(connector)];\n}", "map": {"version": 3, "names": ["useCollectedProps", "useOptionalFactory", "useConnectDropTarget", "useDropTargetConnector", "useDropTargetMonitor", "useRegisteredDropTarget", "useDrop", "specArg", "deps", "spec", "monitor", "connector", "options", "collect"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd\\src\\hooks\\useDrop\\useDrop.ts"], "sourcesContent": ["import type { ConnectDropTarget } from '../../types/index.js'\nimport type { DropTargetHookSpec, FactoryOrInstance } from '../types.js'\nimport { useCollectedProps } from '../useCollectedProps.js'\nimport { useOptionalFactory } from '../useOptionalFactory.js'\nimport { useConnectDropTarget } from './connectors.js'\nimport { useDropTargetConnector } from './useDropTargetConnector.js'\nimport { useDropTargetMonitor } from './useDropTargetMonitor.js'\nimport { useRegisteredDropTarget } from './useRegisteredDropTarget.js'\n\n/**\n * useDropTarget Hook\n * @param spec The drop target specification (object or function, function preferred)\n * @param deps The memoization deps array to use when evaluating spec changes\n */\nexport function useDrop<\n\tDragObject = unknown,\n\tDropResult = unknown,\n\tCollectedProps = unknown,\n>(\n\tspecArg: FactoryOrInstance<\n\t\tDropTargetHookSpec<DragObject, DropResult, CollectedProps>\n\t>,\n\tdeps?: unknown[],\n): [CollectedProps, ConnectDropTarget] {\n\tconst spec = useOptionalFactory(specArg, deps)\n\tconst monitor = useDropTargetMonitor<DragObject, DropResult>()\n\tconst connector = useDropTargetConnector(spec.options)\n\tuseRegisteredDropTarget(spec, monitor, connector)\n\n\treturn [\n\t\tuseCollectedProps(spec.collect, monitor, connector),\n\t\tuseConnectDropTarget(connector),\n\t]\n}\n"], "mappings": "AAEA,SAASA,iBAAiB,QAAQ,yBAAyB;AAC3D,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,oBAAoB,QAAQ,iBAAiB;AACtD,SAASC,sBAAsB,QAAQ,6BAA6B;AACpE,SAASC,oBAAoB,QAAQ,2BAA2B;AAChE,SAASC,uBAAuB,QAAQ,8BAA8B;AAEtE;;;;;AAKA,OAAO,SAASC,OAAOA,CAKtBC,OAEC,EACDC,IAAgB,EACsB;EACtC,MAAMC,IAAI,GAAGR,kBAAkB,CAACM,OAAO,EAAEC,IAAI,CAAC;EAC9C,MAAME,OAAO,GAAGN,oBAAoB,EAA0B;EAC9D,MAAMO,SAAS,GAAGR,sBAAsB,CAACM,IAAI,CAACG,OAAO,CAAC;EACtDP,uBAAuB,CAACI,IAAI,EAAEC,OAAO,EAAEC,SAAS,CAAC;EAEjD,OAAO,CACNX,iBAAiB,CAACS,IAAI,CAACI,OAAO,EAAEH,OAAO,EAAEC,SAAS,CAAC,EACnDT,oBAAoB,CAACS,SAAS,CAAC,CAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}