"""
Card management endpoints
"""
from typing import List
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, delete
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.core.exceptions import ResourceNotFoundError, InsufficientPermissionsError
from app.models.user import User
from app.models.organization import OrganizationMember
from app.models.column import Column
from app.models.board import Board
from app.models.card import Card, CardAssignment
from app.models.comment import Comment
from app.models.attachment import Attachment
from app.schemas.card import (
    CardCreate, CardUpdate, CardResponse, CardMove, CardAssignmentResponse,
    CommentCreate, CommentUpdate, CommentResponse,
    AttachmentResponse, ActivityResponse
)

router = APIRouter()


@router.get("/{card_id}", response_model=CardResponse)
async def get_card(
    card_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get card by ID"""
    result = await db.execute(
        select(Card)
        .options(
            selectinload(Card.column)
            .selectinload(Column.board)
            .selectinload(Board.project),
            selectinload(Card.assignments).selectinload(CardAssignment.user)
        )
        .where(Card.id == card_id)
    )
    card = result.scalar_one_or_none()
    if not card:
        raise ResourceNotFoundError("Card not found")
    
    # Check access
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == card.column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    if not org_member_result.scalar_one_or_none():
        raise InsufficientPermissionsError("Access denied")
    
    # Format response with assignments
    card_response = CardResponse.from_orm(card)
    if card.assignments:
        card_response.assignments = []
        for assignment in card.assignments:
            assignment_data = CardAssignmentResponse.from_orm(assignment)
            assignment_data.user = {
                "id": str(assignment.user.id),
                "email": assignment.user.email,
                "first_name": assignment.user.first_name,
                "last_name": assignment.user.last_name,
                "avatar_url": assignment.user.avatar_url
            }
            card_response.assignments.append(assignment_data)
    
    return card_response


@router.put("/{card_id}", response_model=CardResponse)
async def update_card(
    card_id: str,
    card_data: CardUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update card"""
    result = await db.execute(
        select(Card)
        .options(
            selectinload(Card.column)
            .selectinload(Column.board)
            .selectinload(Board.project)
        )
        .where(Card.id == card_id)
    )
    card = result.scalar_one_or_none()
    if not card:
        raise ResourceNotFoundError("Card not found")
    
    # Check access and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == card.column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    # Update fields
    if card_data.title is not None:
        card.title = card_data.title
    if card_data.description is not None:
        card.description = card_data.description
    if card_data.position is not None:
        card.position = card_data.position
    if card_data.priority is not None:
        card.priority = card_data.priority
    if card_data.due_date is not None:
        card.due_date = card_data.due_date
    
    # Handle assignments
    if card_data.assigned_to is not None:
        # Remove existing assignments
        await db.execute(delete(CardAssignment).where(CardAssignment.card_id == card_id))
        
        # Add new assignments
        for user_id in card_data.assigned_to:
            assignment = CardAssignment(
                card_id=card_id,
                user_id=user_id,
                assigned_by=current_user.id
            )
            db.add(assignment)
    
    await db.commit()
    await db.refresh(card)
    
    return CardResponse.from_orm(card)


@router.delete("/{card_id}")
async def delete_card(
    card_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete card"""
    result = await db.execute(
        select(Card)
        .options(
            selectinload(Card.column)
            .selectinload(Column.board)
            .selectinload(Board.project)
        )
        .where(Card.id == card_id)
    )
    card = result.scalar_one_or_none()
    if not card:
        raise ResourceNotFoundError("Card not found")
    
    # Check access and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == card.column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    await db.delete(card)
    await db.commit()
    
    return {"success": True, "message": "Card deleted successfully"}


@router.put("/{card_id}/move")
async def move_card(
    card_id: str,
    move_data: CardMove,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Move card to different column"""
    result = await db.execute(
        select(Card)
        .options(
            selectinload(Card.column)
            .selectinload(Column.board)
            .selectinload(Board.project)
        )
        .where(Card.id == card_id)
    )
    card = result.scalar_one_or_none()
    if not card:
        raise ResourceNotFoundError("Card not found")
    
    # Check access and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == card.column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    # Verify target column exists and belongs to same board
    target_column_result = await db.execute(
        select(Column).where(
            Column.id == move_data.target_column_id,
            Column.board_id == card.column.board_id
        )
    )
    if not target_column_result.scalar_one_or_none():
        raise ResourceNotFoundError("Target column not found or not in same board")
    
    # Move card
    card.column_id = move_data.target_column_id
    card.position = move_data.position
    
    await db.commit()
    
    return {"success": True, "message": "Card moved successfully"}


@router.get("/{column_id}/cards", response_model=List[CardResponse])
async def get_column_cards(
    column_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get cards for a column"""
    # Check column access
    column_result = await db.execute(
        select(Column)
        .options(
            selectinload(Column.board)
            .selectinload(Board.project)
        )
        .where(Column.id == column_id)
    )
    column = column_result.scalar_one_or_none()
    if not column:
        raise ResourceNotFoundError("Column not found")
    
    # Check organization membership
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    if not org_member_result.scalar_one_or_none():
        raise InsufficientPermissionsError("Access denied")
    
    # Get cards
    result = await db.execute(
        select(Card)
        .options(selectinload(Card.assignments).selectinload(CardAssignment.user))
        .where(Card.column_id == column_id)
        .order_by(Card.position)
    )
    cards = result.scalars().all()
    
    # Format response
    response = []
    for card in cards:
        card_response = CardResponse.from_orm(card)
        if card.assignments:
            card_response.assignments = []
            for assignment in card.assignments:
                assignment_data = CardAssignmentResponse.from_orm(assignment)
                assignment_data.user = {
                    "id": str(assignment.user.id),
                    "email": assignment.user.email,
                    "first_name": assignment.user.first_name,
                    "last_name": assignment.user.last_name,
                    "avatar_url": assignment.user.avatar_url
                }
                card_response.assignments.append(assignment_data)
        response.append(card_response)
    
    return response


@router.post("/{column_id}/cards", response_model=CardResponse)
async def create_card(
    column_id: str,
    card_data: CardCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new card"""
    # Check column access
    column_result = await db.execute(
        select(Column)
        .options(
            selectinload(Column.board)
            .selectinload(Board.project)
        )
        .where(Column.id == column_id)
    )
    column = column_result.scalar_one_or_none()
    if not column:
        raise ResourceNotFoundError("Column not found")
    
    # Check organization membership and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    # Create card
    card = Card(
        column_id=column_id,
        title=card_data.title,
        description=card_data.description,
        position=card_data.position,
        priority=card_data.priority,
        due_date=card_data.due_date,
        created_by=current_user.id
    )
    
    db.add(card)
    await db.flush()  # Get the ID
    
    # Add assignments if provided
    if card_data.assigned_to:
        for user_id in card_data.assigned_to:
            assignment = CardAssignment(
                card_id=card.id,
                user_id=user_id,
                assigned_by=current_user.id
            )
            db.add(assignment)
    
    await db.commit()
    await db.refresh(card)
    
    return CardResponse.from_orm(card)
