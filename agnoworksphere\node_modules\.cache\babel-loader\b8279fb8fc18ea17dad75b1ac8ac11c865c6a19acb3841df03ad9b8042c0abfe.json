{"ast": null, "code": "import { invariant } from '@react-dnd/invariant';\nimport { useMemo } from 'react';\nexport function useDragType(spec) {\n  return useMemo(() => {\n    const result = spec.type;\n    invariant(result != null, 'spec.type must be defined');\n    return result;\n  }, [spec]);\n}", "map": {"version": 3, "names": ["invariant", "useMemo", "useDragType", "spec", "result", "type"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd\\src\\hooks\\useDrag\\useDragType.ts"], "sourcesContent": ["import { invariant } from '@react-dnd/invariant'\nimport type { Identifier } from 'dnd-core'\nimport { useMemo } from 'react'\n\nimport type { DragSourceHookSpec } from '../types.js'\n\nexport function useDragType(\n\tspec: DragSourceHookSpec<any, any, any>,\n): Identifier {\n\treturn useMemo(() => {\n\t\tconst result: Identifier = spec.type\n\t\tinvariant(result != null, 'spec.type must be defined')\n\t\treturn result\n\t}, [spec])\n}\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,sBAAsB;AAEhD,SAASC,OAAO,QAAQ,OAAO;AAI/B,OAAO,SAASC,WAAWA,CAC1BC,IAAuC,EAC1B;EACb,OAAOF,OAAO,CAAC,MAAM;IACpB,MAAMG,MAAM,GAAeD,IAAI,CAACE,IAAI;IACpCL,SAAS,CAACI,MAAM,IAAI,IAAI,EAAE,2BAA2B,CAAC;IACtD,OAAOA,MAAM;GACb,EAAE,CAACD,IAAI,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}