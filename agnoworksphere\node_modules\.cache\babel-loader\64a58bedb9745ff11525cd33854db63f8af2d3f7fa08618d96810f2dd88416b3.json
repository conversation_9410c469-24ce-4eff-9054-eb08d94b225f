{"ast": null, "code": "import { addMilliseconds } from \"./addMilliseconds.js\";\n\n/**\n * The {@link subMilliseconds} function options.\n */\n\n/**\n * Subtract the specified number of milliseconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the milliseconds subtracted\n */\nexport function subMilliseconds(date, amount, options) {\n  return addMilliseconds(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMilliseconds;", "map": {"version": 3, "names": ["addMilliseconds", "subMilliseconds", "date", "amount", "options"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/node_modules/date-fns/subMilliseconds.js"], "sourcesContent": ["import { addMilliseconds } from \"./addMilliseconds.js\";\n\n/**\n * The {@link subMilliseconds} function options.\n */\n\n/**\n * Subtract the specified number of milliseconds from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the milliseconds subtracted\n */\nexport function subMilliseconds(date, amount, options) {\n  return addMilliseconds(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMilliseconds;\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,sBAAsB;;AAEtD;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACrD,OAAOJ,eAAe,CAACE,IAAI,EAAE,CAACC,MAAM,EAAEC,OAAO,CAAC;AAChD;;AAEA;AACA,eAAeH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}