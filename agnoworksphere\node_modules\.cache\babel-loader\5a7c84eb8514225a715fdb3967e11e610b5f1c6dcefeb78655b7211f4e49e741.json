{"ast": null, "code": "import { registerTarget } from '../../internals/index.js';\nimport { useDragDropManager } from '../useDragDropManager.js';\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js';\nimport { useAccept } from './useAccept.js';\nimport { useDropTarget } from './useDropTarget.js';\nexport function useRegisteredDropTarget(spec, monitor, connector) {\n  const manager = useDragDropManager();\n  const dropTarget = useDropTarget(spec, monitor);\n  const accept = useAccept(spec);\n  useIsomorphicLayoutEffect(function registerDropTarget() {\n    const [handlerId, unregister] = registerTarget(accept, dropTarget, manager);\n    monitor.receiveHandlerId(handlerId);\n    connector.receiveHandlerId(handlerId);\n    return unregister;\n  }, [manager, monitor, dropTarget, connector, accept.map(a => a.toString()).join('|')]);\n}", "map": {"version": 3, "names": ["registerTarget", "useDragDropManager", "useIsomorphicLayoutEffect", "useAccept", "useDropTarget", "useRegisteredDropTarget", "spec", "monitor", "connector", "manager", "drop<PERSON>ar<PERSON>", "accept", "registerDropTarget", "handlerId", "unregister", "receiveHandlerId", "map", "a", "toString", "join"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd\\src\\hooks\\useDrop\\useRegisteredDropTarget.ts"], "sourcesContent": ["import type { TargetConnector } from '../../internals/index.js'\nimport { registerTarget } from '../../internals/index.js'\nimport type { DropTargetMonitor } from '../../types/index.js'\nimport type { DropTargetHookSpec } from '../types.js'\nimport { useDragDropManager } from '../useDragDropManager.js'\nimport { useIsomorphicLayoutEffect } from '../useIsomorphicLayoutEffect.js'\nimport { useAccept } from './useAccept.js'\nimport { useDropTarget } from './useDropTarget.js'\n\nexport function useRegisteredDropTarget<O, R, P>(\n\tspec: DropTargetHookSpec<O, R, P>,\n\tmonitor: DropTargetMonitor<O, R>,\n\tconnector: TargetConnector,\n): void {\n\tconst manager = useDragDropManager()\n\tconst dropTarget = useDropTarget(spec, monitor)\n\tconst accept = useAccept(spec)\n\n\tuseIsomorphicLayoutEffect(\n\t\tfunction registerDropTarget() {\n\t\t\tconst [handlerId, unregister] = registerTarget(\n\t\t\t\taccept,\n\t\t\t\tdropTarget,\n\t\t\t\tmanager,\n\t\t\t)\n\t\t\tmonitor.receiveHandlerId(handlerId)\n\t\t\tconnector.receiveHandlerId(handlerId)\n\t\t\treturn unregister\n\t\t},\n\t\t[\n\t\t\tmanager,\n\t\t\tmonitor,\n\t\t\tdropTarget,\n\t\t\tconnector,\n\t\t\taccept.map((a) => a.toString()).join('|'),\n\t\t],\n\t)\n}\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,0BAA0B;AAGzD,SAASC,kBAAkB,QAAQ,0BAA0B;AAC7D,SAASC,yBAAyB,QAAQ,iCAAiC;AAC3E,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,aAAa,QAAQ,oBAAoB;AAElD,OAAO,SAASC,uBAAuBA,CACtCC,IAAiC,EACjCC,OAAgC,EAChCC,SAA0B,EACnB;EACP,MAAMC,OAAO,GAAGR,kBAAkB,EAAE;EACpC,MAAMS,UAAU,GAAGN,aAAa,CAACE,IAAI,EAAEC,OAAO,CAAC;EAC/C,MAAMI,MAAM,GAAGR,SAAS,CAACG,IAAI,CAAC;EAE9BJ,yBAAyB,CACxB,SAASU,kBAAkBA,CAAA,EAAG;IAC7B,MAAM,CAACC,SAAS,EAAEC,UAAU,CAAC,GAAGd,cAAc,CAC7CW,MAAM,EACND,UAAU,EACVD,OAAO,CACP;IACDF,OAAO,CAACQ,gBAAgB,CAACF,SAAS,CAAC;IACnCL,SAAS,CAACO,gBAAgB,CAACF,SAAS,CAAC;IACrC,OAAOC,UAAU;GACjB,EACD,CACCL,OAAO,EACPF,OAAO,EACPG,UAAU,EACVF,SAAS,EACTG,MAAM,CAACK,GAAG,CAAEC,CAAC,IAAKA,CAAC,CAACC,QAAQ,EAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CACzC,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}