{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\PM\\\\agnoworksphere\\\\src\\\\pages\\\\role-based-dashboard\\\\components\\\\QuickActions.jsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Button from '../../../components/ui/Button';\nimport Icon from '../../../components/AppIcon';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst QuickActions = ({\n  userRole,\n  onCreateProject,\n  onInviteMembers,\n  onManageUsers\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const getActionsForRole = () => {\n    switch (userRole) {\n      case 'Owner':\n        return [{\n          label: 'Create Project',\n          icon: 'FolderPlus',\n          variant: 'default',\n          action: 'create_project'\n        }, {\n          label: 'Manage Users',\n          icon: 'Users',\n          variant: 'outline',\n          action: 'manage_users'\n        }, {\n          label: 'View Analytics',\n          icon: 'BarChart3',\n          variant: 'outline',\n          action: 'view_analytics'\n        }, {\n          label: 'Organization Settings',\n          icon: 'Settings',\n          variant: 'outline',\n          action: 'org_settings'\n        }, {\n          label: 'Billing & Plans',\n          icon: 'CreditCard',\n          variant: 'outline',\n          action: 'billing'\n        }, {\n          label: 'Export Data',\n          icon: 'Download',\n          variant: 'ghost',\n          action: 'export_data'\n        }];\n      case 'Admin':\n        return [{\n          label: 'Create Project',\n          icon: 'FolderPlus',\n          variant: 'default',\n          action: 'create_project'\n        }, {\n          label: 'Manage Team',\n          icon: 'Users',\n          variant: 'outline',\n          action: 'manage_team'\n        }, {\n          label: 'View Reports',\n          icon: 'FileText',\n          variant: 'outline',\n          action: 'view_reports'\n        }, {\n          label: 'Project Settings',\n          icon: 'Settings',\n          variant: 'outline',\n          action: 'project_settings'\n        }, {\n          label: 'Invite Members',\n          icon: 'UserPlus',\n          variant: 'outline',\n          action: 'invite_members'\n        }, {\n          label: 'Bulk Actions',\n          icon: 'List',\n          variant: 'ghost',\n          action: 'bulk_actions'\n        }];\n      case 'Member':\n        return [{\n          label: 'Create Task',\n          icon: 'Plus',\n          variant: 'default',\n          action: 'create_task'\n        }, {\n          label: 'My Tasks',\n          icon: 'CheckSquare',\n          variant: 'outline',\n          action: 'my_tasks'\n        }, {\n          label: 'Join Project',\n          icon: 'UserPlus',\n          variant: 'outline',\n          action: 'join_project'\n        }, {\n          label: 'Time Tracking',\n          icon: 'Clock',\n          variant: 'outline',\n          action: 'time_tracking'\n        }, {\n          label: 'Upload Files',\n          icon: 'Upload',\n          variant: 'outline',\n          action: 'upload_files'\n        }, {\n          label: 'Calendar View',\n          icon: 'Calendar',\n          variant: 'ghost',\n          action: 'calendar_view'\n        }];\n      case 'Viewer':\n        return [{\n          label: 'View Projects',\n          icon: 'Eye',\n          variant: 'default',\n          action: 'view_projects'\n        }, {\n          label: 'Download Reports',\n          icon: 'Download',\n          variant: 'outline',\n          action: 'download_reports'\n        }, {\n          label: 'Export Data',\n          icon: 'FileText',\n          variant: 'outline',\n          action: 'export_data'\n        }, {\n          label: 'Print Dashboard',\n          icon: 'Printer',\n          variant: 'outline',\n          action: 'print_dashboard'\n        }, {\n          label: 'Share Link',\n          icon: 'Share',\n          variant: 'ghost',\n          action: 'share_link'\n        }, {\n          label: 'Subscribe Updates',\n          icon: 'Bell',\n          variant: 'ghost',\n          action: 'subscribe_updates'\n        }];\n      default:\n        return [];\n    }\n  };\n  const handleAction = actionType => {\n    console.log(`Executing action: ${actionType} for role: ${userRole}`);\n    // Action handlers would be implemented here\n  };\n  const actions = getActionsForRole();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-lg border border-border p-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-lg font-semibold text-text-primary\",\n        children: \"Quick Actions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center gap-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-2 h-2 bg-primary rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-text-secondary font-medium\",\n          children: userRole\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 68,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\",\n      children: actions.map((action, index) => /*#__PURE__*/_jsxDEV(Button, {\n        variant: action.variant,\n        onClick: () => handleAction(action.action),\n        iconName: action.icon,\n        iconPosition: \"left\",\n        className: \"justify-start h-auto py-3 px-4\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-start\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"font-medium\",\n            children: action.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 76,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 p-4 bg-muted rounded-lg\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-start gap-3\",\n        children: [/*#__PURE__*/_jsxDEV(Icon, {\n          name: \"Lightbulb\",\n          size: 16,\n          className: \"text-accent mt-0.5\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-sm font-medium text-text-primary mb-1\",\n            children: [userRole, \" Tips\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-text-secondary\",\n            children: [userRole === 'Owner' && \"You have full access to all features. Consider setting up automated reports and user permissions.\", userRole === 'Admin' && \"You can manage projects and team members. Use bulk actions to save time on repetitive tasks.\", userRole === 'Member' && \"Focus on your assigned tasks and collaborate with team members. Use time tracking for better productivity.\", userRole === 'Viewer' && \"You have read-only access. Export data and reports to share insights with stakeholders.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 97,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 63,\n    columnNumber: 5\n  }, this);\n};\n_s(QuickActions, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = QuickActions;\nexport default QuickActions;\nvar _c;\n$RefreshReg$(_c, \"QuickActions\");", "map": {"version": 3, "names": ["React", "useNavigate", "<PERSON><PERSON>", "Icon", "jsxDEV", "_jsxDEV", "QuickActions", "userRole", "onCreateProject", "onInviteMembers", "onManageUsers", "_s", "navigate", "getActionsForRole", "label", "icon", "variant", "action", "handleAction", "actionType", "console", "log", "actions", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "index", "onClick", "iconName", "iconPosition", "name", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/PM/agnoworksphere/src/pages/role-based-dashboard/components/QuickActions.jsx"], "sourcesContent": ["import React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport Button from '../../../components/ui/Button';\nimport Icon from '../../../components/AppIcon';\n\nconst QuickActions = ({ userRole, onCreateProject, onInviteMembers, onManageUsers }) => {\n  const navigate = useNavigate();\n  const getActionsForRole = () => {\n    switch (userRole) {\n      case 'Owner':\n        return [\n          { label: 'Create Project', icon: 'FolderPlus', variant: 'default', action: 'create_project' },\n          { label: 'Manage Users', icon: 'Users', variant: 'outline', action: 'manage_users' },\n          { label: 'View Analytics', icon: 'BarChart3', variant: 'outline', action: 'view_analytics' },\n          { label: 'Organization Settings', icon: 'Settings', variant: 'outline', action: 'org_settings' },\n          { label: 'Billing & Plans', icon: 'CreditCard', variant: 'outline', action: 'billing' },\n          { label: 'Export Data', icon: 'Download', variant: 'ghost', action: 'export_data' }\n        ];\n      \n      case 'Admin':\n        return [\n          { label: 'Create Project', icon: 'FolderPlus', variant: 'default', action: 'create_project' },\n          { label: 'Manage Team', icon: 'Users', variant: 'outline', action: 'manage_team' },\n          { label: 'View Reports', icon: 'FileText', variant: 'outline', action: 'view_reports' },\n          { label: 'Project Settings', icon: 'Settings', variant: 'outline', action: 'project_settings' },\n          { label: 'Invite Members', icon: 'UserPlus', variant: 'outline', action: 'invite_members' },\n          { label: 'Bulk Actions', icon: 'List', variant: 'ghost', action: 'bulk_actions' }\n        ];\n      \n      case 'Member':\n        return [\n          { label: 'Create Task', icon: 'Plus', variant: 'default', action: 'create_task' },\n          { label: 'My Tasks', icon: 'CheckSquare', variant: 'outline', action: 'my_tasks' },\n          { label: 'Join Project', icon: 'UserPlus', variant: 'outline', action: 'join_project' },\n          { label: 'Time Tracking', icon: 'Clock', variant: 'outline', action: 'time_tracking' },\n          { label: 'Upload Files', icon: 'Upload', variant: 'outline', action: 'upload_files' },\n          { label: 'Calendar View', icon: 'Calendar', variant: 'ghost', action: 'calendar_view' }\n        ];\n      \n      case 'Viewer':\n        return [\n          { label: 'View Projects', icon: 'Eye', variant: 'default', action: 'view_projects' },\n          { label: 'Download Reports', icon: 'Download', variant: 'outline', action: 'download_reports' },\n          { label: 'Export Data', icon: 'FileText', variant: 'outline', action: 'export_data' },\n          { label: 'Print Dashboard', icon: 'Printer', variant: 'outline', action: 'print_dashboard' },\n          { label: 'Share Link', icon: 'Share', variant: 'ghost', action: 'share_link' },\n          { label: 'Subscribe Updates', icon: 'Bell', variant: 'ghost', action: 'subscribe_updates' }\n        ];\n      \n      default:\n        return [];\n    }\n  };\n\n  const handleAction = (actionType) => {\n    console.log(`Executing action: ${actionType} for role: ${userRole}`);\n    // Action handlers would be implemented here\n  };\n\n  const actions = getActionsForRole();\n\n  return (\n    <div className=\"bg-white rounded-lg border border-border p-6\">\n      <div className=\"flex items-center justify-between mb-6\">\n        <h3 className=\"text-lg font-semibold text-text-primary\">\n          Quick Actions\n        </h3>\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-2 h-2 bg-primary rounded-full\"></div>\n          <span className=\"text-xs text-text-secondary font-medium\">\n            {userRole}\n          </span>\n        </div>\n      </div>\n\n      <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\">\n        {actions.map((action, index) => (\n          <Button\n            key={index}\n            variant={action.variant}\n            onClick={() => handleAction(action.action)}\n            iconName={action.icon}\n            iconPosition=\"left\"\n            className=\"justify-start h-auto py-3 px-4\"\n          >\n            <div className=\"flex flex-col items-start\">\n              <span className=\"font-medium\">{action.label}</span>\n            </div>\n          </Button>\n        ))}\n      </div>\n\n      {/* Role-specific tips */}\n      <div className=\"mt-6 p-4 bg-muted rounded-lg\">\n        <div className=\"flex items-start gap-3\">\n          <Icon name=\"Lightbulb\" size={16} className=\"text-accent mt-0.5\" />\n          <div>\n            <h4 className=\"text-sm font-medium text-text-primary mb-1\">\n              {userRole} Tips\n            </h4>\n            <p className=\"text-xs text-text-secondary\">\n              {userRole === 'Owner' && \"You have full access to all features. Consider setting up automated reports and user permissions.\"}\n              {userRole === 'Admin' && \"You can manage projects and team members. Use bulk actions to save time on repetitive tasks.\"}\n              {userRole === 'Member' && \"Focus on your assigned tasks and collaborate with team members. Use time tracking for better productivity.\"}\n              {userRole === 'Viewer' && \"You have read-only access. Export data and reports to share insights with stakeholders.\"}\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default QuickActions;"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,MAAM,MAAM,+BAA+B;AAClD,OAAOC,IAAI,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,eAAe;EAAEC,eAAe;EAAEC;AAAc,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQN,QAAQ;MACd,KAAK,OAAO;QACV,OAAO,CACL;UAAEO,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,YAAY;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC7F;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACpF;UAAEH,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,WAAW;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC5F;UAAEH,KAAK,EAAE,uBAAuB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EAChG;UAAEH,KAAK,EAAE,iBAAiB;UAAEC,IAAI,EAAE,YAAY;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAU,CAAC,EACvF;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAc,CAAC,CACpF;MAEH,KAAK,OAAO;QACV,OAAO,CACL;UAAEH,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,YAAY;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC7F;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAc,CAAC,EAClF;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACvF;UAAEH,KAAK,EAAE,kBAAkB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAmB,CAAC,EAC/F;UAAEH,KAAK,EAAE,gBAAgB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAiB,CAAC,EAC3F;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAe,CAAC,CAClF;MAEH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAc,CAAC,EACjF;UAAEH,KAAK,EAAE,UAAU;UAAEC,IAAI,EAAE,aAAa;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAW,CAAC,EAClF;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACvF;UAAEH,KAAK,EAAE,eAAe;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAgB,CAAC,EACtF;UAAEH,KAAK,EAAE,cAAc;UAAEC,IAAI,EAAE,QAAQ;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAe,CAAC,EACrF;UAAEH,KAAK,EAAE,eAAe;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAgB,CAAC,CACxF;MAEH,KAAK,QAAQ;QACX,OAAO,CACL;UAAEH,KAAK,EAAE,eAAe;UAAEC,IAAI,EAAE,KAAK;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAgB,CAAC,EACpF;UAAEH,KAAK,EAAE,kBAAkB;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAmB,CAAC,EAC/F;UAAEH,KAAK,EAAE,aAAa;UAAEC,IAAI,EAAE,UAAU;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAc,CAAC,EACrF;UAAEH,KAAK,EAAE,iBAAiB;UAAEC,IAAI,EAAE,SAAS;UAAEC,OAAO,EAAE,SAAS;UAAEC,MAAM,EAAE;QAAkB,CAAC,EAC5F;UAAEH,KAAK,EAAE,YAAY;UAAEC,IAAI,EAAE,OAAO;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAa,CAAC,EAC9E;UAAEH,KAAK,EAAE,mBAAmB;UAAEC,IAAI,EAAE,MAAM;UAAEC,OAAO,EAAE,OAAO;UAAEC,MAAM,EAAE;QAAoB,CAAC,CAC5F;MAEH;QACE,OAAO,EAAE;IACb;EACF,CAAC;EAED,MAAMC,YAAY,GAAIC,UAAU,IAAK;IACnCC,OAAO,CAACC,GAAG,CAAC,qBAAqBF,UAAU,cAAcZ,QAAQ,EAAE,CAAC;IACpE;EACF,CAAC;EAED,MAAMe,OAAO,GAAGT,iBAAiB,CAAC,CAAC;EAEnC,oBACER,OAAA;IAAKkB,SAAS,EAAC,8CAA8C;IAAAC,QAAA,gBAC3DnB,OAAA;MAAKkB,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDnB,OAAA;QAAIkB,SAAS,EAAC,yCAAyC;QAAAC,QAAA,EAAC;MAExD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLvB,OAAA;QAAKkB,SAAS,EAAC,yBAAyB;QAAAC,QAAA,gBACtCnB,OAAA;UAAKkB,SAAS,EAAC;QAAiC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvDvB,OAAA;UAAMkB,SAAS,EAAC,yCAAyC;UAAAC,QAAA,EACtDjB;QAAQ;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAENvB,OAAA;MAAKkB,SAAS,EAAC,sDAAsD;MAAAC,QAAA,EAClEF,OAAO,CAACO,GAAG,CAAC,CAACZ,MAAM,EAAEa,KAAK,kBACzBzB,OAAA,CAACH,MAAM;QAELc,OAAO,EAAEC,MAAM,CAACD,OAAQ;QACxBe,OAAO,EAAEA,CAAA,KAAMb,YAAY,CAACD,MAAM,CAACA,MAAM,CAAE;QAC3Ce,QAAQ,EAAEf,MAAM,CAACF,IAAK;QACtBkB,YAAY,EAAC,MAAM;QACnBV,SAAS,EAAC,gCAAgC;QAAAC,QAAA,eAE1CnB,OAAA;UAAKkB,SAAS,EAAC,2BAA2B;UAAAC,QAAA,eACxCnB,OAAA;YAAMkB,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEP,MAAM,CAACH;UAAK;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD;MAAC,GATDE,KAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUJ,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CnB,OAAA;QAAKkB,SAAS,EAAC,wBAAwB;QAAAC,QAAA,gBACrCnB,OAAA,CAACF,IAAI;UAAC+B,IAAI,EAAC,WAAW;UAACC,IAAI,EAAE,EAAG;UAACZ,SAAS,EAAC;QAAoB;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClEvB,OAAA;UAAAmB,QAAA,gBACEnB,OAAA;YAAIkB,SAAS,EAAC,4CAA4C;YAAAC,QAAA,GACvDjB,QAAQ,EAAC,OACZ;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLvB,OAAA;YAAGkB,SAAS,EAAC,6BAA6B;YAAAC,QAAA,GACvCjB,QAAQ,KAAK,OAAO,IAAI,mGAAmG,EAC3HA,QAAQ,KAAK,OAAO,IAAI,8FAA8F,EACtHA,QAAQ,KAAK,QAAQ,IAAI,4GAA4G,EACrIA,QAAQ,KAAK,QAAQ,IAAI,yFAAyF;UAAA;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjB,EAAA,CA1GIL,YAAY;EAAA,QACCL,WAAW;AAAA;AAAAmC,EAAA,GADxB9B,YAAY;AA4GlB,eAAeA,YAAY;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}