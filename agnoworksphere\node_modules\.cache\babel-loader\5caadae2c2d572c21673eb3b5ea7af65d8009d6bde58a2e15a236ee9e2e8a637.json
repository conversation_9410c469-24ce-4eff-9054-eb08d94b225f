{"ast": null, "code": "import { useMemo } from 'react';\nexport function useConnectDropTarget(connector) {\n  return useMemo(() => connector.hooks.dropTarget(), [connector]);\n}", "map": {"version": 3, "names": ["useMemo", "useConnectDropTarget", "connector", "hooks", "drop<PERSON>ar<PERSON>"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\react-dnd\\src\\hooks\\useDrop\\connectors.ts"], "sourcesContent": ["import { useMemo } from 'react'\n\nimport type { TargetConnector } from '../../internals/index.js'\n\nexport function useConnectDropTarget(connector: TargetConnector) {\n\treturn useMemo(() => connector.hooks.dropTarget(), [connector])\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAI/B,OAAO,SAASC,oBAAoBA,CAACC,SAA0B,EAAE;EAChE,OAAOF,OAAO,CAAC,MAAME,SAAS,CAACC,KAAK,CAACC,UAAU,EAAE,EAAE,CAACF,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}