import React from "react";
import { BrowserRouter, Routes as RouterRoutes, Route } from "react-router-dom";
import ScrollToTop from "./components/ScrollToTop";
import ErrorBoundary from "./components/ErrorBoundary";

// Page imports
import Login from "./pages/login";
import Register from "./pages/register";
import KanbanBoard from "./pages/kanban-board";
import CardDetails from "./pages/card-details";
import TeamMembers from "./pages/team-members";
import OrganizationSettings from "./pages/organization-settings";
import OrganizationDashboard from "./pages/organization-dashboard";
import UserProfileSettings from "./pages/user-profile-settings";
import ProjectManagement from "./pages/project-management";
import RoleBasedDashboard from "./pages/role-based-dashboard";
import NotFound from "./pages/NotFound";

const Routes = () => {
  return (
    <BrowserRouter>
      <ErrorBoundary>
        <>
          <ScrollToTop />
          <RouterRoutes>
            <Route path="/" element={<OrganizationDashboard />} />
            <Route path="/dashboard" element={<OrganizationDashboard />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register />} />
            <Route path="/kanban-board" element={<KanbanBoard />} />
            <Route path="/card-details" element={<CardDetails />} />
            <Route path="/team-members" element={<TeamMembers />} />
            <Route path="/organization-settings" element={<OrganizationSettings />} />
            <Route path="/user-profile-settings" element={<UserProfileSettings />} />
            <Route path="/project-management" element={<ProjectManagement />} />
            <Route path="/role-based-dashboard" element={<RoleBasedDashboard />} />                 
            <Route path="*" element={<NotFound />} />
          </RouterRoutes>
        </>
      </ErrorBoundary>
    </BrowserRouter>
  );
};

export default Routes;
