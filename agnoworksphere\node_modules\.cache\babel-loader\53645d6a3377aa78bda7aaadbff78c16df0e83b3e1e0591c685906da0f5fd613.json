{"ast": null, "code": "/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */export function add(a, b) {\n  return {\n    x: a.x + b.x,\n    y: a.y + b.y\n  };\n}\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */\nexport function subtract(a, b) {\n  return {\n    x: a.x - b.x,\n    y: a.y - b.y\n  };\n}\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */\nexport function getSourceClientOffset(state) {\n  const {\n    clientOffset,\n    initialClientOffset,\n    initialSourceClientOffset\n  } = state;\n  if (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n    return null;\n  }\n  return subtract(add(clientOffset, initialSourceClientOffset), initialClientOffset);\n}\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */\nexport function getDifferenceFromInitialOffset(state) {\n  const {\n    clientOffset,\n    initialClientOffset\n  } = state;\n  if (!clientOffset || !initialClientOffset) {\n    return null;\n  }\n  return subtract(clientOffset, initialClientOffset);\n}", "map": {"version": 3, "names": ["add", "a", "b", "x", "y", "subtract", "getSourceClientOffset", "state", "clientOffset", "initialClientOffset", "initialSourceClientOffset", "getDifferenceFromInitialOffset"], "sources": ["C:\\Users\\<USER>\\PM\\agnoworksphere\\node_modules\\dnd-core\\src\\utils\\coords.ts"], "sourcesContent": ["import type { XYCoord } from '../interfaces.js'\nimport type { State } from '../reducers/dragOffset.js'\n\n/**\n * Coordinate addition\n * @param a The first coordinate\n * @param b The second coordinate\n */\nexport function add(a: XYCoord, b: XYCoord): XYCoord {\n\treturn {\n\t\tx: a.x + b.x,\n\t\ty: a.y + b.y,\n\t}\n}\n\n/**\n * Coordinate subtraction\n * @param a The first coordinate\n * @param b The second coordinate\n */\nexport function subtract(a: XYCoord, b: XYCoord): XYCoord {\n\treturn {\n\t\tx: a.x - b.x,\n\t\ty: a.y - b.y,\n\t}\n}\n\n/**\n * Returns the cartesian distance of the drag source component's position, based on its position\n * at the time when the current drag operation has started, and the movement difference.\n *\n * Returns null if no item is being dragged.\n *\n * @param state The offset state to compute from\n */\nexport function getSourceClientOffset(state: State): XYCoord | null {\n\tconst { clientOffset, initialClientOffset, initialSourceClientOffset } = state\n\tif (!clientOffset || !initialClientOffset || !initialSourceClientOffset) {\n\t\treturn null\n\t}\n\treturn subtract(\n\t\tadd(clientOffset, initialSourceClientOffset),\n\t\tinitialClientOffset,\n\t)\n}\n\n/**\n * Determines the x,y offset between the client offset and the initial client offset\n *\n * @param state The offset state to compute from\n */\nexport function getDifferenceFromInitialOffset(state: State): XYCoord | null {\n\tconst { clientOffset, initialClientOffset } = state\n\tif (!clientOffset || !initialClientOffset) {\n\t\treturn null\n\t}\n\treturn subtract(clientOffset, initialClientOffset)\n}\n"], "mappings": "AAGA;;;;GAKA,OAAO,SAASA,GAAGA,CAACC,CAAU,EAAEC,CAAU,EAAW;EACpD,OAAO;IACNC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAGD,CAAC,CAACC,CAAC;IACZC,CAAC,EAAEH,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE;GACX;;AAGF;;;;;AAKA,OAAO,SAASC,QAAQA,CAACJ,CAAU,EAAEC,CAAU,EAAW;EACzD,OAAO;IACNC,CAAC,EAAEF,CAAC,CAACE,CAAC,GAAGD,CAAC,CAACC,CAAC;IACZC,CAAC,EAAEH,CAAC,CAACG,CAAC,GAAGF,CAAC,CAACE;GACX;;AAGF;;;;;;;;AAQA,OAAO,SAASE,qBAAqBA,CAACC,KAAY,EAAkB;EACnE,MAAM;IAAEC,YAAY;IAAEC,mBAAmB;IAAEC;EAAyB,CAAE,GAAGH,KAAK;EAC9E,IAAI,CAACC,YAAY,IAAI,CAACC,mBAAmB,IAAI,CAACC,yBAAyB,EAAE;IACxE,OAAO,IAAI;;EAEZ,OAAOL,QAAQ,CACdL,GAAG,CAACQ,YAAY,EAAEE,yBAAyB,CAAC,EAC5CD,mBAAmB,CACnB;;AAGF;;;;;AAKA,OAAO,SAASE,8BAA8BA,CAACJ,KAAY,EAAkB;EAC5E,MAAM;IAAEC,YAAY;IAAEC;EAAmB,CAAE,GAAGF,KAAK;EACnD,IAAI,CAACC,YAAY,IAAI,CAACC,mBAAmB,EAAE;IAC1C,OAAO,IAAI;;EAEZ,OAAOJ,QAAQ,CAACG,YAAY,EAAEC,mBAAmB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}